# Accept-Language 头部实现文档

本文档详细说明了如何在Flutter费用追踪应用中实现自动添加 `Accept-Language` 头部的功能。

## 🎯 实现目标

- ✅ 自动为所有API请求添加 `Accept-Language` 头部
- ✅ 根据用户当前设置的语言动态生成头部值
- ✅ 遵循HTTP标准的 Accept-Language 格式
- ✅ 支持语言优先级和备选语言
- ✅ 与现有的多语言系统无缝集成

## 🏗️ 架构设计

### 核心组件

1. **LocaleInterceptor** - Dio拦截器，负责添加语言头部
2. **LocaleProvider** - 语言状态管理，提供当前语言设置
3. **ApiConstants** - API常量，定义头部键名

### 工作流程

```
用户切换语言 → LocaleProvider更新状态 → LocaleInterceptor读取当前语言 → 转换为Accept-Language格式 → 添加到请求头部
```

## 📁 新增文件

### 1. 语言拦截器
**文件**: `lib/core/network/interceptors/locale_interceptor.dart`

```dart
class LocaleInterceptor extends Interceptor {
  final Ref ref;
  
  LocaleInterceptor(this.ref);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final currentLocale = ref.read(localeProvider);
    final acceptLanguage = _convertToAcceptLanguage(currentLocale);
    options.headers[ApiConstants.acceptLanguageHeader] = acceptLanguage;
    handler.next(options);
  }
}
```

### 2. 测试文件
- `test/core/network/interceptors/locale_interceptor_test.dart` - 单元测试
- `test/core/network/interceptors/locale_interceptor_integration_test.dart` - 集成测试

## 🔧 修改文件

### 1. Dio配置
**文件**: `lib/core/network/dio_provider.dart`

添加了语言拦截器到Dio实例：
```dart
dio.interceptors.add(LocaleInterceptor(ref)); // 语言拦截器
```

### 2. API常量
**文件**: `lib/core/constants/api_constants.dart`

添加了Accept-Language头部常量：
```dart
static const String acceptLanguageHeader = "Accept-Language";
```

## 🌐 Accept-Language 格式

### 标准格式说明

Accept-Language头部遵循RFC 7231标准，格式为：
```
Accept-Language: language-tag[;q=weight][,language-tag[;q=weight]]*
```

### 应用中的实现

| 用户设置语言 | Accept-Language 头部值 | 说明 |
|-------------|----------------------|------|
| 中文 (zh_CN) | `zh-CN,zh;q=0.9,en;q=0.8` | 中文优先，英文备选 |
| 英文 (en) | `en-US,en;q=0.9,zh;q=0.8` | 英文优先，中文备选 |
| 其他/未知 | `zh-CN,zh;q=0.9,en;q=0.8` | 默认回退到中文 |

### 权重说明

- `q=1.0` (默认) - 最高优先级
- `q=0.9` - 高优先级
- `q=0.8` - 中等优先级

## 🔄 拦截器执行顺序

在Dio配置中，拦截器按以下顺序执行：

1. **LoggingInterceptor** - 日志记录
2. **LocaleInterceptor** - 语言头部 ⭐ 新增
3. **AuthInterceptor** - 认证头部
4. **ErrorInterceptor** - 错误处理
5. **BusinessInterceptor** - 业务逻辑

## 📊 使用示例

### 自动添加头部

当用户设置语言为中文时，所有API请求会自动包含：
```http
GET /api/transactions HTTP/1.1
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Authorization: Bearer xxx
Content-Type: application/json
```

### 后端处理建议

后端可以根据Accept-Language头部返回对应语言的内容：

```javascript
// Node.js Express 示例
app.get('/api/transactions', (req, res) => {
  const acceptLanguage = req.headers['accept-language'];
  const preferredLanguage = parseAcceptLanguage(acceptLanguage);
  
  // 根据语言返回本地化内容
  if (preferredLanguage.startsWith('zh')) {
    return res.json({ message: '交易列表', data: transactions });
  } else {
    return res.json({ message: 'Transaction List', data: transactions });
  }
});
```

## 🧪 测试覆盖

### 单元测试
- ✅ 中文语言转换测试
- ✅ 英文语言转换测试
- ✅ 未知语言回退测试
- ✅ 空值处理测试

### 集成测试
- ✅ 实际请求头部验证
- ✅ 多请求一致性测试
- ✅ 语言切换动态更新测试

### 运行测试
```bash
# 运行单元测试
flutter test test/core/network/interceptors/locale_interceptor_test.dart

# 运行集成测试
flutter test test/core/network/interceptors/locale_interceptor_integration_test.dart
```

## 🔍 调试和监控

### 日志输出

拦截器会输出详细的日志信息：
```
LocaleInterceptor: Added Accept-Language header: zh-CN,zh;q=0.9,en;q=0.8 for path: /api/transactions
```

### 网络调试

可以通过网络调试工具查看实际发送的头部：
- Chrome DevTools
- Charles Proxy
- Wireshark

## 🚀 最佳实践

### 1. 语言代码标准化
- 使用标准的语言代码（zh-CN, en-US）
- 保持与后端约定的一致性

### 2. 权重设置
- 主语言权重设为1.0（默认）
- 备选语言权重递减（0.9, 0.8, 0.7...）

### 3. 错误处理
- 未知语言自动回退到默认语言
- 记录异常情况的日志

### 4. 性能考虑
- 拦截器逻辑简单高效
- 避免在拦截器中进行异步操作

## 🔮 未来扩展

### 计划中的功能
1. **更多语言支持**: 日语、韩语、法语等
2. **地区特定格式**: zh-TW, en-GB, fr-CA等
3. **动态权重**: 根据用户使用习惯调整权重
4. **缓存优化**: 缓存转换结果提高性能

### 扩展新语言
```dart
String _convertToAcceptLanguage(String locale) {
  switch (locale) {
    case 'zh_CN':
      return 'zh-CN,zh;q=0.9,en;q=0.8';
    case 'en':
      return 'en-US,en;q=0.9,zh;q=0.8';
    case 'ja': // 新增日语支持
      return 'ja-JP,ja;q=0.9,en;q=0.8,zh;q=0.7';
    default:
      return 'zh-CN,zh;q=0.9,en;q=0.8';
  }
}
```

## 📞 技术支持

如果在使用Accept-Language功能时遇到问题：

1. **检查拦截器配置**: 确认LocaleInterceptor已正确添加到Dio
2. **验证语言设置**: 确认用户语言设置正确
3. **查看网络日志**: 检查请求头部是否包含Accept-Language
4. **运行测试**: 执行相关测试确认功能正常

---

**注意**: 此功能需要后端配合处理Accept-Language头部，以实现完整的国际化体验。
