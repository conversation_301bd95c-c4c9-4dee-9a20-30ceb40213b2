# 工具组件选择性编辑功能实现

## 🎯 设计理念

基于实际业务需求，只有**创建交易工具**需要编辑功能，其他工具组件（图表、表格等）仅用于展示。

## 📊 组件分类

### 可编辑组件
- **TransactionReceiptCard**: 交易收据卡片
  - 包含 `transactionId` 字段
  - 支持编辑、删除操作
  - 在流式响应和历史记录中都可编辑

### 只读组件
- **图表组件**: PieChart, LineChart, BarChart, RadarChart
- **数据表格**: DataTable
- **摘要卡片**: SummaryCard
- **列表卡片**: ListCard
- **图片卡片**: ImageCard

## 🔧 技术实现

### 1. 模型调整

```dart
@freezed
class ToolUIComponent with _$ToolUIComponent {
  const factory ToolUIComponent({
    String? id, // 可选，只有需要编辑的组件才有ID
    required ToolUIComponentType type,
    required String name,
    required Map<String, dynamic> props,
    @Default({}) Map<String, dynamic> metadata,
    String? transactionId, // 交易ID，用于交易组件的编辑
  }) = _ToolUIComponent;
}
```

### 2. 组件创建逻辑

```dart
factory ToolUIComponent.fromComponentData(Map<String, dynamic> componentData) {
  final name = componentData['name'] as String? ?? 'UnknownComponent';
  final props = componentData['props'] as Map<String, dynamic>? ?? {};
  
  // 检查是否是交易组件
  String? transactionId;
  String? componentId;
  
  if (name.toLowerCase().contains('transaction') || name.toLowerCase().contains('receipt')) {
    transactionId = props['transactionId'] as String?;
    // 交易组件需要ID用于编辑
    componentId = transactionId != null ? 'transaction-$transactionId' : null;
  }
  // 其他组件不需要ID（纯展示用）
  
  return ToolUIComponent(
    id: componentId, // 只有交易组件有ID
    type: type,
    name: name,
    props: props,
    transactionId: transactionId,
  );
}
```

### 3. UI渲染增强

**参考交易详情页设计风格**：

```dart
Widget _buildTransactionReceiptCard(BuildContext context, ToolUIComponent component) {
  final theme = ShadTheme.of(context);
  final colorScheme = theme.colorScheme;
  final props = TransactionReceiptCardProps.fromJson(component.props);
  final hasEditCapability = component.transactionId != null;

  return ShadCard(
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 顶部：类别图标、类别名称、时间、操作按钮
        Row(
          children: [
            ShadAvatar(iconUrl, radius: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(props.category, style: theme.textTheme.p),
                  Text(props.date, style: theme.textTheme.small),
                ],
              ),
            ),
            if (hasEditCapability)
              ShadIconButton.ghost(
                icon: Icon(LucideIcons.moreHorizontal),
                onPressed: () => _showTransactionActions(context, transactionId),
              ),
          ],
        ),

        // 金额和描述
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(props.description, style: theme.textTheme.h4),
                  Text(props.paymentMethod, style: theme.textTheme.small),
                ],
              ),
            ),
            Text(
              '${props.type == 'income' ? '+' : '-'}¥${props.amount}',
              style: theme.textTheme.h3.copyWith(
                color: props.type == 'income' ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),

        // 查看详情提示
        if (hasEditCapability)
          GestureDetector(
            onTap: () => _navigateToTransactionDetail(context, transactionId),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: colorScheme.muted.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(LucideIcons.eye, size: 14),
                  SizedBox(width: 6),
                  Text('查看详情', style: theme.textTheme.small),
                ],
              ),
            ),
          ),
      ],
    ),
  );
}
```

**操作菜单**：
- 查看详情 → 重定向到交易详情页
- 编辑交易 → 重定向到交易编辑页
- 删除交易 → 确认对话框

## 📝 数据格式

### SSE 流式响应（保持原格式）

**创建交易工具**:
```json
{
  "type": "tool_result",
  "tool_name": "create_transaction",
  "componentForUi": {
    "name": "TransactionReceiptCard",
    "props": {
      "transactionId": "12345",
      "amount": 150.00,
      "category": "餐饮",
      "description": "午餐",
      "date": "2025-07-02",
      "type": "expense",
      "paymentMethod": "支付宝"
    }
  }
}
```

**其他工具**:
```json
{
  "type": "tool_result",
  "tool_name": "get_spending_by_category",
  "componentForUi": {
    "name": "PieChart",
    "props": {
      "title": "消费分类占比",
      "total": 5974,
      "unit": "元",
      "sections": [...]
    }
  }
}
```

### 历史记录响应

```json
{
  "toolComponents": [
    {
      "id": "transaction-12345",
      "name": "TransactionReceiptCard",
      "type": "transactionReceiptCard",
      "transactionId": "12345",
      "props": {
        "amount": 150.00,
        "category": "餐饮",
        "description": "午餐"
      }
    },
    {
      "name": "PieChart",
      "type": "chartCard",
      "props": {
        "title": "消费分类占比",
        "sections": [...]
      }
    }
  ]
}
```

## ✅ 实现优势

1. **精准功能定位**: 只有需要编辑的组件才有编辑功能
2. **性能优化**: 减少不必要的ID生成和管理
3. **代码简洁**: 避免过度设计，保持代码清晰
4. **用户体验**: 编辑按钮只在有意义的地方出现
5. **向后兼容**: 保持SSE原有格式，减少后端改动

## 🔄 组件生命周期

### 流式响应时
1. 解析 `componentForUi` 数据
2. 检查组件类型，提取 `transactionId`
3. 生成对应的组件ID（如果需要）
4. 渲染组件，显示编辑按钮（如果可编辑）

### 历史记录时
1. 解析 `toolComponents` 数据
2. 直接使用后端提供的ID和transactionId
3. 渲染组件，保持编辑功能

## 🎯 关键设计决策

1. **ID的实际意义**: 只有需要后续操作的组件才有ID
2. **功能分离**: 展示组件和交互组件明确区分
3. **格式兼容**: 保持SSE原有格式，减少系统改动
4. **渐进增强**: 可以根据需要扩展更多可编辑组件

这种设计既满足了实际业务需求，又保持了系统的简洁性和可维护性。
