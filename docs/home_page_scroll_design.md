# 首页优雅滑动固定点设计

## 概述

为了解决首页滑动时页面元素遮挡系统栏的问题，我们设计了一个智能的滑动固定点系统，提供三个优雅的滑动状态，确保用户体验的流畅性和内容的可访问性。

## 设计理念

### 三个滑动固定点

1. **完全展开状态 (Expanded)**
   - 显示完整的预算区域
   - 包含预算金额、眼睛图标和操作按钮
   - 适合用户查看详细预算信息

2. **半收起状态 (Collapsed)**
   - 预算区域部分收起
   - 在标题栏显示简化的预算信息
   - 平衡了信息展示和空间利用

3. **最小化状态 (Minimal)**
   - 预算区域最小化显示
   - 最大化日历和交易数据流的显示空间
   - 适合专注于交易数据的场景

### 智能滑动逻辑

- **自动吸附**：滑动结束时自动滑动到最近的固定点
- **阈值控制**：只有滑动距离超过30px时才触发自动吸附
- **平滑动画**：使用300ms的缓动动画，提供流畅的视觉体验

## 技术实现

### 核心组件

1. **SmartScrollController**
   - 继承自Flutter的ScrollController
   - 提供智能滑动到固定点的功能
   - 支持自定义滑动位置、动画时长和曲线

2. **ScrollStateManager**
   - 管理当前滑动状态
   - 监听滑动进度变化
   - 通知UI组件更新显示

3. **ScrollIndicator**
   - 可视化显示当前滑动状态
   - 支持点击切换状态
   - 提供用户友好的交互反馈

### 动画效果

- **渐变动画**：按钮区域根据滑动状态淡入淡出
- **透明度动画**：标题栏内容根据状态显示/隐藏
- **高度动画**：容器高度平滑变化
- **位移动画**：滑动指导图标的上下移动

## 用户体验优化

### 防遮挡设计

1. **底部安全区域**
   - 添加底部padding，防止内容被系统导航栏遮挡
   - 动态计算安全区域高度

2. **顶部状态栏适配**
   - 使用SafeArea确保内容不被状态栏遮挡
   - 智能调整内容布局

### 交互指导

1. **滑动手势指导**
   - 首次进入时显示动画指导
   - 3秒后自动隐藏或用户滑动后隐藏

2. **状态提示卡片**
   - 根据当前状态显示相应提示
   - 用户可手动关闭提示

3. **视觉反馈**
   - 滑动指示器显示当前状态
   - 点击指示器可循环切换状态

## 配置参数

### 滑动固定点高度
```dart
static const double _expandedHeight = 240.0;   // 完全展开高度
static const double _collapsedHeight = 120.0;  // 半收起高度
static const double _minimalHeight = 80.0;     // 最小化高度
```

### 动画参数
```dart
snapDuration: Duration(milliseconds: 300)     // 滑动动画时长
snapCurve: Curves.easeOutCubic               // 滑动动画曲线
snapThreshold: 30.0                          // 触发自动滑动的阈值
```

## 使用场景

### 适用情况
- 需要在有限空间内展示多层信息
- 用户需要在不同信息密度间切换
- 希望提供流畅的滑动体验

### 最佳实践
1. 确保每个固定点都有明确的使用场景
2. 提供清晰的视觉反馈和状态指示
3. 保持动画的一致性和流畅性
4. 考虑不同屏幕尺寸的适配

## 扩展性

### 自定义固定点
可以通过修改`snapPositions`数组来添加更多固定点：

```dart
snapPositions: [
  0.0,                              // 完全展开
  _expandedHeight - _collapsedHeight, // 半收起
  _expandedHeight - _minimalHeight,   // 最小化
  _expandedHeight,                    // 完全收起（可选）
]
```

### 自定义动画
可以通过修改动画参数来调整滑动体验：

```dart
SmartScrollController(
  snapDuration: Duration(milliseconds: 500),  // 更慢的动画
  snapCurve: Curves.bounceOut,                // 弹跳效果
  snapThreshold: 50.0,                        // 更大的触发阈值
)
```

## 总结

这个优雅的滑动固定点系统解决了原有的页面元素遮挡问题，同时提供了更好的用户体验。通过智能的状态管理、流畅的动画效果和友好的交互指导，用户可以轻松地在不同的信息展示模式间切换，既保证了信息的完整性，又优化了空间的利用效率。
