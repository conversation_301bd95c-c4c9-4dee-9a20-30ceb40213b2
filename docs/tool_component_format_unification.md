# 工具组件数据格式统一方案

## 🎯 目标

统一流式响应（SSE）和历史记录加载时的工具组件数据格式，提高系统一致性和可维护性。

## 📊 当前格式对比

### 流式响应格式（SSE）
```json
{
  "type": "tool_result",
  "tool_name": "createGetSpendingByCategoryTool",
  "component_for_ui": {
    "name": "PieChart",
    "props": {
      "title": "消费分类占比",
      "total": 5974,
      "unit": "元",
      "sections": [...]
    }
  }
}
```

### 历史记录格式
```json
{
  "toolComponents": [
    {
      "id": "widget-bf031f",
      "name": "PieChart", 
      "type": "chartCard",
      "props": {
        "title": "消费分类占比",
        "total": 5974,
        "unit": "元",
        "sections": [...]
      }
    }
  ]
}
```

## 🚀 推荐统一方案

### 方案：统一为完整格式

**优势**：
- 数据结构完整，包含所有必要字段
- 便于前端组件类型映射和渲染
- 支持更丰富的元数据
- 向后兼容性好

## 📝 后端接口调整

### 1. SSE 流式响应调整

**当前格式**：
```json
{
  "type": "tool_result",
  "tool_name": "createGetSpendingByCategoryTool",
  "component_for_ui": {
    "name": "PieChart",
    "props": {...}
  }
}
```

**调整后格式**：
```json
{
  "type": "tool_result",
  "tool_name": "createGetSpendingByCategoryTool",
  "tool_component": {
    "id": "widget-bf031f",
    "name": "PieChart",
    "type": "chartCard",
    "props": {...}
  }
}
```

### 2. 历史记录API保持不变

```json
{
  "toolComponents": [
    {
      "id": "widget-bf031f",
      "name": "PieChart", 
      "type": "chartCard",
      "props": {...}
    }
  ]
}
```

## 🔧 实施步骤

### 阶段1：兼容性支持（已完成）
- ✅ 前端同时支持新旧两种格式
- ✅ 智能检测数据格式并正确解析
- ✅ 保持现有功能正常运行

### 阶段2：后端调整
1. **修改 SSE 响应格式**：
   ```php
   // 当前
   return [
       'type' => 'tool_result',
       'component_for_ui' => [
           'name' => 'PieChart',
           'props' => $chartData
       ]
   ];
   
   // 调整后
   return [
       'type' => 'tool_result', 
       'tool_component' => [
           'id' => 'widget-' . uniqid(),
           'name' => 'PieChart',
           'type' => 'chartCard',
           'props' => $chartData
       ]
   ];
   ```

2. **组件类型映射**：
   ```php
   private function getComponentType(string $name): string {
       $typeMap = [
           'PieChart' => 'chartCard',
           'LineChart' => 'chartCard', 
           'BarChart' => 'chartCard',
           'RadarChart' => 'chartCard',
           'DataTable' => 'dataTable',
           'TransactionReceiptCard' => 'transactionReceiptCard',
           // ...
       ];
       
       return $typeMap[$name] ?? 'customCard';
   }
   ```

### 阶段3：清理旧格式支持
- 移除 `component_for_ui` 兼容代码
- 统一使用 `tool_component` 格式

## 📋 字段说明

### 必需字段
- `id`: 组件唯一标识符
- `name`: 组件名称（如 PieChart, LineChart）
- `type`: 组件类型枚举（如 chartCard, dataTable）
- `props`: 组件属性数据

### 可选字段
- `metadata`: 额外元数据

## 🎨 支持的组件类型

| 组件名称 | 类型枚举 | 说明 |
|---------|---------|------|
| PieChart | chartCard | 饼图 |
| LineChart | chartCard | 折线图 |
| BarChart | chartCard | 条形图 |
| RadarChart | chartCard | 雷达图 |
| DataTable | dataTable | 数据表格 |
| TransactionReceiptCard | transactionReceiptCard | 交易收据卡片 |
| SummaryCard | summaryCard | 摘要卡片 |

## 🔄 迁移时间线

1. **立即**：前端兼容性支持已完成
2. **1周内**：后端调整 SSE 响应格式
3. **2周内**：验证新格式正常工作
4. **1个月后**：移除旧格式兼容代码

## ✅ 验证清单

- [ ] SSE 响应使用新的 `tool_component` 格式
- [ ] 历史记录加载正常显示工具组件
- [ ] 所有图表类型正确渲染
- [ ] 新旧格式兼容性测试通过
- [ ] 性能无明显影响

## 📞 联系方式

如有问题或建议，请联系前端开发团队。
