// shared/config/category_config.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '/shared/l10n/app_strings.dart';

/// 分类配置管理类
/// 负责管理分类ID、名称、图标的映射关系，支持多语言扩展
class CategoryConfig {
  // 分类ID到中文名称的映射
  static const Map<String, String> _categoryNames = {
    '1': '日常消费',
    '2': '交通出行',
    '3': '医疗健康',
    '4': '住房物业',
    '5': '教育培训',
    '6': '收入进账',
    '7': '社交馈赠',
    '8': '资金周转',
  };

  // 分类名称到图标的映射
  static const Map<String, IconData> _categoryIcons = {
    '日常消费': FIcons.shoppingBag,
    '交通出行': FIcons.carTaxiFront,
    '医疗健康': FIcons.briefcaseMedical,
    '住房物业': FIcons.housePlus,
    '教育培训': FIcons.libraryBig,
    '收入进账': FIcons.walletCards,
    '社交馈赠': FIcons.gift,
    '资金周转': FIcons.folderSync,
  };

  // 分类ID到图标的直接映射（优化性能）
  static const Map<String, IconData> _categoryIdToIcon = {
    '1': FIcons.shoppingBag,
    '2': FIcons.carTaxiFront,
    '3': FIcons.briefcaseMedical,
    '4': FIcons.housePlus,
    '5': FIcons.libraryBig,
    '6': FIcons.walletCards,
    '7': FIcons.gift,
    '8': FIcons.folderSync,
  };

  /// 根据分类ID获取分类名称
  static String getCategoryName(String? categoryId, [String? locale]) {
    return AppStrings.getCategoryName(categoryId, locale);
  }

  /// 根据分类ID获取分类图标
  static IconData getCategoryIcon(String? categoryId) {
    if (categoryId == null) return FIcons.shoppingBag;
    return _categoryIdToIcon[categoryId] ?? FIcons.shoppingBag;
  }

  /// 根据分类名称获取分类图标（向后兼容）
  static IconData getCategoryIconByName(String categoryName) {
    return _categoryIcons[categoryName] ?? FIcons.shoppingBag;
  }

  /// 获取所有分类列表
  static List<CategoryItem> getAllCategories([String? locale]) {
    return _categoryNames.entries.map((entry) {
      return CategoryItem(
        id: entry.key,
        name: AppStrings.getCategoryName(entry.key, locale),
        icon: _categoryIdToIcon[entry.key]!,
      );
    }).toList();
  }

  /// 检查分类ID是否有效
  static bool isValidCategoryId(String? categoryId) {
    return categoryId != null && _categoryNames.containsKey(categoryId);
  }
}

/// 分类项目数据类
class CategoryItem {
  final String id;
  final String name;
  final IconData icon;

  const CategoryItem({
    required this.id,
    required this.name,
    required this.icon,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CategoryItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'CategoryItem(id: $id, name: $name)';
}

/// 多语言支持扩展（为未来准备）
class CategoryConfigI18n {
  // 英文分类名称映射（示例）
  static const Map<String, String> _categoryNamesEn = {
    '1': 'Daily Expenses',
    '2': 'Transportation',
    '3': 'Healthcare',
    '4': 'Housing',
    '5': 'Education',
    '6': 'Income',
    '7': 'Social Gifts',
    '8': 'Money Transfer',
  };

  /// 根据语言代码获取分类名称
  static String getCategoryName(String? categoryId, [String locale = 'zh_CN']) {
    if (categoryId == null) {
      return locale == 'en' ? 'Other' : '其他';
    }

    switch (locale) {
      case 'en':
        return _categoryNamesEn[categoryId] ?? 'Other';
      case 'zh_CN':
      default:
        return CategoryConfig._categoryNames[categoryId] ?? '其他';
    }
  }

  /// 获取本地化的分类列表
  static List<CategoryItem> getAllCategories([String locale = 'zh_CN']) {
    final nameMap = locale == 'en' ? _categoryNamesEn : CategoryConfig._categoryNames;
    
    return nameMap.entries.map((entry) {
      return CategoryItem(
        id: entry.key,
        name: entry.value,
        icon: CategoryConfig._categoryIdToIcon[entry.key]!,
      );
    }).toList();
  }
}
