// shared/models/action_item_model.dart (或类似位置)
import 'package:flutter/material.dart';

class ActionItem {
  final String title;
  final IconData icon;
  final VoidCallback onTap;
  final Color? color; // 如果确实需要覆盖主题颜色
  final bool isDestructive; // 新增

  ActionItem({
    required this.title,
    required this.icon,
    required this.onTap,
    this.color,
    this.isDestructive = false, // 默认为 false
  });
}

