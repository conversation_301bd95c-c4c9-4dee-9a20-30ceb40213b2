// shared/services/locale_service.dart
import 'package:shared_preferences/shared_preferences.dart';
import '../l10n/app_strings.dart';

/// 语言管理服务
/// 负责语言设置的持久化存储和管理
class LocaleService {
  static const String _localeKey = 'app_locale';
  static const String _defaultLocale = 'zh_CN';
  
  /// 支持的语言列表
  static const List<String> supportedLocales = ['zh_CN', 'en'];
  
  /// 语言显示名称映射
  static const Map<String, String> localeDisplayNames = {
    'zh_CN': '中文',
    'en': 'English',
  };
  
  /// 获取当前保存的语言设置
  static Future<String> getSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLocale = prefs.getString(_localeKey);
      
      // 验证保存的语言是否在支持列表中
      if (savedLocale != null && supportedLocales.contains(savedLocale)) {
        return savedLocale;
      }
      
      return _defaultLocale;
    } catch (e) {
      return _defaultLocale;
    }
  }
  
  /// 保存语言设置
  static Future<bool> saveLocale(String locale) async {
    try {
      if (!supportedLocales.contains(locale)) {
        throw ArgumentError('Unsupported locale: $locale');
      }
      
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.setString(_localeKey, locale);
      
      if (success) {
        // 同时更新AppStrings的当前语言
        AppStrings.setLocale(locale);
      }
      
      return success;
    } catch (e) {
      return false;
    }
  }
  
  /// 获取语言显示名称
  static String getLocaleDisplayName(String locale) {
    return localeDisplayNames[locale] ?? locale;
  }
  
  /// 检查是否为支持的语言
  static bool isSupportedLocale(String locale) {
    return supportedLocales.contains(locale);
  }
  
  /// 获取系统默认语言（如果支持的话）
  static String getSystemLocale() {
    // 这里可以根据系统语言返回对应的locale
    // 目前简化处理，返回默认语言
    return _defaultLocale;
  }
  
  /// 初始化语言服务
  static Future<void> initialize() async {
    final savedLocale = await getSavedLocale();
    AppStrings.setLocale(savedLocale);
  }
}
