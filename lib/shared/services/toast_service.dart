// shared/services/toast_service.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

// 导入您定义的全局 navigatorKey
import '../../app/router/app_router.dart';

class ToastService {
  ToastService._(); // 私有构造函数

  static final BuildContext? _context = navigatorKey.currentContext;

  /// 显示一个标准的 Toast
  static void show({
    required Widget description,
    Widget? title,
    Widget? action,
    Duration? duration,
    Alignment? alignment,
  }) {
    if (_context == null) {
      print("ToastService Error: BuildContext is not available.");
      return;
    }
    showFToast(
      context: _context!,
      title: title ?? description,
      description: title != null ? description : null,
      duration: duration,
    );
  }

  /// 显示一个破坏性的 (错误) Toast
  static void showDestructive({
    required Widget description,
    Widget? title,
    Widget? action,
    Duration? duration,
    Alignment? alignment,
  }) {
    if (_context == null) {
      print("ToastService Error: BuildContext is not available.");
      return;
    }
    showFToast(
      context: _context!,
      icon: const Icon(FIcons.triangleAlert),
      title: title ?? description,
      description: title != null ? description : null,
      duration: duration,
    );
  }

  /// 隐藏当前显示的 Toast
  /// 注意：Forui 的 Toast 会自动隐藏，此方法保留以兼容现有代码
  static void hide() {
    if (_context == null) return;
    // Forui Toast 会自动隐藏，无需手动调用
    // 如果需要手动控制，可以使用 showRawFToast 的 builder 返回的 entry.dismiss()
  }
}