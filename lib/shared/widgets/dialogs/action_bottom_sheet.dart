// shared/widgets/dialogs/action_bottom_sheet.dart
import 'package:flutter/material.dart';
import '../../models/action_item_model.dart';
import 'package:forui/forui.dart'; // 1. 引入 forui


class ActionBottomSheet extends StatelessWidget {
  final List<ActionItem> actions;
  final List<ActionItem>? destructiveActions; // 分开处理警示性操作

  const ActionBottomSheet({
    super.key,
    required this.actions,
    this.destructiveActions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme; // 2. 获取 Forui 主题
    final colors = theme.colors;

    // 3. 从主题获取颜色和样式 - 修复颜色配置
    final Color sheetBackgroundColor = colors.background; // 使用背景色作为底部表单背景
    final Color sectionBackgroundColor = colors.background;  // 使用背景色作为分段背景
    final Color itemTextColor = colors.foreground; // 使用前景色确保文字清晰可见
    final Color destructiveItemColor = colors.destructive; // 破坏性操作用 destructive 颜色
    final Color iconColor = colors.mutedForeground; // 图标使用柔和的前景色
    final Color dividerColor = colors.border;
    final Color handleColor = colors.mutedForeground.withValues(alpha: 0.4); // 拖拽柄颜色

    Widget buildActionItem(ActionItem item, BuildContext sheetContext, {bool isDestructive = false}) {

      return Container(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(sheetContext);
            item.onTap();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    item.title,
                    style: theme.typography.base.copyWith(
                      color: isDestructive ? destructiveItemColor : (item.color ?? itemTextColor),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  item.icon,
                  color: isDestructive ? destructiveItemColor : (item.color ?? iconColor),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      );
    }

    Widget buildActionSection(List<ActionItem> items, {bool isDestructive = false}) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16.0),
        decoration: BoxDecoration(
          color: sectionBackgroundColor,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: dividerColor,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            for (int i = 0; i < items.length; i++) ...[
              buildActionItem(items[i], context, isDestructive: isDestructive),
              if (i < items.length - 1)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0),
                  height: 1,
                  color: dividerColor,
                ),
            ],
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: sheetBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24.0),
          topRight: Radius.circular(24.0),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: handleColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 主要操作项区域
            if (actions.isNotEmpty) ...[
              buildActionSection(actions),
              const SizedBox(height: 8.0),
            ],

            // 警示性操作项区域
            if (destructiveActions != null && destructiveActions!.isNotEmpty) ...[
              buildActionSection(destructiveActions!, isDestructive: true),
              const SizedBox(height: 8.0),
            ],

            // 底部间距
            const SizedBox(height: 16.0),
          ],
        ),
      ),
    );
  }
}