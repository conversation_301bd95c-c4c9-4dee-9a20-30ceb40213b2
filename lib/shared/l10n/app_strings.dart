// shared/l10n/app_strings.dart
/// 应用字符串管理类
/// 为未来的多语言支持做准备，目前主要支持中文
class AppStrings {
  // 私有构造函数，防止实例化
  AppStrings._();

  // 当前语言代码，默认为中文
  static String _currentLocale = 'zh_CN';

  /// 设置当前语言
  static void setLocale(String locale) {
    _currentLocale = locale;
  }

  /// 获取当前语言
  static String get currentLocale => _currentLocale;

  // =============================================================================
  // 分类相关字符串
  // =============================================================================

  /// 分类名称映射
  static const Map<String, Map<String, String>> _categoryNames = {
    'zh_CN': {
      '1': '日常消费',
      '2': '交通出行',
      '3': '医疗健康',
      '4': '住房物业',
      '5': '教育培训',
      '6': '收入进账',
      '7': '社交馈赠',
      '8': '资金周转',
      'other': '其他',
    },
    'en': {
      '1': 'Daily Expenses',
      '2': 'Transportation',
      '3': 'Healthcare',
      '4': 'Housing',
      '5': 'Education',
      '6': 'Income',
      '7': 'Social Gifts',
      '8': 'Money Transfer',
      'other': 'Other',
    },
  };

  /// 获取分类名称
  static String getCategoryName(String? categoryId, [String? locale]) {
    final currentLoc = locale ?? _currentLocale;
    final categoryMap = _categoryNames[currentLoc] ?? _categoryNames['zh_CN']!;
    return categoryMap[categoryId ?? 'other'] ?? categoryMap['other']!;
  }

  // =============================================================================
  // 交易类型相关字符串
  // =============================================================================

  /// 交易类型名称映射
  static const Map<String, Map<String, String>> _transactionTypes = {
    'zh_CN': {
      'expense': '支出',
      'income': '收入',
    },
    'en': {
      'expense': 'Expense',
      'income': 'Income',
    },
  };

  /// 获取交易类型名称
  static String getTransactionTypeName(String type, [String? locale]) {
    final currentLoc = locale ?? _currentLocale;
    final typeMap = _transactionTypes[currentLoc] ?? _transactionTypes['zh_CN']!;
    return typeMap[type] ?? type;
  }

  // =============================================================================
  // 通用UI字符串
  // =============================================================================

  /// 通用UI字符串映射
  static const Map<String, Map<String, String>> _commonStrings = {
    'zh_CN': {
      'loading': '加载中...',
      'error': '错误',
      'retry': '重试',
      'cancel': '取消',
      'confirm': '确认',
      'save': '保存',
      'delete': '删除',
      'edit': '编辑',
      'add': '添加',
      'search': '搜索',
      'filter': '筛选',
      'sort': '排序',
      'refresh': '刷新',
      'more': '更多',
      'less': '收起',
      'all': '全部',
      'none': '无',
      'today': '今天',
      'yesterday': '昨天',
      'thisWeek': '本周',
      'thisMonth': '本月',
      'thisYear': '今年',
      'unknown': '未知',
      'noData': '暂无数据',
      'loadMore': '加载更多',
      'noMore': '没有更多了',

      // 导航相关
      'home': '首页',
      'forecast': '预测',
      'footprint': '足迹',
      'profile': '我的',

      // 问候语
      'goodMorning': '上午好',
      'goodAfternoon': '下午好',
      'goodEvening': '晚上好',

      // 用户信息
      'username': '用户名',
      'userEmail': '<EMAIL>',

      // 设置相关
      'settings': '设置',
      'language': '语言',
      'languageSettings': '语言设置',
      'theme': '主题',
      'darkMode': '深色模式',
      'lightMode': '浅色模式',
      'systemMode': '跟随系统',

      // 开发者选项
      'developerOptions': '开发者选项',
      'authDebug': '认证状态调试',
      'authDebugSubtitle': '查看认证状态和调试信息',
      'fontTest': '字体测试',
      'fontTestSubtitle': '测试应用字体显示效果',

      // 其他选项
      'helpAndFeedback': '帮助与反馈',
      'helpAndFeedbackSubtitle': '获取帮助或提供反馈',
      'aboutApp': '关于应用',
      'aboutAppSubtitle': '版本信息和开发者信息',

      // 语言选项
      'chinese': '中文',
      'english': 'English',
      'selectLanguage': '选择语言',
      'languageChanged': '语言已更改',
      'restartToApply': '重启应用以应用更改',

      // 认证相关
      'welcomeBack': '欢迎回来',
      'loginSuccess': '欢迎回来!',
      'loginFailed': '登录失败',
      'unknownError': '发生未知错误',
      'pleaseTryAgain': '请稍后重试。',
      'loginSubtitle': '登录以继续使用 AI 记账助理',
      'email': '邮箱',
      'emailPlaceholder': '请输入您的邮箱',
      'emailRequired': '邮箱不能为空',
      'emailInvalid': '请输入有效的邮箱地址',
      'password': '密码',
      'passwordPlaceholder': '请输入您的密码',
      'passwordRequired': '密码不能为空',
      'login': '登录',
      'loggingIn': '登录中...',
      'noAccount': '还没有账户？注册',

      // 字体测试页面
      'fontTestPage': '字体测试页面',
      'fontDisplayTest': '字体显示测试',
      'chineseTextTest': '中文文本测试',
      'englishTextTest': '英文文本测试',
      'fontTestSample1': '这是一段中文文本，用于测试字体显示效果。',
      'fontTestSample2': '支出分类汇总，购物最高',
      'fontTestSample3': '人工智能助手为您提供专业的财务分析服务',
      'fontTestSample4': '数据可视化图表展示您的消费趋势',
      'fontTestSample5': '微信支付、支付宝、银行卡等多种支付方式',

      // 预算相关
      'budgetSetup': '预算设置',
      'setBudgetAmount': '设置预算金额',
      'setBudgetAmountDesc': '为每个分类设置预算金额',
      'nextStep': '下一步',
      'monthlyBudget': '月度预算',
      'monthlyBudgetDesc': '按月管理您的支出，适合大多数人',
      'weeklyBudget': '周预算',
      'weeklyBudgetDesc': '按周管理支出，更精细的控制',
      'yearlyBudget': '年度预算',
      'yearlyBudgetDesc': '长期财务规划，适合大额支出管理',

      // 注册相关
      'createAccount': '创建您的账户',
      'setPassword': '设置密码',
      'setAccountPassword': '设置您的账户密码',
      'verificationCode': '验证码',
      'getVerificationCode': '获取验证码',
      'sending': '发送中...',
      'codeSent': '验证码已发送',
      'sendFailed': '发送失败',
      'pleaseEnterEmail': '请输入邮箱',
      'pleaseEnterEmailPlaceholder': '请输入邮箱...',
      'emailCannotBeEmpty': '邮箱不能为空',
      'pleaseEnterValidEmail': '请输入有效的邮箱',
      'pleaseEnterVerificationCode': '请输入验证码',
      'verificationCodeCannotBeEmpty': '验证码不能为空',
      'confirmPassword': '确认密码',
      'pleaseEnterPasswordAgain': '请再次输入您的密码',
      'pleaseEnterPasswordAgainShort': '请再次输入密码',
      'passwordCannotBeEmpty': '密码不能为空',
      'passwordTooShort': '密码长度不能少于6位',
      'passwordMustContainNumbersAndLetters': '密码必须包含数字和字母',
      'passwordsDoNotMatch': '两次输入的密码不一致',
      'registering': '注册中...',
      'completeRegistration': '完成注册',
      'registrationSuccess': '注册成功!',
      'registrationFailed': '注册失败',

      // 预测模块
      'financialNavigator': '你好，我是你的财务领航员',
      'financialMapSubtitle': '只需3步，我们一起绘制你未来的财务地图',
      'predictCashFlow': '预测未来现金流',
      'predictCashFlowDesc': '看清每一天的财务状况',
      'aiSmartSuggestions': 'AI智能建议',
      'aiSmartSuggestionsDesc': '个性化的财务决策指导',
      'riskWarning': '风险预警',
      'riskWarningDesc': '提前发现潜在的财务风险',
      'analyzingFinancialData': '我正在分析你的财务数据，生成未来30天的现金流预测',
      'analyzeIncomeExpensePattern': '分析收入支出模式',
      'calculateCashFlowTrend': '计算现金流趋势',
      'generateRiskWarning': '生成风险预警',
      'loadingFinancialForecast': '正在加载财务预测...',

      // 聊天模块
      'newChat': '新聊天',
      'noMessagesToDisplay': '没有消息可显示。',
      'loadingFailed': '加载失败',

      // 预算模块
      'budgetSettings': '预算设置',
      'editBudget': '编辑预算',
      'editBudgetDesc': '修改预算金额和分类',
      'reminderSettings': '提醒设置',
      'reminderSettingsDesc': '设置预算提醒和通知',
      'budgetReport': '预算报告',
      'budgetReportDesc': '查看详细的预算分析报告',
      'welcomeToBudget': '欢迎使用预算功能！',
      'createNewBudgetPlan': '创建新的预算计划',
      'budgetWelcomeDesc': '通过设置预算，您可以更好地控制支出，实现财务目标。让我们开始设置您的第一个预算计划吧！',
      'budgetCreateDesc': '为不同的支出类别设置预算限额，帮助您更好地管理财务。',

      // 足迹模块
      'searchIn': '搜索',
      'searchInAllRecords': '在所有记录中搜索相关内容',

      // 预测工具栏
      'forecastTitle': '预测',
      'forecastSubtitle': '基于您的财务数据智能预测未来现金流',

      // 聊天输入相关
      'inputMessage': '输入消息...',
      'listening': '正在聆听...',
      'aiThinking': 'AI思考中...',
      'speechNotRecognized': '未识别到语音，请重试',

      // 交易搜索相关
      'noTransactionsFound': '没有找到交易记录',
      'tryAdjustingSearch': '尝试调整搜索条件或创建新的交易记录',

      // 预测模块 - 日期和时间
      'todayLabel': '今日',
      'tomorrowLabel': '明日',
      'balanceLabel': '余额',
      'noSpecialEvents': '无特殊事件',
      'monthDayFormat': '月日', // 用于格式化 "X月X日"

      // 财务安全线页面
      'financialSafetyLine': '财务安全线',
      'currentSetting': '当前设置',
      'dailySpendingEstimate': '日常消费预估',
      'adjustDailySpendingAmount': '调整每日消费预测金额',
      'tellMeYourSafetyLine': '告诉我你的财务"安心线"是多少？',
      'safetyLineDescription': '这是你希望账户保持的最低余额，当余额接近这个数值时，我会提醒你注意财务风险。',

      // 消费设置步骤
      'dailySpendingQuestion': '每天的"小日子"大概花多少？',
      'dailySpendingDescription': '包括吃饭、交通、购物等日常开销\n这只是一个初始估算，我会通过你未来的真实记录，让预测越来越准',
      'perDay': '每天',
      'referenceStandard': '参考标准',
      'frugalType': '节俭型',
      'comfortableType': '舒适型',
      'relaxedType': '宽松型',
      'frugalAmount': '50-100元/天',
      'comfortableAmount': '100-200元/天',
      'relaxedAmount': '200-300元/天',
      'previousStep': '上一步',
      'completeMapping': '完成绘制',

      // 手动记账相关
      'expense': '支出',
      'income': '收入',
      'category': '分类',
      'dateTime': '日期时间',
      'description': '描述',
      'tags': '标签',
      'dayBeforeYesterday': '前天',
      'selectDate': '选择日期',
      'selectTime': '选择时间',
      'addCustomTag': '添加自定义标签',
      'commonTags': '常用标签',
      'saveTransaction': '保存记账',
      'pleaseEnterAmount': '请输入金额',
      'pleaseSelectCategory': '请选择分类',
      'saveFailed': '保存失败',
      'descriptionHint': '记录这笔交易的详细信息...',
      'maxTagsHint': '最多添加 {maxTags} 个标签',
    },
    'en': {
      'loading': 'Loading...',
      'error': 'Error',
      'retry': 'Retry',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'refresh': 'Refresh',
      'more': 'More',
      'less': 'Less',
      'all': 'All',
      'none': 'None',
      'today': 'Today',
      'yesterday': 'Yesterday',
      'thisWeek': 'This Week',
      'thisMonth': 'This Month',
      'thisYear': 'This Year',
      'unknown': 'Unknown',
      'noData': 'No Data',
      'loadMore': 'Load More',
      'noMore': 'No More',

      // 导航相关
      'home': 'Home',
      'forecast': 'Forecast',
      'footprint': 'Footprint',
      'profile': 'Profile',

      // 问候语
      'goodMorning': 'Good Morning',
      'goodAfternoon': 'Good Afternoon',
      'goodEvening': 'Good Evening',

      // 用户信息
      'username': 'Username',
      'userEmail': '<EMAIL>',

      // 设置相关
      'settings': 'Settings',
      'language': 'Language',
      'languageSettings': 'Language Settings',
      'theme': 'Theme',
      'darkMode': 'Dark Mode',
      'lightMode': 'Light Mode',
      'systemMode': 'Follow System',

      // 开发者选项
      'developerOptions': 'Developer Options',
      'authDebug': 'Auth Debug',
      'authDebugSubtitle': 'View authentication status and debug info',
      'fontTest': 'Font Test',
      'fontTestSubtitle': 'Test application font display effects',

      // 其他选项
      'helpAndFeedback': 'Help & Feedback',
      'helpAndFeedbackSubtitle': 'Get help or provide feedback',
      'aboutApp': 'About App',
      'aboutAppSubtitle': 'Version info and developer information',

      // 语言选项
      'chinese': '中文',
      'english': 'English',
      'selectLanguage': 'Select Language',
      'languageChanged': 'Language Changed',
      'restartToApply': 'Restart app to apply changes',

      // 认证相关
      'welcomeBack': 'Welcome Back',
      'loginSuccess': 'Welcome back!',
      'loginFailed': 'Login Failed',
      'unknownError': 'Unknown Error Occurred',
      'pleaseTryAgain': 'Please try again later.',
      'loginSubtitle': 'Login to continue using AI Expense Assistant',
      'email': 'Email',
      'emailPlaceholder': 'Please enter your email',
      'emailRequired': 'Email is required',
      'emailInvalid': 'Please enter a valid email address',
      'password': 'Password',
      'passwordPlaceholder': 'Please enter your password',
      'passwordRequired': 'Password is required',
      'login': 'Login',
      'loggingIn': 'Logging in...',
      'noAccount': 'Don\'t have an account? Register',

      // 字体测试页面
      'fontTestPage': 'Font Test Page',
      'fontDisplayTest': 'Font Display Test',
      'chineseTextTest': 'Chinese Text Test',
      'englishTextTest': 'English Text Test',
      'fontTestSample1': 'This is a Chinese text sample for testing font display effects.',
      'fontTestSample2': 'Expense category summary, shopping is the highest',
      'fontTestSample3': 'AI assistant provides professional financial analysis services',
      'fontTestSample4': 'Data visualization charts show your spending trends',
      'fontTestSample5': 'WeChat Pay, Alipay, bank cards and other payment methods',

      // 预算相关
      'budgetSetup': 'Budget Setup',
      'setBudgetAmount': 'Set Budget Amount',
      'setBudgetAmountDesc': 'Set budget amount for each category',
      'nextStep': 'Next Step',
      'monthlyBudget': 'Monthly Budget',
      'monthlyBudgetDesc': 'Manage your expenses monthly, suitable for most people',
      'weeklyBudget': 'Weekly Budget',
      'weeklyBudgetDesc': 'Manage expenses weekly for more precise control',
      'yearlyBudget': 'Yearly Budget',
      'yearlyBudgetDesc': 'Long-term financial planning, suitable for large expense management',

      // 注册相关
      'createAccount': 'Create Your Account',
      'setPassword': 'Set Password',
      'setAccountPassword': 'Set Your Account Password',
      'verificationCode': 'Verification Code',
      'getVerificationCode': 'Get Verification Code',
      'sending': 'Sending...',
      'codeSent': 'Verification code sent',
      'sendFailed': 'Send failed',
      'pleaseEnterEmail': 'Please enter email',
      'pleaseEnterEmailPlaceholder': 'Please enter email...',
      'emailCannotBeEmpty': 'Email cannot be empty',
      'pleaseEnterValidEmail': 'Please enter a valid email',
      'pleaseEnterVerificationCode': 'Please enter verification code',
      'verificationCodeCannotBeEmpty': 'Verification code cannot be empty',
      'confirmPassword': 'Confirm Password',
      'pleaseEnterPasswordAgain': 'Please enter your password again',
      'pleaseEnterPasswordAgainShort': 'Please enter password again',
      'passwordCannotBeEmpty': 'Password cannot be empty',
      'passwordTooShort': 'Password must be at least 6 characters',
      'passwordMustContainNumbersAndLetters': 'Password must contain numbers and letters',
      'passwordsDoNotMatch': 'Passwords do not match',
      'registering': 'Registering...',
      'completeRegistration': 'Complete Registration',
      'registrationSuccess': 'Registration successful!',
      'registrationFailed': 'Registration failed',

      // 预测模块
      'financialNavigator': 'Hello, I am your financial navigator',
      'financialMapSubtitle': 'In just 3 steps, let\'s map your financial future together',
      'predictCashFlow': 'Predict Future Cash Flow',
      'predictCashFlowDesc': 'See your financial status every day',
      'aiSmartSuggestions': 'AI Smart Suggestions',
      'aiSmartSuggestionsDesc': 'Personalized financial decision guidance',
      'riskWarning': 'Risk Warning',
      'riskWarningDesc': 'Detect potential financial risks in advance',
      'analyzingFinancialData': 'I am analyzing your financial data to generate a 30-day cash flow forecast',
      'analyzeIncomeExpensePattern': 'Analyze income and expense patterns',
      'calculateCashFlowTrend': 'Calculate cash flow trends',
      'generateRiskWarning': 'Generate risk warnings',
      'loadingFinancialForecast': 'Loading financial forecast...',

      // 聊天模块
      'newChat': 'New Chat',
      'noMessagesToDisplay': 'No messages to display.',
      'loadingFailed': 'Loading failed',

      // 预算模块
      'budgetSettings': 'Budget Settings',
      'editBudget': 'Edit Budget',
      'editBudgetDesc': 'Modify budget amounts and categories',
      'reminderSettings': 'Reminder Settings',
      'reminderSettingsDesc': 'Set budget reminders and notifications',
      'budgetReport': 'Budget Report',
      'budgetReportDesc': 'View detailed budget analysis reports',
      'welcomeToBudget': 'Welcome to Budget Feature!',
      'createNewBudgetPlan': 'Create New Budget Plan',
      'budgetWelcomeDesc': 'By setting budgets, you can better control spending and achieve financial goals. Let\'s start setting up your first budget plan!',
      'budgetCreateDesc': 'Set budget limits for different spending categories to help you manage your finances better.',

      // 足迹模块
      'searchIn': 'Search',
      'searchInAllRecords': 'Search for related content in all records',

      // 预测工具栏
      'forecastTitle': 'Forecast',
      'forecastSubtitle': 'Intelligently predict future cash flow based on your financial data',

      // 聊天输入相关
      'inputMessage': 'Type a message...',
      'listening': 'Listening...',
      'aiThinking': 'AI thinking...',
      'speechNotRecognized': 'Speech not recognized, please try again',

      // 交易搜索相关
      'noTransactionsFound': 'No transactions found',
      'tryAdjustingSearch': 'Try adjusting search criteria or create new transaction records',

      // 预测模块 - 日期和时间
      'todayLabel': 'Today',
      'tomorrowLabel': 'Tomorrow',
      'balanceLabel': 'Balance',
      'noSpecialEvents': 'No special events',
      'monthDayFormat': '', // 英文使用不同的日期格式

      // 财务安全线页面
      'financialSafetyLine': 'Financial Safety Line',
      'currentSetting': 'Current Setting',
      'dailySpendingEstimate': 'Daily Spending Estimate',
      'adjustDailySpendingAmount': 'Adjust daily spending forecast amount',
      'tellMeYourSafetyLine': 'What is your financial "safety line"?',
      'safetyLineDescription': 'This is the minimum balance you want to maintain in your account. I will alert you when your balance approaches this amount.',

      // 消费设置步骤
      'dailySpendingQuestion': 'How much do you spend on daily life?',
      'dailySpendingDescription': 'Including meals, transportation, shopping and other daily expenses\nThis is just an initial estimate, I will make predictions more accurate through your future real records',
      'perDay': 'per day',
      'referenceStandard': 'Reference Standard',
      'frugalType': 'Frugal',
      'comfortableType': 'Comfortable',
      'relaxedType': 'Relaxed',
      'frugalAmount': '¥50-100/day',
      'comfortableAmount': '¥100-200/day',
      'relaxedAmount': '¥200-300/day',
      'previousStep': 'Previous',
      'completeMapping': 'Complete',

      // 手动记账相关
      'expense': 'Expense',
      'income': 'Income',
      'category': 'Category',
      'dateTime': 'Date & Time',
      'description': 'Description',
      'tags': 'Tags',
      'dayBeforeYesterday': 'Day Before Yesterday',
      'selectDate': 'Select Date',
      'selectTime': 'Select Time',
      'addCustomTag': 'Add Custom Tag',
      'commonTags': 'Common Tags',
      'saveTransaction': 'Save Transaction',
      'pleaseEnterAmount': 'Please enter amount',
      'pleaseSelectCategory': 'Please select category',
      'saveFailed': 'Save failed',
      'descriptionHint': 'Record details of this transaction...',
      'maxTagsHint': 'Maximum {maxTags} tags allowed',
    },
  };

  /// 获取通用字符串
  static String get(String key, [String? locale]) {
    final currentLoc = locale ?? _currentLocale;
    final stringMap = _commonStrings[currentLoc] ?? _commonStrings['zh_CN']!;
    return stringMap[key] ?? key;
  }

  /// 获取当前语言代码
  static String getCurrentLocale() {
    return _currentLocale;
  }

  // =============================================================================
  // 便捷访问器
  // =============================================================================

  static String get loading => get('loading');
  static String get error => get('error');
  static String get retry => get('retry');
  static String get cancel => get('cancel');
  static String get confirm => get('confirm');
  static String get save => get('save');
  static String get delete => get('delete');
  static String get edit => get('edit');
  static String get add => get('add');
  static String get search => get('search');
  static String get filter => get('filter');
  static String get sort => get('sort');
  static String get refresh => get('refresh');
  static String get more => get('more');
  static String get less => get('less');
  static String get all => get('all');
  static String get none => get('none');
  static String get today => get('today');
  static String get yesterday => get('yesterday');
  static String get thisWeek => get('thisWeek');
  static String get thisMonth => get('thisMonth');
  static String get thisYear => get('thisYear');
  static String get unknown => get('unknown');
  static String get noData => get('noData');
  static String get loadMore => get('loadMore');
  static String get noMore => get('noMore');

  // =============================================================================
  // 转账相关字符串
  // =============================================================================

  // 转账描述格式化函数已移除

  /// 获取支持的语言列表
  static List<String> get supportedLocales => _categoryNames.keys.toList();

  /// 检查是否支持指定语言
  static bool isLocaleSupported(String locale) {
    return _categoryNames.containsKey(locale);
  }

  // 手动记账相关getter方法
  static String get expense => get('expense');
  static String get income => get('income');
  static String get category => get('category');
  static String get dateTime => get('dateTime');
  static String get description => get('description');
  static String get tags => get('tags');
  static String get dayBeforeYesterday => get('dayBeforeYesterday');
  static String get selectDate => get('selectDate');
  static String get selectTime => get('selectTime');
  static String get addCustomTag => get('addCustomTag');
  static String get commonTags => get('commonTags');
  static String get saveTransaction => get('saveTransaction');
  static String get pleaseEnterAmount => get('pleaseEnterAmount');
  static String get pleaseSelectCategory => get('pleaseSelectCategory');
  static String get saveFailed => get('saveFailed');
  static String get descriptionHint => get('descriptionHint');

  // 带参数的方法
  static String maxTagsHint(int maxTags) {
    final template = get('maxTagsHint');
    return template.replaceAll('{maxTags}', maxTags.toString());
  }

  // 便捷的context方法
  static AppStrings of(context) => AppStrings._();
}
