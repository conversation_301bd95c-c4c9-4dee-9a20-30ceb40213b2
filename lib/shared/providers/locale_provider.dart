// shared/providers/locale_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/locale_service.dart';

/// 语言状态管理
class LocaleNotifier extends StateNotifier<String> {
  LocaleNotifier() : super('zh_CN') {
    _loadSavedLocale();
  }
  
  /// 加载保存的语言设置
  Future<void> _loadSavedLocale() async {
    final savedLocale = await LocaleService.getSavedLocale();
    state = savedLocale;
  }
  
  /// 更改语言
  Future<bool> changeLocale(String newLocale) async {
    if (!LocaleService.isSupportedLocale(newLocale)) {
      return false;
    }
    
    final success = await LocaleService.saveLocale(newLocale);
    if (success) {
      state = newLocale;
      return true;
    }
    
    return false;
  }
  
  /// 获取当前语言的显示名称
  String get currentLocaleDisplayName {
    return LocaleService.getLocaleDisplayName(state);
  }
  
  /// 获取支持的语言列表
  List<String> get supportedLocales {
    return LocaleService.supportedLocales;
  }
  
  /// 获取语言显示名称
  String getLocaleDisplayName(String locale) {
    return LocaleService.getLocaleDisplayName(locale);
  }
}

/// 语言状态提供者
final localeProvider = StateNotifierProvider<LocaleNotifier, String>((ref) {
  return LocaleNotifier();
});
