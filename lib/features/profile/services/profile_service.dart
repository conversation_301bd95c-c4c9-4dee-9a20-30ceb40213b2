import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/network_client.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/cash_pocket.dart';
import '../models/financial_settings.dart';

class ProfileService {
  final NetworkClient _networkClient;

  ProfileService(this._networkClient);

  /// 获取用户现金来源完整信息（新API）
  Future<CashSourceResponse> getCashSources() async {
    return await _networkClient.request<CashSourceResponse>(
      '/user/cash-sources',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return CashSourceResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析现金来源响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /user/cash-sources 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 保存用户现金来源信息
  Future<CashSourceSummary> saveCashSources(List<CashSource> sources) async {
    final request = CashSourceRequest(sources: sources);

    return await _networkClient.request<CashSourceSummary>(
      '/user/cash-sources',
      method: HttpMethod.post,
      data: request.toJson(),
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return CashSourceSummary.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析现金来源响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /user/cash-sources 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }



  /// 获取用户财务安全线设置
  Future<FinancialSettingsResponse> getFinancialSettings() async {
    return await _networkClient.request<FinancialSettingsResponse>(
      '/user/financial-settings',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return FinancialSettingsResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析财务设置响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /user/financial-settings 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 更新用户财务安全线设置
  Future<FinancialSettingsResponse> updateFinancialSettings(FinancialSettingsRequest request) async {
    return await _networkClient.request<FinancialSettingsResponse>(
      '/user/financial-settings',
      method: HttpMethod.patch,
      data: request.toJson(),
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return FinancialSettingsResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析财务设置响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /user/financial-settings 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }
}

// Provider for ProfileService
final profileServiceProvider = Provider<ProfileService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return ProfileService(networkClient);
});
