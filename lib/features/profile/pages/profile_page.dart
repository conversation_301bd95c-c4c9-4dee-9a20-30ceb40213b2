import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../widgets/theme_switcher.dart';
import '/shared/l10n/app_strings.dart';
import '/shared/providers/locale_provider.dart';

class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;

    return FScaffold(
      header: FHeader(
        title: Text(
          AppStrings.get('profile'),
          style: theme.typography.xl.copyWith(
            color: colors.foreground,
          ),
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息卡片
            FCard.raw(
              child: Padding(
                padding: const EdgeInsets.all(12.0), // 减少内边距
                child: Row(
                  children: [
                    // 头像 - 减小尺寸
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(color: colors.primary, borderRadius: BorderRadius.circular(24)),
                      child: Icon(Icons.person, size: 24, color: colors.primaryForeground),
                    ),
                    const SizedBox(width: 12), // 减少间距
                    // 用户信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(AppStrings.get('username'), style: theme.typography.lg),
                          const SizedBox(height: 2), // 减少间距
                          Text(AppStrings.get('userEmail'), style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                        ],
                      ),
                    ),

                    // 编辑按钮
                    FButton(
                      style: FButtonStyle.outline(),
                      onPress: () {
                        // TODO: 实现编辑功能
                      },
                      child: Text(AppStrings.get('edit')),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16), // 减少卡片间距
            // 主题切换
            const ThemeSwitcher(),

            const SizedBox(height: 16), // 减少卡片间距
            // 共享空间
            FCard(
              title: Row(
                children: [
                  Icon(
                    Icons.group_outlined,
                    size: 18, // 减小图标尺寸
                    color: colors.foreground,
                  ),
                  const SizedBox(width: 8),
                  Text('共享空间'),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: FTile(
                  prefix: Icon(Icons.people_outline),
                  title: Text('我的共享空间'),
                  subtitle: Text('与朋友一起记账，轻松AA制'),
                  suffix: Icon(Icons.chevron_right),
                  onPress: () => context.push('/shared-space'),
                ),
              ),
            ),

            const SizedBox(height: 16), // 减少卡片间距
            // 设置选项
            FCard(
              title: Row(
                children: [
                  Icon(
                    Icons.settings_outlined,
                    size: 18, // 减小图标尺寸
                    color: colors.foreground,
                  ),
                  const SizedBox(width: 8),
                  Text(AppStrings.get('settings')),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: FTile(
                  prefix: Icon(Icons.language_outlined),
                  title: Text(AppStrings.get('language')),
                  subtitle: Text(ref.watch(localeProvider.notifier).currentLocaleDisplayName),
                  suffix: Icon(Icons.chevron_right),
                  onPress: () => context.go('/profile/language'),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 开发者选项
            FCard(
              title: Row(
                children: [
                  Icon(Icons.code_outlined, size: 20, color: colors.foreground),
                  const SizedBox(width: 8),
                  Text(AppStrings.get('developerOptions')),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Column(
                  children: [
                    FTile(
                      prefix: Icon(Icons.bug_report_outlined),
                      title: Text(AppStrings.get('authDebug')),
                      subtitle: Text(AppStrings.get('authDebugSubtitle')),
                      suffix: Icon(Icons.chevron_right),
                      onPress: () => context.go('/profile/debug'),
                    ),
                    const SizedBox(height: 12),
                    FTile(
                      prefix: Icon(Icons.font_download_outlined),
                      title: Text(AppStrings.get('fontTest')),
                      subtitle: Text(AppStrings.get('fontTestSubtitle')),
                      suffix: Icon(Icons.chevron_right),
                      onPress: () => context.go('/profile/font-test'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 其他选项
            FCard(
              title: Row(
                children: [
                  Icon(Icons.more_horiz, size: 20, color: colors.foreground),
                  const SizedBox(width: 8),
                  Text(AppStrings.get('more')),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Column(
                  children: [
                    FTile(
                      prefix: Icon(Icons.help_outline),
                      title: Text(AppStrings.get('helpAndFeedback')),
                      subtitle: Text(AppStrings.get('helpAndFeedbackSubtitle')),
                      suffix: Icon(Icons.chevron_right),
                      onPress: () {
                        // TODO: 实现帮助功能
                      },
                    ),
                    const SizedBox(height: 12),
                    FTile(
                      prefix: Icon(Icons.info_outline),
                      title: Text(AppStrings.get('aboutApp')),
                      subtitle: Text(AppStrings.get('aboutAppSubtitle')),
                      suffix: Icon(Icons.chevron_right),
                      onPress: () {
                        // TODO: 实现关于页面
                      },
                    ),
                    const SizedBox(height: 12),
                    FTile(
                      prefix: Icon(Icons.bug_report),
                      title: const Text('会话列表调试'),
                      subtitle: const Text('调试会话列表加载问题'),
                      suffix: Icon(Icons.chevron_right),
                      onPress: () {
                        context.goNamed('conversation-debug');
                      },
                    ),
                  ],
                ),
              ),
            ),

            // 底部间距
            const SizedBox(height: 24), // 减少底部间距
          ],
        ),
      ),
    );
  }
}
