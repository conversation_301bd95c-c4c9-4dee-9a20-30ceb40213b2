// features/profile/pages/language_settings_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '/shared/providers/locale_provider.dart';
import '/shared/l10n/app_strings.dart';

class LanguageSettingsPage extends ConsumerWidget {
  const LanguageSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;
    final currentLocale = ref.watch(localeProvider);
    final localeNotifier = ref.read(localeProvider.notifier);

    return FScaffold(
      header: FHeader(
        title: Text(AppStrings.get('languageSettings')),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              // 说明文本
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  AppStrings.get('selectLanguage'),
                  style: theme.typography.base.copyWith(
                    color: colors.mutedForeground,
                  ),
                ),
              ),
              
              // 语言选项卡片
              FCard(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: localeNotifier.supportedLocales.map((locale) {
                      final isSelected = locale == currentLocale;
                      final displayName = localeNotifier.getLocaleDisplayName(locale);

                      return FTile(
                        title: Text(
                          displayName,
                          style: theme.typography.base.copyWith(
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                            color: isSelected ? colors.primary : colors.foreground,
                          ),
                        ),
                        suffix: isSelected ? Icon(Icons.check, size: 20, color: colors.primary) : null,
                        onPress: () => _changeLanguage(context, ref, locale),
                      );
                    }).toList(),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 提示信息
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colors.muted.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 20,
                      color: colors.mutedForeground,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        AppStrings.get('restartToApply'),
                        style: theme.typography.sm.copyWith(
                          color: colors.mutedForeground,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
    );
  }


  Future<void> _changeLanguage(BuildContext context, WidgetRef ref, String newLocale) async {
    final localeNotifier = ref.read(localeProvider.notifier);
    final currentLocale = ref.read(localeProvider);
    
    if (newLocale == currentLocale) {
      return; // 相同语言，无需更改
    }
    
    final success = await localeNotifier.changeLocale(newLocale);

    if (!context.mounted) return;

    if (success) {
      showFToast(
        context: context,
        title: Text(AppStrings.get('languageChanged')),
      );
    } else {
      showFToast(
        context: context,
        title: Text(AppStrings.get('error')),
      );
    }
  }
}
