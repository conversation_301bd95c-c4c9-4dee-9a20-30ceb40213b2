import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/cash_pocket.dart';
import '../services/profile_service.dart';
import '../../../core/network/exceptions/app_exception.dart';

part 'cash_pocket_provider.freezed.dart';

// 现金来源状态
@freezed
abstract class CashSourceState with _$CashSourceState {
  const factory CashSourceState({
    @Default([]) List<CashSource> sources,
    Decimal? totalBalance,
    DateTime? lastUpdatedAt,
    @Default(false) bool isLoading,
    String? error,
  }) = _CashSourceState;

  const CashSourceState._();

  // 计算现金来源总余额
  Decimal get calculatedSourcesBalance {
    return sources.fold(Decimal.zero, (sum, source) => sum + source.balance);
  }

  // 获取实际总余额（优先使用服务器返回的值）
  Decimal get effectiveTotalBalance {
    return totalBalance ?? calculatedSourcesBalance;
  }
}

// 现金来源状态管理器
class CashSourceNotifier extends StateNotifier<CashSourceState> {
  final ProfileService _profileService;

  CashSourceNotifier(this._profileService) : super(const CashSourceState());

  /// 加载现金来源数据
  Future<void> loadCashSources() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _profileService.getCashSources();

      state = state.copyWith(
        sources: response.sources,
        totalBalance: response.totalBalance,
        lastUpdatedAt: DateTime.parse(response.lastUpdatedAt),
        isLoading: false,
        error: null,
      );
    } catch (e) {
      String errorMessage = '加载现金来源失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );
    }
  }

  /// 保存现金来源数据
  Future<bool> saveCashSources(List<CashSource> sources) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final summary = await _profileService.saveCashSources(sources);

      // 保存成功后，使用UI上的来源列表 + 服务器返回的总余额和时间
      state = state.copyWith(
        sources: sources,
        totalBalance: summary.totalBalance,
        lastUpdatedAt: summary.lastUpdatedAt,
        isLoading: false,
        error: null,
      );

      return true;
    } catch (e) {
      String errorMessage = '保存现金来源失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );

      return false;
    }
  }

  /// 添加新的现金来源
  void addCashSource(CashSource source) {
    final updatedSources = [...state.sources, source];
    state = state.copyWith(
      sources: updatedSources,
      totalBalance: _calculateSourcesTotal(updatedSources),
    );
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 计算现金来源总余额
  Decimal _calculateSourcesTotal(List<CashSource> sources) {
    return sources.fold(Decimal.zero, (sum, source) => sum + source.balance);
  }
}

// Provider
final cashSourceProvider = StateNotifierProvider<CashSourceNotifier, CashSourceState>((ref) {
  final profileService = ref.watch(profileServiceProvider);
  return CashSourceNotifier(profileService);
});

// 保持向后兼容的别名
final cashPocketProvider = cashSourceProvider;
