// features/profile/providers/financial_settings_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:decimal/decimal.dart';
import '../models/financial_settings.dart';
import '../services/profile_service.dart';
import '../../../core/network/exceptions/app_exception.dart';

/// 财务设置状态管理器
class FinancialSettingsNotifier extends StateNotifier<FinancialSettingsState> {
  final ProfileService _profileService;
  Decimal? _originalThreshold; // 保存原始值用于比较

  FinancialSettingsNotifier(this._profileService) : super(const FinancialSettingsState()) {
    // 初始化时加载设置
    loadFinancialSettings();
  }

  /// 加载财务设置
  Future<void> loadFinancialSettings() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _profileService.getFinancialSettings();
      
      _originalThreshold = response.safetyBalanceThreshold;
      
      state = state.copyWith(
        safetyBalanceThreshold: response.safetyBalanceThreshold,
        lastUpdatedAt: response.updatedAt,
        isLoading: false,
        hasChanges: false,
        error: null,
      );
    } catch (e) {
      String errorMessage = '加载财务设置失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      // 如果加载失败，设置默认值
      final defaultThreshold = Decimal.fromInt(1500);
      _originalThreshold = defaultThreshold;

      state = state.copyWith(
        safetyBalanceThreshold: defaultThreshold,
        isLoading: false,
        hasChanges: false,
        error: errorMessage,
      );
    }
  }

  /// 更新安全线阈值
  void updateSafetyThreshold(Decimal newThreshold) {
    // 如果还没有原始值，使用当前状态的值作为原始值
    if (_originalThreshold == null) {
      _originalThreshold = state.effectiveSafetyBalanceThreshold;
      print('FinancialSettingsNotifier: 设置原始阈值为: $_originalThreshold');
    }

    final hasChanges = _originalThreshold != newThreshold;
    print('FinancialSettingsNotifier: 更新阈值 - 原始值: $_originalThreshold, 新值: $newThreshold, 有变化: $hasChanges');

    state = state.copyWith(
      safetyBalanceThreshold: newThreshold,
      hasChanges: hasChanges,
      error: null,
    );
  }

  /// 保存财务设置
  Future<bool> saveFinancialSettings() async {
    if (!state.hasChanges) return true;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = FinancialSettingsRequest(
        safetyBalanceThreshold: state.effectiveSafetyBalanceThreshold,
      );

      final response = await _profileService.updateFinancialSettings(request);
      
      _originalThreshold = response.safetyBalanceThreshold;
      
      state = state.copyWith(
        safetyBalanceThreshold: response.safetyBalanceThreshold,
        lastUpdatedAt: response.updatedAt,
        isLoading: false,
        hasChanges: false,
        error: null,
      );

      return true;
    } catch (e) {
      String errorMessage = '保存财务设置失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );

      return false;
    }
  }

  /// 重置到原始值
  void resetToOriginal() {
    if (_originalThreshold != null) {
      state = state.copyWith(
        safetyBalanceThreshold: _originalThreshold!,
        hasChanges: false,
        error: null,
      );
    }
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider
final financialSettingsProvider = StateNotifierProvider<FinancialSettingsNotifier, FinancialSettingsState>((ref) {
  final profileService = ref.watch(profileServiceProvider);
  return FinancialSettingsNotifier(profileService);
});
