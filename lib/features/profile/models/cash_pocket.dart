// features/profile/models/cash_pocket.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:decimal/decimal.dart';

part 'cash_pocket.freezed.dart';
part 'cash_pocket.g.dart';

/// 现金来源类型枚举
enum CashSourceType {
  @JsonValue('preset')
  preset,
  @JsonValue('custom')
  custom,
}

/// 现金来源数据模型
@freezed
abstract class CashSource with _$CashSource {
  const factory CashSource({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal balance,
    required CashSourceType sourceType,
    @JsonKey(includeIfNull: false) String? presetKey,
    @JsonKey(includeIfNull: false) String? customName,
  }) = _CashSource;

  factory CashSource.fromJson(Map<String, dynamic> json) =>
      _$CashSourceFromJson(json);
}

/// 现金来源摘要响应模型（用于获取总余额）
@freezed
abstract class CashSourceSummary with _$CashSourceSummary {
  const factory CashSourceSummary({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal totalBalance,
    required DateTime lastUpdatedAt,
  }) = _CashSourceSummary;

  factory CashSourceSummary.fromJson(Map<String, dynamic> json) =>
      _$CashSourceSummaryFromJson(json);
}

/// 现金来源响应模型（新API格式）
@freezed
abstract class CashSourceResponse with _$CashSourceResponse {
  const factory CashSourceResponse({
    required List<CashSource> sources,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal totalBalance,
    required String lastUpdatedAt,
  }) = _CashSourceResponse;

  factory CashSourceResponse.fromJson(Map<String, dynamic> json) =>
      _$CashSourceResponseFromJson(json);
}

/// 现金来源请求模型（新API格式）
@freezed
abstract class CashSourceRequest with _$CashSourceRequest {
  @JsonSerializable(explicitToJson: true)
  const factory CashSourceRequest({
    required List<CashSource> sources,
  }) = _CashSourceRequest;

  factory CashSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$CashSourceRequestFromJson(json);
}



// 辅助函数：Decimal序列化/反序列化
Decimal _decimalFromJson(String value) => Decimal.parse(value);
String _decimalToJson(Decimal value) => value.toString();
