// features/profile/models/financial_settings.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:decimal/decimal.dart';

part 'financial_settings.freezed.dart';
part 'financial_settings.g.dart';

/// 财务安全线设置请求模型
@freezed
abstract class FinancialSettingsRequest with _$FinancialSettingsRequest {
  const factory FinancialSettingsRequest({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) 
    required Decimal safetyBalanceThreshold,
  }) = _FinancialSettingsRequest;

  factory FinancialSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$FinancialSettingsRequestFromJson(json);
}

/// 财务安全线设置响应模型
@freezed
abstract class FinancialSettingsResponse with _$FinancialSettingsResponse {
  const factory FinancialSettingsResponse({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) 
    required Decimal safetyBalanceThreshold,
    required String updatedAt,
  }) = _FinancialSettingsResponse;

  factory FinancialSettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$FinancialSettingsResponseFromJson(json);
}

/// 财务安全线状态模型
@freezed
abstract class FinancialSettingsState with _$FinancialSettingsState {
  const factory FinancialSettingsState({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson)
    Decimal? safetyBalanceThreshold,
    String? lastUpdatedAt,
    @Default(false) bool isLoading,
    @Default(false) bool hasChanges,
    String? error,
  }) = _FinancialSettingsState;

  const FinancialSettingsState._();

  /// 获取实际的安全线阈值（带默认值）
  Decimal get effectiveSafetyBalanceThreshold {
    return safetyBalanceThreshold ?? Decimal.fromInt(1500);
  }

  /// 获取人设描述
  String get personalityDescription {
    final amount = effectiveSafetyBalanceThreshold.toDouble();
    if (amount <= 500) {
      return '随遇而安型：我相信船到桥头自然直。';
    } else if (amount <= 2000) {
      return '稳健前行型：我喜欢为生活保留一份余地。';
    } else if (amount <= 5000) {
      return '未雨绸缪型：充足的储备让我无比安心。';
    } else {
      return '绝对掌控型：一切尽在我的掌握之中。';
    }
  }

  /// 获取人设类型（用于UI样式）
  PersonalityType get personalityType {
    final amount = effectiveSafetyBalanceThreshold.toDouble();
    if (amount <= 500) {
      return PersonalityType.casual;
    } else if (amount <= 2000) {
      return PersonalityType.steady;
    } else if (amount <= 5000) {
      return PersonalityType.cautious;
    } else {
      return PersonalityType.control;
    }
  }
}

/// 人设类型枚举
enum PersonalityType {
  casual,   // 随遇而安型
  steady,   // 稳健前行型
  cautious, // 未雨绸缪型
  control,  // 绝对掌控型
}

/// 人设类型扩展
extension PersonalityTypeExtension on PersonalityType {
  String get displayName {
    switch (this) {
      case PersonalityType.casual:
        return '随遇而安型';
      case PersonalityType.steady:
        return '稳健前行型';
      case PersonalityType.cautious:
        return '未雨绸缪型';
      case PersonalityType.control:
        return '绝对掌控型';
    }
  }

  String get description {
    switch (this) {
      case PersonalityType.casual:
        return '我相信船到桥头自然直。';
      case PersonalityType.steady:
        return '我喜欢为生活保留一份余地。';
      case PersonalityType.cautious:
        return '充足的储备让我无比安心。';
      case PersonalityType.control:
        return '一切尽在我的掌握之中。';
    }
  }
}

// 辅助函数：Decimal序列化/反序列化
Decimal _decimalFromJson(dynamic value) {
  if (value is String) {
    return Decimal.parse(value);
  } else if (value is num) {
    return Decimal.fromInt(value.toInt());
  }
  throw ArgumentError('Cannot convert $value to Decimal');
}

String _decimalToJson(Decimal value) => value.toString();
