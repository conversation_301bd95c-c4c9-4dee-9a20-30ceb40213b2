import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../../../app/theme/theme_provider.dart';
import '../../../app/theme/app_theme_mode.dart';
import 'theme_preview.dart';

class ThemeSwitcher extends ConsumerWidget {
  const ThemeSwitcher({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Icon(
                  Icons.palette_outlined,
                  size: 20,
                  color: colorScheme.foreground,
                ),
                const SizedBox(width: 8),
                Text(
                  '主题设置',
                  style: theme.typography.lg,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 主题预览
            Row(
              children: [
                ThemePreview(
                  isDark: false,
                  isSelected: currentTheme == ThemeMode.light,
                  title: '浅色',
                  onTap: () {
                    ref.read(themeProvider.notifier).setTheme(AppThemeMode.light);
                  },
                ),
                const SizedBox(width: 8),
                ThemePreview(
                  isDark: true,
                  isSelected: currentTheme == ThemeMode.dark,
                  title: '深色',
                  onTap: () {
                    ref.read(themeProvider.notifier).setTheme(AppThemeMode.dark);
                  },
                ),
                const SizedBox(width: 8),
                ThemePreview(
                  isDark: MediaQuery.of(context).platformBrightness == Brightness.dark,
                  isSelected: currentTheme == ThemeMode.system,
                  title: '跟随系统',
                  onTap: () {
                    ref.read(themeProvider.notifier).setTheme(AppThemeMode.system);
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 当前主题状态
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.muted.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _getCurrentThemeIcon(currentTheme),
                    size: 16,
                    color: colorScheme.mutedForeground,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '当前主题：${_getCurrentThemeName(currentTheme)}',
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required WidgetRef ref,
    required String title,
    required String subtitle,
    required IconData icon,
    required AppThemeMode themeMode,
    required ThemeMode currentTheme,
  }) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    
    // 判断当前选项是否被选中
    final isSelected = _isThemeModeSelected(themeMode, currentTheme);

    return GestureDetector(
      onTap: () {
        final platformBrightness = MediaQuery.of(context).platformBrightness;
        ref.read(themeProvider.notifier).setTheme(
          themeMode,
          platformBrightness: platformBrightness,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary
                : colorScheme.border,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? colorScheme.primary
                    : colorScheme.muted,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                size: 18,
                color: isSelected 
                    ? colorScheme.primaryForeground
                    : colorScheme.mutedForeground,
              ),
            ),
            const SizedBox(width: 12),
            
            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.typography.base.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected 
                          ? colorScheme.primary
                          : colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
            
            // 选中指示器
            if (isSelected)
              Icon(
                Icons.check_circle,
                size: 20,
                color: colorScheme.primary,
              ),
          ],
        ),
      ),
    );
  }

  IconData _getCurrentThemeIcon(ThemeMode currentTheme) {
    switch (currentTheme) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  String _getCurrentThemeName(ThemeMode currentTheme) {
    switch (currentTheme) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  bool _isThemeModeSelected(AppThemeMode themeMode, ThemeMode currentTheme) {
    switch (themeMode) {
      case AppThemeMode.system:
        return currentTheme == ThemeMode.system;
      case AppThemeMode.light:
        return currentTheme == ThemeMode.light;
      case AppThemeMode.dark:
        return currentTheme == ThemeMode.dark;
      case AppThemeMode.invertedSystem:
        return false;
    }
  }
}
