import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:tabler_icons_next/tabler_icons_next.dart' as tabler;
import '../models/cash_pocket.dart';
import 'cash_source_input_sheet.dart';

/// 现金来源选择界面
class CashSourceSelectionSheet extends StatelessWidget {
  final Function(CashSource) onSourceAdded;

  const CashSourceSelectionSheet({
    super.key,
    required this.onSourceAdded,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colors.background,
        border: Border.symmetric(horizontal: BorderSide(color: colors.border)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择现金来源',
              style: theme.typography.xl2.copyWith(
                fontWeight: FontWeight.w600,
                color: colors.foreground,
                height: 1.5,
              ),
            ),
            Text(
              '选择预设来源或添加自定义来源',
              style: theme.typography.sm.copyWith(
                color: colors.mutedForeground,
              ),
            ),
            const SizedBox(height: 20),
            // 预设选项网格
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 2.5,
              children: [
                _buildPresetOption(
                  context,
                  icon: tabler.BrandWechat(
                    color: const Color(0xFF07C160),
                  ),
                  label: '微信',
                  presetKey: 'wechat',
                  color: const Color(0xFF07C160),
                ),
                _buildPresetOption(
                  context,
                  icon: tabler.BrandAlipay(
                    color: const Color(0xFF1677FF),
                  ),
                  label: '支付宝',
                  presetKey: 'alipay',
                  color: const Color(0xFF1677FF),
                ),
                _buildPresetOption(
                  context,
                  icon: tabler.Cash(
                    color: const Color(0xFF52C41A),
                  ),
                  label: '现金',
                  presetKey: 'cash',
                  color: const Color(0xFF52C41A),
                ),
                _buildPresetOption(
                  context,
                  icon: tabler.BrandPaypal(
                    color: const Color(0xFF0070BA),
                  ),
                  label: 'PayPal',
                  presetKey: 'paypal',
                  color: const Color(0xFF0070BA),
                ),
                _buildPresetOption(
                  context,
                  icon: tabler.BrandApple(
                    color: const Color(0xFF000000),
                  ),
                  label: '苹果',
                  presetKey: 'apple',
                  color: const Color(0xFF000000),
                ),
                _buildPresetOption(
                  context,
                  icon: tabler.BrandGoogle(
                    color: const Color(0xFF4285F4),
                  ),
                  label: '谷歌',
                  presetKey: 'google',
                  color: const Color(0xFF4285F4),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 自定义选项
            _buildCustomOption(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetOption(
    BuildContext context, {
    required Widget icon,
    required String label,
    required String presetKey,
    required Color color,
  }) {
    final theme = context.theme;

    return FButton(
      style: FButtonStyle.outline(),
      onPress: () => _showPresetInputSheet(context, presetKey, label, icon, color),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: icon,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.typography.sm,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomOption(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return FButton(
      onPress: () => _showCustomInputSheet(context),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: tabler.Plus(
              color: colors.primaryForeground,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '其他 (自定义来源)',
            style: theme.typography.sm.copyWith(
              color: colors.primaryForeground,
            ),
          ),
        ],
      ),
    );
  }

  void _showPresetInputSheet(
    BuildContext context,
    String presetKey,
    String label,
    Widget icon,
    Color color,
  ) {
    Navigator.of(context).pop(); // 关闭选择界面

    showFSheet(
      context: context,
      side: FLayout.btt,
      builder: (context) => CashSourceInputSheet(
        title: label,
        icon: icon,
        iconColor: color,
        isPreset: true,
        presetKey: presetKey,
        onSourceAdded: onSourceAdded,
      ),
    );
  }

  void _showCustomInputSheet(BuildContext context) {
    Navigator.of(context).pop(); // 关闭选择界面

    showFSheet(
      context: context,
      side: FLayout.btt,
      builder: (context) => CashSourceInputSheet(
        title: '自定义来源',
        icon: tabler.Wallet(),
        iconColor: const Color(0xFF6B7280),
        isPreset: false,
        onSourceAdded: onSourceAdded,
      ),
    );
  }
}
