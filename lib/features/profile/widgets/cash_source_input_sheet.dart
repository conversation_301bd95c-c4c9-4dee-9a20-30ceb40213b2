import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:decimal/decimal.dart';
import '../models/cash_pocket.dart';

/// 现金来源输入界面
class CashSourceInputSheet extends StatefulWidget {
  final String title;
  final Widget icon;
  final Color iconColor;
  final bool isPreset;
  final String? presetKey;
  final Function(CashSource) onSourceAdded;

  const CashSourceInputSheet({
    super.key,
    required this.title,
    required this.icon,
    required this.iconColor,
    required this.isPreset,
    this.presetKey,
    required this.onSourceAdded,
  });

  @override
  State<CashSourceInputSheet> createState() => _CashSourceInputSheetState();
}

class _CashSourceInputSheetState extends State<CashSourceInputSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colors.background,
        border: Border.symmetric(horizontal: BorderSide(color: colors.border)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: theme.typography.xl2.copyWith(fontWeight: FontWeight.w600, color: colors.foreground, height: 1.5),
            ),
            Text(widget.isPreset ? '请输入当前余额' : '请输入来源名称和当前余额', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
            const SizedBox(height: 8),
            SizedBox(
              width: 450,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // 图标显示
                      Center(
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(color: widget.iconColor.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(30)),
                          child: SizedBox(width: 30, height: 30, child: widget.icon),
                        ),
                      ),
                      const SizedBox(height: 24),
                      // 自定义来源名称输入（仅自定义时显示）
                      if (!widget.isPreset) ...[
                        Text('来源名称', style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500)),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _nameController,
                          decoration: const InputDecoration(hintText: '例如：招商银行、床头柜里的备用金'),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return '请输入来源名称';
                            }
                            if (value.trim().length < 2) {
                              return '来源名称至少需要2个字符';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                      ],
                      // 余额输入
                      Text('当前余额', style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500)),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _balanceController,
                        decoration: InputDecoration(
                          hintText: '0.00',
                          prefixIcon: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Text('¥', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                          ),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入当前余额';
                          }
                          final amount = double.tryParse(value.trim());
                          if (amount == null) {
                            return '请输入有效的金额';
                          }
                          if (amount < 0) {
                            return '余额不能为负数';
                          }
                          if (amount > 999999999.99) {
                            return '余额不能超过999,999,999.99';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      // 保存按钮
                      FButton(
                        onPress: _isLoading ? null : _handleSave,
                        child: _isLoading
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox.square(dimension: 16, child: CircularProgressIndicator(strokeWidth: 2, color: colors.primaryForeground)),
                                  const SizedBox(width: 8),
                                  const Text('保存中...'),
                                ],
                              )
                            : const Text('保存'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final balance = Decimal.parse(_balanceController.text.trim());

      // 验证预设类型的presetKey
      if (widget.isPreset && (widget.presetKey == null || widget.presetKey!.isEmpty)) {
        throw Exception('预设来源的presetKey不能为空');
      }

      final cashSource = CashSource(
        balance: balance,
        sourceType: widget.isPreset ? CashSourceType.preset : CashSourceType.custom,
        presetKey: widget.isPreset ? widget.presetKey : null,
        customName: widget.isPreset ? null : _nameController.text.trim(),
      );

      widget.onSourceAdded(cashSource);

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('保存失败：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: context.theme.colors.destructive));
  }
}
