import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import 'package:decimal/decimal.dart';
import '../providers/cash_pocket_provider.dart';


class CashPocketCard extends ConsumerStatefulWidget {
  const CashPocketCard({super.key});

  @override
  ConsumerState<CashPocketCard> createState() => _CashPocketCardState();
}

class _CashPocketCardState extends ConsumerState<CashPocketCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _balanceAnimation;
  Decimal _previousBalance = Decimal.zero;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _balanceAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // 初始加载摘要数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSummary();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _animateBalanceChange(Decimal newBalance) {
    final newBalanceDouble = newBalance.toDouble();
    final previousBalanceDouble = _previousBalance.toDouble();

    if (_previousBalance != newBalance) {
      _balanceAnimation = Tween<double>(
        begin: previousBalanceDouble,
        end: newBalanceDouble,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));

      _animationController.reset();
      _animationController.forward();
      _previousBalance = newBalance;
    }
  }

  /// 加载摘要信息（用于profile卡片显示）
  Future<void> _loadSummary() async {
    try {
      // 使用现金来源API加载数据
      await ref.read(cashSourceProvider.notifier).loadCashSources();
    } catch (e) {
      // 静默处理错误，不影响UI显示
      log('加载现金来源失败: $e');
    }
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: '¥ ',
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  String _formatLastUpdated(DateTime? dateTime) {
    if (dateTime == null) return '从未更新';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final cashSourceState = ref.watch(cashSourceProvider);

    // 触发余额动画
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animateBalanceChange(cashSourceState.effectiveTotalBalance);
    });

    return GestureDetector(
      onTap: () {
        // 点击时跳转到现金来源管理页面
        context.push('/forecast', extra: 1);
      },
      child: FCard(
        child: Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colors.primary,
                colors.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '我的现金口袋',
                        style: theme.typography.xl.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white.withValues(alpha: 0.7),
                    size: 16,
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // 余额显示
              if (cashSourceState.isLoading)
                Row(
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '加载中...',
                      style: theme.typography.sm.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                )
              else if (cashSourceState.error != null)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '加载失败',
                      style: theme.typography.xl.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      cashSourceState.error!,
                      style: theme.typography.sm.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                )
              else
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 动画余额
                    AnimatedBuilder(
                      animation: _balanceAnimation,
                      builder: (context, child) {
                        return Text(
                          _formatCurrency(_balanceAnimation.value),
                          style: theme.typography.xl.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 32,
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 8),

                    // 更新时间和口袋数量
                    Row(
                      children: [
                        Text(
                          '上次更新：${_formatLastUpdated(cashSourceState.lastUpdatedAt)}',
                          style: theme.typography.sm.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                        if (cashSourceState.sources.isNotEmpty) ...[
                          const SizedBox(width: 16),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${cashSourceState.sources.length}个来源',
                              style: theme.typography.sm.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

              const SizedBox(height: 16),

              // 立即更新按钮
              FButton(
                style: FButtonStyle.outline(),
                onPress: () => context.push('/forecast', extra: 1),
                child: Text(
                  '立即更新',
                  style: TextStyle(
                    color: colors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
