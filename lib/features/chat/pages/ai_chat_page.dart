// features/chat/pages/ai_chat_page.dart
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_expense_tracker/features/chat/widgets/ai_message_bubble.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

import '../widgets/chat_input_field.dart';
import '../providers/chat_history_notifier.dart';
import '../models/chat_message.dart';
import '../models/conversation_info.dart';
import '../widgets/tool_message_bubble.dart';
import '../widgets/chat_conversation_drawer.dart';
import '../services/conversation_service.dart';
import '/shared/l10n/app_strings.dart';

class AIChatPage extends ConsumerStatefulWidget {
  final String? conversationId; // 从 GoRouter 获取
  const AIChatPage({super.key, this.conversationId});

  @override
  ConsumerState<AIChatPage> createState() => _AIChatPageState();
}

class _AIChatPageState extends ConsumerState<AIChatPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    log("DEBUG: AIChatPage initState called. conversationId: ${widget.conversationId}");
    // 当 Widget 第一次被插入到树中时，根据传入的 conversationId 加载初始数据。
    // 使用 addPostFrameCallback 确保在第一帧渲染后安全地与 Provider 交互。
    WidgetsBinding.instance.addPostFrameCallback((_) {
      log("AIChatPage(initState): Initializing with conversationId: ${widget.conversationId}");
      _loadDataForCurrentRoute();
    });
  }

  @override
  void didUpdateWidget(covariant AIChatPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    log("DEBUG: AIChatPage didUpdateWidget called. oldConversationId: ${oldWidget.conversationId}, newConversationId: ${widget.conversationId}");
    // 当 GoRouter 改变路由导致这个 Widget 的参数变化时，此方法被调用。
    // 我们比较新旧 conversationId。
    // 当路由变化导致 Widget 更新时 (例如从 /ai/123 导航到 /ai/456)
    // 重新加载数据
    if (widget.conversationId != oldWidget.conversationId) {
      log("AIChatPage(didUpdateWidget): conversationId changed from ${oldWidget.conversationId} to ${widget.conversationId}. Reloading data.");
      _loadDataForCurrentRoute();
    }
  }

  void _loadDataForCurrentRoute() {
    log("DEBUG: _loadDataForCurrentRoute called. Current widget.conversationId: ${widget.conversationId}");
    final notifier = ref.read(chatHistoryProvider.notifier);
    // 如果路由提供了 conversationId，就加载它。
    // 添加一个检查，避免在ID相同时重复加载。
    if (widget.conversationId != null) {
      // 如果有ID，加载对应的会话
      notifier.loadConversation(widget.conversationId!);
    } else {
      // 如果没有ID，检查当前 Notifier 中是否有会话，如果没有则创建新的
      // 如果路由是 /ai (conversationId 为 null)，
      // 检查 Notifier 是否已经有一个会话。如果没有，则创建一个新的。
      // 这处理了“新建聊天”和应用首次启动进入 /ai 的情况。
      final currentConvId = ref.read(chatHistoryProvider).currentConversationId;
      if (currentConvId == null) {
        notifier.createNewConversation();
      }
    }
  }

  // 显示原生 Drawer 侧边栏
  void _showSidebar() {
    log("DEBUG: _showSidebar called, opening drawer");
    _scaffoldKey.currentState?.openDrawer();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final chatHistoryState = ref.watch(chatHistoryProvider);
    final messages = chatHistoryState.messages;
    final chatHistoryNotifier = ref.read(chatHistoryProvider.notifier);

    return Scaffold(
      key: _scaffoldKey,
      resizeToAvoidBottomInset: true,
      drawer: const ChatConversationDrawer(),
      backgroundColor: theme.colors.background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: colors.background,
        foregroundColor: colors.foreground,
        // 左侧菜单按钮，使用 Forui Sheet
        leading: FButton.icon(style: FButtonStyle.ghost(), onPress: _showSidebar, child: const Icon(FIcons.alignLeft)),
        title: GestureDetector(
          onTap: _showSidebar, // 点击标题也可以打开侧边栏
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                chatHistoryState.currentConversationTitle ?? AppStrings.get('newChat'),
                style: theme.typography.xl.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const Icon(FIcons.chevronRight, size: 18),
            ],
          ),
        ),
        centerTitle: true,
        actions: [
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: () {
              // TODO: 实现编辑会话标题的功能
            },
            child: const Icon(FIcons.pencil),
          ),
        ],
      ),
      body: Column(
        children: [
          // --- DEBUG INFO START ---
          // Container(
          //   padding: const EdgeInsets.all(8.0),
          //   color: Colors.yellow.withOpacity(0.2),
          //   child: Column(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       Text('Debug Info:', style: TextStyle(fontWeight: FontWeight.bold)),
          //       Text('Conversation ID: ${widget.conversationId ?? 'N/A'}'),
          //       Text('Is Loading History: ${chatHistoryState.isLoadingHistory}'),
          //       Text('History Error: ${chatHistoryState.historyError ?? 'None'}'),
          //       Text('Messages Count: ${messages.length}'),
          //     ],
          //   ),
          // ),
          // --- DEBUG INFO END ---
          Expanded(
            child: messages.isEmpty && !chatHistoryState.isLoadingHistory
                ? Center(
                    child: Text(
                      chatHistoryState.historyError != null
                          ? '${AppStrings.get('loadingFailed')}: ${chatHistoryState.historyError}'
                          : AppStrings.get('noMessagesToDisplay'),
                      style: theme.typography.base,
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    reverse: true,
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      final message = messages[messages.length - 1 - index];

                      // 根据消息发送者和类型选择合适的消息气泡
                      switch (message.sender) {
                        case MessageSender.user:
                          return _UserMessageBubble(message: message);

                        case MessageSender.tool:
                          return ToolMessageBubble(message: message);

                        case MessageSender.ai:
                        case MessageSender.assistant:
                        default:
                          return AiMessageBubble(
                            message: message,
                            onFeedback: (feedbackStatus) {
                              chatHistoryNotifier.updateAIFeedback(message.id, feedbackStatus);
                            },
                            onShare: () {
                              // TODO: 实现分享功能
                              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('分享功能开发中...'), duration: Duration(seconds: 1)));
                            },
                          );
                      }
                    },
                  ),
          ),
          SafeArea(
            top: false,
            child: ChatInputField(onSendMessage: chatHistoryNotifier.addUserMessageAndGetResponse),
          ),
        ],
      ),
    );
  }
}

// 用户消息气泡组件
class _UserMessageBubble extends StatelessWidget {
  final ChatMessage message;

  const _UserMessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.8),
        margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 12.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        decoration: BoxDecoration(
          color: colors.primary,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(4),
            bottomLeft: Radius.circular(20),
            bottomRight: Radius.circular(20),
          ),
        ),
        // child: Text(
        //   message.text,
        //   style: TextStyle(
        //     color: colors.primaryForeground,
        //     fontSize: 16,
        //     height: 1.4,
        //   ),
        // ),
        // 如果只是纯文本:
        child: Text(
          message.text,
          style: theme.typography.base.copyWith(
            // 使用主题文本样式
            color: colors.primaryForeground, // 主色对应的前景色
            fontSize: 15, // 调整字体大小
            height: 1.4, // 行高
          ),
        ),
      ),
    );
  }
}
