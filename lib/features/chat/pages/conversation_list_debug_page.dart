import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../services/conversation_service.dart';
import '../models/conversation_info.dart';

class ConversationListDebugPage extends ConsumerStatefulWidget {
  const ConversationListDebugPage({super.key});

  @override
  ConsumerState<ConversationListDebugPage> createState() => _ConversationListDebugPageState();
}

class _ConversationListDebugPageState extends ConsumerState<ConversationListDebugPage> {
  List<ConversationInfo>? conversations;
  String? error;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    setState(() {
      isLoading = true;
      error = null;
      conversations = null;
    });

    try {
      log('ConversationListDebugPage: Starting manual API call...');
      final service = ref.read(conversationServiceProvider);
      final result = await service.getConversationList();
      log('ConversationListDebugPage: Manual API call successful, got ${result.length} conversations');
      
      setState(() {
        conversations = result;
        isLoading = false;
      });
    } catch (e, stackTrace) {
      log('ConversationListDebugPage: Manual API call failed: $e');
      log('ConversationListDebugPage: Stack trace: $stackTrace');
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('会话列表调试'),
        actions: [
          IconButton(
            onPressed: _loadConversations,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Provider 测试部分
            Text(
              'Provider 测试:',
              style: theme.typography.lg.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Consumer(
              builder: (context, ref, child) {
                final conversationListAsync = ref.watch(refreshableConversationListProvider);
                return conversationListAsync.when(
                  data: (data) {
                    log('ConversationListDebugPage: Provider returned ${data.length} conversations');
                    return Card(
                      color: Colors.green.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('✅ Provider 成功', style: TextStyle(color: Colors.green.shade700, fontWeight: FontWeight.bold)),
                            Text('会话数量: ${data.length}'),
                            if (data.isNotEmpty) ...[
                              const SizedBox(height: 8),
                              Text('前3个会话:'),
                              ...data.take(3).map((conv) => Text('- ${conv.id}: ${conv.title}')),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                  loading: () {
                    log('ConversationListDebugPage: Provider is loading...');
                    return Card(
                      color: Colors.blue.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            const SizedBox(width: 8),
                            Text('🔄 Provider 加载中...', style: TextStyle(color: Colors.blue.shade700)),
                          ],
                        ),
                      ),
                    );
                  },
                  error: (err, stack) {
                    log('ConversationListDebugPage: Provider error: $err');
                    return Card(
                      color: Colors.red.shade50,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('❌ Provider 错误', style: TextStyle(color: Colors.red.shade700, fontWeight: FontWeight.bold)),
                            Text('错误: $err'),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 手动调用测试部分
            Text(
              '手动调用测试:',
              style: theme.typography.lg.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            if (isLoading)
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      const SizedBox(width: 8),
                      Text('🔄 手动调用加载中...', style: TextStyle(color: Colors.blue.shade700)),
                    ],
                  ),
                ),
              )
            else if (error != null)
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('❌ 手动调用错误', style: TextStyle(color: Colors.red.shade700, fontWeight: FontWeight.bold)),
                      Text('错误: $error'),
                    ],
                  ),
                ),
              )
            else if (conversations != null)
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('✅ 手动调用成功', style: TextStyle(color: Colors.green.shade700, fontWeight: FontWeight.bold)),
                      Text('会话数量: ${conversations!.length}'),
                      if (conversations!.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text('前3个会话:'),
                        ...conversations!.take(3).map((conv) => Text('- ${conv.id}: ${conv.title}')),
                      ],
                    ],
                  ),
                ),
              ),
              
            const SizedBox(height: 24),
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: FButton(
                    onPress: _loadConversations,
                    child: const Text('重新手动调用'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FButton(
                    style: FButtonStyle.outline(),
                    onPress: () {
                      ref.read(conversationListRefreshProvider.notifier).state++;
                    },
                    child: const Text('刷新 Provider'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
