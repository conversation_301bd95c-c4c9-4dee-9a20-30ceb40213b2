// features/chat/widgets/ai_thinking_indicator.dart
import 'package:flutter/material.dart';
import 'dart:math' as math;

class AiThinkingIndicator extends StatefulWidget {
  final double dotSize;
  final Color? dotColor;
  final Duration duration;
  final String thinkingText;

  const AiThinkingIndicator({
    super.key,
    this.dotSize = 7.0,
    this.dotColor,
    this.duration = const Duration(milliseconds: 1400),
    this.thinkingText = "AI正在思考...", // 可自定义思考文本
  });

  @override
  State<AiThinkingIndicator> createState() => _AiThinkingIndicatorState();
}

class _AiThinkingIndicatorState extends State<AiThinkingIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildDot(int index, Color color) {
    // 每个点动画的延迟和持续时间相对于总时长的比例
    final double begin = (index * 0.2).clamp(0.0, 1.0); // 0.0, 0.2, 0.4
    final double end = (begin + 0.4).clamp(0.0, 1.0);    // 0.4, 0.6, 0.8

    // 使用 ScaleTransition 和 FadeTransition 组合
    return ScaleTransition(
      scale: Tween<double>(begin: 0.4, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(begin, end, curve: Curves.easeInOutSine),
        ),
      ),
      child: FadeTransition(
        opacity: Tween<double>(begin: 0.5, end: 1.0).animate( // 从半透明到完全不透明再回去
          CurvedAnimation(
            parent: _controller,
            curve: Interval(begin, end, curve: Curves.easeInOutSine),
          ),
        ),
        child: Container(
          width: widget.dotSize,
          height: widget.dotSize,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.dotColor ?? Theme.of(context).colorScheme.primary.withOpacity(0.8);

    // 如果有思考文本，则显示文本 + 动画点
    if (widget.thinkingText.isNotEmpty) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            widget.thinkingText,
            style: TextStyle(
              fontSize: 13, // 稍小一点的字体
              color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(width: 6),
          _buildDot(0, color),
          SizedBox(width: widget.dotSize * 0.4),
          _buildDot(1, color),
          SizedBox(width: widget.dotSize * 0.4),
          _buildDot(2, color),
        ],
      );
    } else {
      // 否则只显示动画点
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDot(0, color),
          SizedBox(width: widget.dotSize * 0.4),
          _buildDot(1, color),
          SizedBox(width: widget.dotSize * 0.4),
          _buildDot(2, color),
        ],
      );
    }
  }
}