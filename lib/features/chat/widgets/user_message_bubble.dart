import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // 用于格式化时间戳
import '../models/chat_message.dart'; // 确保路径相对于你的项目结构是正确的

class UserMessageBubble extends StatelessWidget {
  final ChatMessage message;

  const UserMessageBubble({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool isLightMode = theme.brightness == Brightness.light;

    return Align(
      alignment: Alignment.centerRight, // 用户消息居右
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.80, // 限制最大宽度
        ),
        margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 12.0), // 统一垂直边距
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary, // 使用主题的主色调
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(22),    // 更圆润的角
            topRight: Radius.circular(6),     // 右上角稍微尖一点，形成对话指向感
            bottomLeft: Radius.circular(22),
            bottomRight: Radius.circular(22),
          ),
          boxShadow: [ // 添加细微的阴影以提升质感
            BoxShadow(
              color: Colors.black.withOpacity(isLightMode ? 0.08 : 0.15),
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end, // 内部文本和时间戳也靠右
          mainAxisSize: MainAxisSize.min, // Column 包裹内容
          children: [
            Text(
              message.text,
              style: TextStyle(
                color: theme.colorScheme.onPrimary, // 主题主色调上的文本颜色
                fontSize: 16.5,                      // 略微调整字体大小
                height: 1.45,                        // 调整行高
              ),
            ),
            if (message.timestamp != null) ...[ // 如果有时间戳则显示
              const SizedBox(height: 5),
              Text(
                DateFormat('HH:mm').format(message.timestamp ?? DateTime.now()),
                style: TextStyle(
                  color: theme.colorScheme.onPrimary.withOpacity(0.75), // 时间戳颜色稍浅
                  fontSize: 11.5,
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }
}