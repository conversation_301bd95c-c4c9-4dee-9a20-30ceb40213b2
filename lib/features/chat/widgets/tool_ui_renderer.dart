import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import '../models/tool_ui_component.dart';
import 'tool_components/transaction_receipt_card.dart';
import 'tool_components/transaction_feed.dart';
import 'tool_components/data_table_card.dart';
import 'tool_components/chart_card.dart';
import 'tool_components/summary_card.dart';
import 'tool_components/custom_card.dart';

/// 工具UI组件渲染器
class ToolUIRenderer extends StatelessWidget {
  final List<ToolUIComponent> components;
  final EdgeInsets? padding;

  const ToolUIRenderer({
    super.key,
    required this.components,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (components.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 4.0), // 减少垂直间距，优化移动端布局
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: components.map((component) => _buildComponent(context, component)).toList(),
      ),
    );
  }

  Widget _buildComponent(BuildContext context, ToolUIComponent component) {
    try {
      Widget result;
      switch (component.type) {
        case ToolUIComponentType.transactionReceiptCard:
          result = _buildTransactionReceiptCard(context, component);
          break;
        case ToolUIComponentType.transactionFeed:
          try {
            result = TransactionFeed(
              props: TransactionFeedProps.fromJson(component.props),
            );
          } catch (e) {
            result = FCard(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('TransactionFeed解析失败', style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(height: 8),
                  Text('错误: $e'),
                  SizedBox(height: 8),
                    Text('组件名称: ${component.name}'),
                    Text('数据键: ${component.props.keys.toList()}'),
                  ],
                ),
              ),
            );
          }
          break;
        case ToolUIComponentType.dataTable:
          result = DataTableCard(
            props: _createDataTableProps(component),
          );
          break;
        case ToolUIComponentType.chartCard:
          final chartProps = _createChartCardProps(component);
          result = ChartCard(
            props: chartProps,
          );
          break;
        case ToolUIComponentType.summaryCard:
          result = SummaryCard(
            props: SummaryCardProps.fromJson(component.props),
          );
          break;
        case ToolUIComponentType.listCard:
        case ToolUIComponentType.imageCard:
        case ToolUIComponentType.customCard:
        // 特殊处理：检查是否是TransactionReceiptCard但被标记为customCard
          if (component.name == 'TransactionReceiptCard') {
            try {
              final props = TransactionReceiptCardProps.fromJson(component.props);
              result = TransactionReceiptCard(props: props);
            } catch (e) {
              // 如果解析失败，回退到CustomCard
              result = CustomCard(
                title: component.name,
                props: component.props,
              );
            }
          } else {
            result = CustomCard(
              title: component.name,
              props: component.props,
            );
          }
          break;
      }

      return Container(
        margin: const EdgeInsets.only(bottom: 8.0),
        child: result,
      );
    } catch (e) {
      // 如果组件渲染失败，显示错误卡片
      return _buildErrorCard(context, component, e);
    }
  }

  Widget _buildErrorCard(BuildContext context, ToolUIComponent component, dynamic error) {
    final theme = context.theme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    FIcons.triangleAlert,
                    color: theme.colors.destructive,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '组件渲染错误',
                    style: theme.typography.sm.copyWith(
                      color: theme.colors.destructive,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '组件类型: ${component.name}',
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '错误信息: ${error.toString()}',
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 创建图表卡片属性，适配不同的数据格式
  ChartCardProps _createChartCardProps(ToolUIComponent component) {
    final props = component.props;

    // 从组件名称推断图表类型
    String chartType = '';
    switch (component.name.toLowerCase()) {
      case 'piechart':
        chartType = 'pie';
        break;
      case 'barchart':
        chartType = 'bar';
        break;
      case 'linechart':
        chartType = 'line';
        break;
      case 'radarchart':
        chartType = 'radar';
        break;
      default:
        chartType = props['chartType'] as String? ?? 'pie';
    }

    return ChartCardProps(
      title: props['title'] as String? ?? '图表',
      chartType: chartType,
      chartData: props, // 直接传递整个props作为chartData
      chartOptions: props['chartOptions'] as Map<String, dynamic>? ?? {},
    );
  }

  /// 创建数据表格属性，适配不同的数据格式
  DataTableProps _createDataTableProps(ToolUIComponent component) {
    final props = component.props;

    List<String> headers = [];
    List<List<String>> rows = [];

    try {
      // 检查数据格式：新格式 (headers + items) 还是旧格式 (headers + rows)
      if (props.containsKey('items') && props.containsKey('headers')) {
        // 新格式：headers 是对象数组，items 是数据数组
        final headersList = props['headers'] as List<dynamic>? ?? [];
        final itemsList = props['items'] as List<dynamic>? ?? [];

        // 解析表头
        headers = headersList.map((header) {
          if (header is Map<String, dynamic>) {
            return header['label'] as String? ?? header['key'] as String? ?? '';
          } else {
            return header.toString();
          }
        }).toList();

        // 获取表头的 key 列表
        final headerKeys = headersList.map((header) {
          if (header is Map<String, dynamic>) {
            return header['key'] as String? ?? '';
          } else {
            return header.toString();
          }
        }).toList();

        // 解析数据行
        rows = itemsList.map((item) {
          if (item is Map<String, dynamic>) {
            return headerKeys.map((key) {
              return item[key]?.toString() ?? '';
            }).toList();
          } else {
            return [item.toString()];
          }
        }).toList();

      } else {
        // 旧格式：直接的 headers 和 rows
        headers = (props['headers'] as List<dynamic>?)
            ?.map((e) => e.toString())
            .toList() ?? [];

        rows = (props['rows'] as List<dynamic>?)
            ?.map((row) => (row as List<dynamic>)
                .map((cell) => cell.toString())
                .toList())
            .toList() ?? [];
      }
    } catch (e) {
      log("Error parsing table data: $e");
      // 如果解析失败，返回空数据
      headers = ['错误'];
      rows = [['数据解析失败: $e']];
    }

    return DataTableProps(
      title: props['title'] as String? ?? '数据表格',
      headers: headers,
      rows: rows,
      styling: props['styling'] as Map<String, dynamic>? ?? {},
      pagination: null, // 暂时移除分页功能
    );
  }

  /// 构建交易收据卡片，使用新的TransactionReceiptCard组件
  Widget _buildTransactionReceiptCard(BuildContext context, ToolUIComponent component) {
    try {
      // 从 component.props 中提取数据，安全地处理类型转换
      final rawProps = component.props;

      // 解析details数组中的信息
      final details = rawProps['details'] as List<dynamic>? ?? [];
      final detailsMap = <String, String>{};
      for (final detail in details) {
        if (detail is Map<String, dynamic>) {
          final label = detail['label'] as String?;
          final value = detail['value'] as String?;
          if (label != null && value != null) {
            detailsMap[label] = value;
          }
        }
      }

      // 提取字段，优先使用直接字段，然后从details中查找
      final category = _safeGetString(rawProps, 'category', '') != ''
          ? _safeGetString(rawProps, 'category', '')
          : detailsMap['分类'] ?? '其他';

      // 处理金额：后端返回的可能是字符串格式如"200,000.00"
      String amountStr = '0';
      final amountValue = rawProps['amount'];
      if (amountValue is String) {
        amountStr = amountValue;
      } else if (amountValue is num) {
        amountStr = amountValue.toString();
      }

      final date = _safeGetString(rawProps, 'date', '') != ''
          ? _safeGetString(rawProps, 'date', '')
          : detailsMap['时间'] ?? '';

      final type = _safeGetString(rawProps, 'type', 'expense');
      final status = _safeGetString(rawProps, 'status', 'success');
      final currency = _safeGetString(rawProps, 'currency', 'CNY');

      // 根据类型确定颜色
      final amountColor = type == 'income' ? '#22c55e' : '#ef4444';

      // 提取标签
      final tags = (rawProps['tags'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [];

      // 创建TransactionReceiptCardProps
      final props = TransactionReceiptCardProps(
        status: status,
        title: category,
        amount: amountStr,
        currency: currency,
        amountColor: amountColor,
        details: [], // 不显示详情列表
        tags: tags,
        transactionId: component.transactionId,
        category: category,
        timestamp: date.isNotEmpty ? date : DateTime.now().toIso8601String(),
      );

      return TransactionReceiptCard(props: props);
    } catch (e) {
      // 如果解析失败，返回错误提示
      return FCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('解析交易数据失败: $e'),
        ),
      );
    }
  }

  /// 导航到交易详情页
  void _navigateToTransactionDetail(BuildContext context, String transactionId) {
    context.pushNamed(
      'transactionDetail',
      pathParameters: {'transactionId': transactionId},
    );
  }

  /// 显示交易操作菜单
  void _showTransactionActions(BuildContext context, String transactionId) {
    final theme = context.theme;
    final colors = theme.colors;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return Container(
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '交易操作',
                style: theme.typography.xl.copyWith(
                  color: colors.foreground,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // 查看详情
              _buildActionItem(
                theme,
                colors,
                Icons.visibility,
                '查看详情',
                '查看完整的交易信息',
                () {
                  Navigator.pop(sheetContext);
                  _navigateToTransactionDetail(context, transactionId);
                },
              ),

              // 编辑交易
              _buildActionItem(
                theme,
                colors,
                Icons.edit,
                '编辑交易',
                '修改交易信息',
                () {
                  Navigator.pop(sheetContext);
                  _editTransaction(context, transactionId);
                },
              ),

              // 删除交易
              _buildActionItem(
                theme,
                colors,
                Icons.delete,
                '删除交易',
                '永久删除此交易记录',
                () {
                  Navigator.pop(sheetContext);
                  _deleteTransaction(context, transactionId);
                },
                isDestructive: true,
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  /// 构建操作项
  Widget _buildActionItem(
    FThemeData theme,
    FColors colors,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: colors.muted.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: isDestructive ? Colors.red.shade600 : colors.foreground,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.typography.base.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDestructive ? Colors.red.shade600 : colors.foreground,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 16,
              color: colors.mutedForeground,
            ),
          ],
        ),
      ),
    );
  }

  /// 编辑交易
  void _editTransaction(BuildContext context, String transactionId) {
    // TODO: 导航到交易编辑页面
    // context.push('/transaction/$transactionId/edit');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('编辑交易: $transactionId')),
    );
  }

  /// 删除交易
  void _deleteTransaction(BuildContext context, String transactionId) {

    showFDialog(
      context: context,
      builder: (dialogContext, style, animation) => FDialog(
        style: style.call,
        animation: animation,
        title: const Text('确认删除'),
        body: const Text('您确定要删除此条交易记录吗？此操作无法撤销。'),
        actions: [
          FButton(
            style: FButtonStyle.outline(),
            onPress: () => Navigator.of(dialogContext).pop(),
            child: const Text('取消'),
          ),
          FButton(
            style: FButtonStyle.destructive(),
            onPress: () {
              Navigator.of(dialogContext).pop();
              // TODO: 实现删除逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已删除交易: $transactionId')),
              );
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 安全地获取字符串值
  String _safeGetString(Map<String, dynamic> map, String key, String defaultValue) {
    final value = map[key];
    if (value == null) return defaultValue;
    if (value is String) return value;
    return value.toString();
  }

  /// 安全地获取数字值
  double _safeGetNumber(Map<String, dynamic> map, String key, double defaultValue) {
    final value = map[key];
    if (value == null) return defaultValue;
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }
}
