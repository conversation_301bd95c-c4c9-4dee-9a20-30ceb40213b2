// features/chat/widgets/ai_message_bubble.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gpt_markdown/gpt_markdown.dart';
import 'package:forui/forui.dart';
import 'package:flutter/services.dart';
import '../models/chat_message.dart'; // 确保路径正确
import 'tool_ui_renderer.dart';

class AiMessageBubble extends ConsumerStatefulWidget {
  final ChatMessage message;
  final Function(AIFeedbackStatus) onFeedback;
  final VoidCallback onShare;
  final VoidCallback? onRefresh;

  const AiMessageBubble({super.key, required this.message, required this.onFeedback, required this.onShare, this.onRefresh});

  @override
  ConsumerState<AiMessageBubble> createState() => _AiMessageBubbleState();
}

class _AiMessageBubbleState extends ConsumerState<AiMessageBubble> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(duration: const Duration(milliseconds: 350), vsync: this);
    _fadeAnimation = CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic);
    _fadeController.forward(); // 消息出现时淡入
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Align(
        alignment: Alignment.centerLeft, // 消息块在屏幕水平方向居中
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width, // 消息最大宽度
          ),
          padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
          //margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0), // 消息间的垂直间距
          child: Column(
            mainAxisSize: MainAxisSize.min, // Column 高度包裹内容
            crossAxisAlignment: CrossAxisAlignment.start, // 内容（文本、按钮）左对齐
            children: [
              // 1. 工具组件优先渲染在最上面（如果存在）
              _buildToolComponents(context),
              // 2. AI回复消息体内容（文本和指示器）
              _buildMessageContentAndIndicator(context),
              // 3. 操作按钮保持在最下方，与消息体左边缘对齐
              if (widget.message.streamingStatus == StreamingStatus.completed || widget.message.streamingStatus == StreamingStatus.error)
                Padding(
                  // 确保按钮与消息体左边缘完全对齐，只添加顶部间距
                  padding: const EdgeInsets.only(top: 8.0),
                  child: _buildActionButtons(context),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建工具组件部分
  Widget _buildToolComponents(BuildContext context) {
    // 确保工具组件列表不为空
    if (widget.message.toolComponents.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      child: ToolUIRenderer(
        components: widget.message.toolComponents,
      ),
    );
  }

  Widget _buildMessageContentAndIndicator(BuildContext context) {
    // 如果消息文本为空，并且状态是正在连接或正在流式传输（但还未收到任何文本），
    // 则显示一个简化的加载指示器。
    if (widget.message.text.isEmpty &&
        (widget.message.streamingStatus == StreamingStatus.connecting ||
            (widget.message.streamingStatus == StreamingStatus.streaming && widget.message.isTyping))) {
      // isTyping 为 true 表示在等待数据块
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: _buildStreamingIndicator(context, showText: true), // 连接中或初始思考中，显示文字
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Markdown 文本内容区域
        // AnimatedSize 会使文本增长时有平滑动画，其内部对齐由父Column的CrossAxisAlignment.start控制
        AnimatedSize(
          duration: const Duration(milliseconds: 50),
          curve: Curves.easeOut,
          alignment: Alignment.topLeft, // 动画对齐方式
          child: widget.message.text.isEmpty && _shouldShowStreamingIndicator()
              ? const SizedBox(height: 20) // 初始空文本时的占位符，避免指示器跳动
              : GptMarkdownTheme(
                  gptThemeData: _gptThemeData(context),
                  child: GptMarkdown(
                    widget.message.text,
                    style: TextStyle(
                      fontSize: 15,
                      color: context.theme.colors.foreground, // 使用前景文字颜色
                      height: 1.5, // 增加行高以便阅读
                    ),
                  ),
                ),
        ),
        // 如果需要，显示流式指示器 (例如 “AI正在思考...”)
        if (_shouldShowStreamingIndicator())
          Padding(
            // 给指示器和上方文本一点间距
            padding: const EdgeInsets.only(top: 4.0),
            child: _buildStreamingIndicator(context),
          ),
      ],
    );
  }

  // 从 Forui Theme 获取 Markdown 主题配置
  GptMarkdownThemeData _gptThemeData(BuildContext context) {
    final theme = context.theme;
    return GptMarkdownThemeData(
      brightness: Theme.of(context).brightness,
      h1: TextStyle(fontSize: 28, color: theme.colors.primary, fontWeight: FontWeight.bold),
      // ... 其他自定义 Markdown 样式
    );
  }

  // 判断是否应该显示流式指示器 (例如 "AI正在思考..." 或打字动画)
  bool _shouldShowStreamingIndicator() {
    // 当消息正在连接，或正在流式传输，并且 isTyping 为 true（表示在等待数据块或思考）
    // 或者即使 isTyping 为 false，但状态是 streaming（表示正在打字动画中，但也可以显示一个小的动画点）
    // 这里简化为：只要不是 completed 或 error，且 isTyping 为 true，就显示完整的指示器。
    // 打字动画本身（点点点）由 _buildTypingAnimation 控制，不依赖此函数决定是否创建。
    // 此函数主要控制是否显示 "AI正在思考..." 这类文本指示。
    return widget.message.isTyping &&
        (widget.message.streamingStatus == StreamingStatus.connecting || widget.message.streamingStatus == StreamingStatus.streaming);
  }

  Widget _buildStreamingIndicator(BuildContext context, {bool showText = true}) {
    final theme = context.theme;
    String indicatorText;

    // 根据消息的流状态决定指示器文本
    switch (widget.message.streamingStatus) {
      case StreamingStatus.connecting:
        indicatorText = 'AI正在连接...';
        break;
      case StreamingStatus.streaming:
        // 如果 isTyping 为 true，表示在数据块之间，可以显示“正在思考”
        // 如果 isTyping 为 false，表示正在打字动画，理论上不应该到这里显示文本指示器，
        // 除非 _shouldShowStreamingIndicator 逻辑允许。
        // 为了安全，我们主要依赖 isTyping。
        indicatorText = widget.message.isTyping ? 'AI正在思考' : 'AI正在回复';
        break;
      default: // 通常是 isTyping 为 true 但非 streaming/connecting 状态， fallback
        indicatorText = 'AI正在处理...';
    }

    // 如果不显示文本，则只返回打字动画
    if (!showText && !widget.message.isTyping) return const SizedBox.shrink();

    return Padding(
      // 给指示器自身一些轻微的垂直边距
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min, // Row宽度包裹内容
        children: [
          SizedBox(
            // 固定打字动画的尺寸
            width: 20, // 略微增大尺寸以便容纳新动画
            height: 16,
            child: _buildTypingAnimation(context),
          ),
          if (showText) ...[
            // 如果需要显示文本
            const SizedBox(width: 8),
            Text(
              indicatorText,
              style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground, fontStyle: FontStyle.italic),
            ),
          ],
        ],
      ),
    );
  }

  // 构建循环播放的打字动画（点点点）
  Widget _buildTypingAnimation(BuildContext context) {
    final theme = context.theme;
    // 使用 TweenAnimationBuilder 来创建持续的动画效果
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      // 动画从0到1
      duration: const Duration(milliseconds: 1200),
      // 动画周期
      key: ValueKey(widget.message.id),
      // 添加 key 确保动画在消息切换时重置
      builder: (context, value, child) {
        return CustomPaint(
          painter: TypingIndicatorPainter(
            progress: value, // 将动画值传递给painter
            color: theme.colors.primary.withValues(alpha: 0.8), // 使用主题颜色
          ),
          size: const Size(20, 16), // 明确告知 CustomPaint 尺寸
        );
      },
      onEnd: () {
        // 当动画结束时，如果组件仍挂载且仍应显示指示器，则通过 setState 重建以重新播放动画
        if (mounted && _shouldShowStreamingIndicatorForDots()) {
          setState(() {}); // 重新触发动画循环
        }
      },
    );
  }

  // 一个辅助判断，专门给 _buildTypingAnimation 的 onEnd 使用，
  // 决定点点点动画是否需要持续。
  // 只要消息不是最终完成或错误状态，都可能需要点点点。
  bool _shouldShowStreamingIndicatorForDots() {
    return widget.message.streamingStatus == StreamingStatus.connecting || widget.message.streamingStatus == StreamingStatus.streaming;
  }

  Widget _buildActionButtons(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    // 确保按钮组与消息体左边缘完全对齐
    // 使用 MainAxisAlignment.start 和 MainAxisSize.min 来实现紧凑的左对齐布局
    return Row(
      mainAxisAlignment: MainAxisAlignment.start, // 按钮从左开始排列
      mainAxisSize: MainAxisSize.min, // Row宽度包裹内容
      children: [
        FButton.icon(
          style: FButtonStyle.ghost(),
          onPress: () {
            Clipboard.setData(ClipboardData(text: widget.message.text));
            // TODO: 使用 Forui 的 Toast 系统
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('内容已复制')),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Icon(FIcons.copy, color: colors.mutedForeground, size: 16),
          ),
        ),
        const SizedBox(width: 4),
        FButton.icon(
          style: FButtonStyle.ghost(),
          onPress: () => widget.onFeedback(AIFeedbackStatus.liked),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Icon(
              FIcons.thumbsUp,
              color: widget.message.feedbackStatus == AIFeedbackStatus.liked ? colors.primary : colors.mutedForeground,
              size: 16,
            ),
          ),
        ),
        const SizedBox(width: 4),
        FButton.icon(
          style: FButtonStyle.ghost(),
          onPress: () => widget.onFeedback(AIFeedbackStatus.disliked),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Icon(
              FIcons.thumbsDown,
              color: widget.message.feedbackStatus == AIFeedbackStatus.disliked ? colors.primary : colors.mutedForeground,
              size: 16,
            ),
          ),
        ),
        if (widget.onRefresh != null) ...[
          const SizedBox(width: 4),
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: widget.onRefresh,
            child: Padding(
              padding: const EdgeInsets.all(6),
              child: Icon(FIcons.refreshCw, color: colors.mutedForeground, size: 16),
            ),
          ),
        ],
        const SizedBox(width: 4),
        FButton.icon(
          style: FButtonStyle.ghost(),
          onPress: widget.onShare,
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Icon(FIcons.share2, color: colors.mutedForeground, size: 16),
          ),
        ),
      ],
    );
  }
}

// 更新 TypingIndicatorPainter 实现新的动画效果 (例如：顺序点亮或呼吸效果)
class TypingIndicatorPainter extends CustomPainter {
  final double progress; // 0.0 to 1.0
  final Color color;
  final int dotCount;

  TypingIndicatorPainter({
    required this.progress,
    required this.color,
    this.dotCount = 3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    final centerY = size.height / 2;

    // 根据可用宽度动态计算点的半径和间距
    // (3 * dotCount - 1) 是一个基于点半径为间距的因子
    final double dotRadius = size.width / (3 * dotCount); // 稍微调整因子以获得更合适的点大小
    final double spacing = dotRadius * 0.8; // 点之间的间距略小于半径

    // 计算所有点和间距占用的总宽度
    final double totalWidthOfGroup = (dotCount * dotRadius * 2) + ((dotCount - 1) * spacing);
    // 计算第一个点的X坐标，以使整个点组在Size内居中
    final double startX = (size.width - totalWidthOfGroup) / 2 + dotRadius;

    for (int i = 0; i < dotCount; i++) {
      final double dotX = startX + i * (dotRadius * 2 + spacing);

      // --- 实现顺序点亮效果 ---
      // 将 progress (0-1) 映射到每个点的激活阶段
      // 每个点在 progress 的一段特定区间内达到最大亮度
      final double phasePerDot = 1.0 / dotCount;
      final double dotCenterPhase = (i + 0.5) * phasePerDot; // 每个点动画的中心点

      // 计算当前 progress 相对于此点中心相位的距离
      // 距离越近，点越亮 (使用高斯函数或类似的平滑曲线)
      double distance = (progress - dotCenterPhase).abs();
      // 为了循环效果，处理 progress 接近 0 或 1 时的情况
      if (progress < phasePerDot && i == dotCount -1) { // 当progress很小时，最后一个点可能也应该亮
        distance = (progress + (1-dotCenterPhase)).abs();
      } else if (progress > (1-phasePerDot) && i == 0) { // 当progress很大时，第一个点可能也应该亮
        distance = (progress - (1+dotCenterPhase)).abs();
      }


      // 基于距离计算透明度 (简单的线性衰减，也可以用更平滑的曲线)
      // 最大距离是0.5 (当progress在两个点的正中间时)
      // 或者一个点周期的长度 phasePerDot
      double maxDistance = phasePerDot * 0.8; // 点影响范围
      double opacity = 1.0 - (distance / maxDistance);
      opacity = opacity.clamp(0.2, 1.0); // 透明度限制在 0.2 到 1.0 之间

      paint.color = color.withValues(alpha: opacity);
      canvas.drawCircle(Offset(dotX, centerY), dotRadius * (0.8 + opacity * 0.2) , paint); // 点大小也略微变化
    }
  }

  @override
  bool shouldRepaint(covariant TypingIndicatorPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.dotCount != dotCount;
  }
}