// features/chat/widgets/conversation_item_skeleton.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class ConversationItemSkeleton extends StatelessWidget {
  const ConversationItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final placeholderColor = theme.colors.muted; // 使用一个柔和的占位符颜色

    return Container(
      width: double.infinity,
      height: 48, // 与真实的 ShadButton 高度保持一致
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        // 骨架项的背景可以与真实按钮的 ghost 样式背景类似
        color: theme.colors.background, // 或其他合适的背景色
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 模拟标题行
          Container(
            height: 14, // 模拟文本高度
            width: 150, // 模拟标题长度
            decoration: BoxDecoration(
              color: placeholderColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          // 模拟日期行
          Container(
            height: 10, // 模拟文本高度
            width: 80,  // 模拟日期长度
            decoration: BoxDecoration(
              color: placeholderColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}