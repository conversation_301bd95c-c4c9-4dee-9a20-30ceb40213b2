import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:shimmer/shimmer.dart';
import 'package:intl/intl.dart';
import '../providers/chat_history_notifier.dart';
import '../services/conversation_service.dart';
import 'conversation_item_skeleton.dart';

class ChatConversationDrawer extends ConsumerWidget {
  const ChatConversationDrawer({super.key});

  // 构建骨架屏列表
  Widget _buildSkeletonList(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: 8,
      separatorBuilder: (context, index) => const SizedBox(height: 4),
      itemBuilder: (context, index) {
        return const ConversationItemSkeleton();
      },
    );
  }

  // 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    final theme = context.theme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: theme.colors.muted,
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                FIcons.messageCircle,
                size: 32,
                color: theme.colors.mutedForeground,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '没有历史会话',
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '开始新的对话吧！',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 构建错误状态
  Widget _buildErrorState(BuildContext context, Object error, WidgetRef ref) {
    final theme = context.theme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: theme.colors.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                FIcons.x,
                size: 32,
                color: theme.colors.destructive,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.destructive,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: theme.colors.border),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    ref.invalidate(conversationListProvider);
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Text(
                      '重试',
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.foreground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建会话列表
  Widget _buildConversationList(BuildContext context, List<dynamic> conversations, String? currentConversationId, WidgetRef ref) {
    final theme = context.theme;
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: conversations.length,
      separatorBuilder: (context, index) => const SizedBox(height: 4),
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        final isSelected = conversation.id == currentConversationId;

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: theme.colors.primary.withValues(alpha: 0.2))
                : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // 如果点击的不是当前会话
                if (!isSelected) {
                  context.goNamed(
                    'conversation',
                    pathParameters: {
                      'conversationId': conversation.id,
                    },
                  );
                }
                // 关闭抽屉
                Navigator.of(context).pop();
              },
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // 会话图标
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? theme.colors.primary.withValues(alpha: 0.2)
                            : theme.colors.muted,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        FIcons.messageCircle,
                        size: 16,
                        color: isSelected
                            ? theme.colors.primary
                            : theme.colors.mutedForeground,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 会话信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            conversation.title,
                            style: theme.typography.sm.copyWith(
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? theme.colors.primary
                                  : theme.colors.foreground,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            DateFormat('M月d日 HH:mm', 'zh_CN').format(conversation.updatedAt),
                            style: theme.typography.xs.copyWith(
                              color: theme.colors.mutedForeground,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final conversationListAsync = ref.watch(conversationListProvider);
    final currentConversationId = ref.watch(chatHistoryProvider.select((state) => state.currentConversationId));

    return Drawer(
      backgroundColor: theme.colors.background, // 使用 Forui 的背景色
      shape: const RoundedRectangleBorder(), // 移除圆角效果
      child: SafeArea(
        child: Column(
          children: [
            // 顶部：标题和新建聊天按钮
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题行
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'AI 聊天',
                          style: theme.typography.lg.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colors.foreground,
                          ),
                        ),
                      ),
                      FButton.icon(
                        style: FButtonStyle.ghost(),
                        onPress: () {
                          // 创建新聊天
                          ref.read(chatHistoryProvider.notifier).createNewConversation();
                          // 关闭抽屉
                          Navigator.of(context).pop();
                          // 导航到新的聊天根路径
                          context.go('/ai');
                        },
                        child: const Icon(FIcons.plus, size: 18),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // 搜索框
                  FTextField(
                    hint: '搜索会话...',
                    onChange: (value) {
                      // TODO: 实现搜索功能
                    },
                  ),
                ],
              ),
            ),

          // 中间：会话列表
          Expanded(
            child: conversationListAsync.when(
              data: (conversations) {
                if (conversations.isEmpty) {
                  return _buildEmptyState(context);
                }
                return _buildConversationList(context, conversations, currentConversationId, ref);
              },
              loading: () => _buildSkeletonList(context),
              error: (err, stack) => _buildErrorState(context, err, ref),
            ),
          ),
            // 底部：用户信息
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: theme.colors.border),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // 导航到个人资料页
                    context.go('/profile');
                    // 关闭抽屉
                    Navigator.of(context).pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: theme.colors.primary,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            FIcons.user,
                            size: 16,
                            color: theme.colors.primaryForeground,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '老计 伏枥',
                                style: theme.typography.sm.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '查看个人资料',
                                style: theme.typography.xs.copyWith(
                                  color: theme.colors.mutedForeground,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          FIcons.chevronRight,
                          size: 16,
                          color: theme.colors.mutedForeground,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
