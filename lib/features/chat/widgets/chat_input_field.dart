// features/chat/widgets/chat_input_field.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/chat_input_notifier.dart';
import '../providers/chat_input_state.dart';
import '/shared/l10n/app_strings.dart';

class ChatInputField extends ConsumerStatefulWidget {
  final Future<void> Function(String) onSendMessage;

  const ChatInputField({super.key, required this.onSendMessage});

  @override
  ConsumerState<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends ConsumerState<ChatInputField> {
  late final TextEditingController _textController;
  final FocusNode _focusNode = FocusNode();
  late final provider = chatInputNotifierProviderFamily(widget.onSendMessage);

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();

    ref.listenManual(provider.select((s) => s.text), (
      previousText,
      currentText,
    ) {
      if (!mounted) return;
      if (_textController.text != currentText) {
        final currentSelection = _textController.selection;
        _textController.text = currentText;
        try {
          if (currentSelection.baseOffset <= currentText.length &&
              currentSelection.extentOffset <= currentText.length) {
            _textController.selection = currentSelection;
          } else {
            _textController.selection = TextSelection.fromPosition(
              TextPosition(offset: currentText.length),
            );
          }
        } catch (e) {
          _textController.selection = TextSelection.fromPosition(
            TextPosition(offset: currentText.length),
          );
        }
      }
    }, fireImmediately: true);

    ref.listenManual(provider, (
      ChatInputState? previousState,
      ChatInputState currentState,
    ) {
      if (!mounted) return;
      final bool wasShowingError = previousState?.showError ?? false;
      final bool isShowingError = currentState.showError;
      if (isShowingError && !wasShowingError) {
        final errorMessage = currentState.errorMessage;
        if (errorMessage.isNotEmpty) {
          ScaffoldMessenger.of(context).removeCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: '关闭',
                onPressed: () => ref.read(provider.notifier).clearError(),
              ),
            ),
          );
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ref.read(provider.notifier).clearError();
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  String _getHintText(HintType hintType, bool isListening) {
    if (isListening)
      return AppStrings.get('listening'); // isListening 状态优先于 hintType 中的 listening
    switch (hintType) {
      case HintType.normal:
        return AppStrings.get('inputMessage');
      case HintType.listening:
        return AppStrings.get('listening');
      case HintType.aiProcessing:
        return AppStrings.get('aiThinking');
      case HintType.speechNotRecognized:
        return AppStrings.get('speechNotRecognized');
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatInputState = ref.watch(provider);
    final chatInputNotifier = ref.read(provider.notifier);

    IconData currentIcon;
    Color buttonBackgroundColor;
    Color iconColor;
    VoidCallback? currentAction = chatInputNotifier.onMainButtonPressed;

    bool canInteractWithTextField =
        !chatInputState.isListening && !chatInputState.isLoadingResponse;
    bool canUseAddButton =
        !chatInputState.isListening && !chatInputState.isLoadingResponse;

    if (chatInputState.isLoadingResponse) {
      currentIcon = Icons.square_rounded;
      buttonBackgroundColor = Colors.black;
      iconColor = Colors.white;
    } else if (chatInputState.isListening) {
      currentIcon = Icons.square_rounded;
      buttonBackgroundColor = Colors.black;
      iconColor = Colors.white;
    } else if (chatInputState.text.trim().isNotEmpty) {
      currentIcon = Icons.arrow_upward;
      buttonBackgroundColor = Colors.black;
      iconColor = Colors.white;
    } else {
      currentIcon = chatInputState.isSpeechAvailable
          ? Icons.mic_none_outlined
          : Icons.mic_off_outlined;
      buttonBackgroundColor = Theme.of(context).cardColor; // Or a light grey
      iconColor = chatInputState.isSpeechAvailable ? Colors.black : Colors.grey;
      if (!chatInputState.isSpeechAvailable) {
        currentAction = null;
      }
    }

    return Container(
      constraints: const BoxConstraints(minHeight: 58.0),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
      decoration: BoxDecoration(
        color: Theme.of(context).canvasColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24.0),
          topRight: Radius.circular(24.0),
        ),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -1),
            blurRadius: 4.0,
            color: Colors.grey.withOpacity(0.1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: canUseAddButton
                ? () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text("附件功能待实现"),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  }
                : null,
            iconSize: 26,
            color: canUseAddButton
                ? Theme.of(context).textTheme.bodyLarge?.color?.withOpacity(0.7)
                : Colors.grey,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: TextField(
              controller: _textController,
              focusNode: _focusNode,
              enabled: canInteractWithTextField,
              onChanged: chatInputNotifier.onTextChanged,
              decoration: InputDecoration(
                hintText: _getHintText(
                  chatInputState.hintType,
                  chatInputState.isListening,
                ),
                border: InputBorder.none,
                filled: false,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 12.0,
                  horizontal: 8.0,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) {
                if (chatInputState.text.trim().isNotEmpty &&
                    canInteractWithTextField) {
                  chatInputNotifier.onMainButtonPressed();
                }
              },
              minLines: 1,
              maxLines: 5,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: currentAction,
            borderRadius: BorderRadius.circular(22),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                color: buttonBackgroundColor,
                shape: BoxShape.circle,
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(scale: animation, child: child);
                },
                child: Icon(
                  currentIcon,
                  key: ValueKey<IconData>(currentIcon),
                  color: iconColor,
                  size: 20,
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),
        ],
      ),
    );
  }
}
