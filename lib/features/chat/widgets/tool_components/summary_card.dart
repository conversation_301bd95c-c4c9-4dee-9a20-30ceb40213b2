import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../../models/tool_ui_component.dart';

/// 摘要卡片组件
class SummaryCard extends StatelessWidget {
  final SummaryCardProps props;

  const SummaryCard({
    super.key,
    required this.props,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0), // 减少底部边距
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(12.0), // 减少内边距
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                props.title,
                style: theme.typography.lg.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // 摘要内容
              Text(
                props.summary,
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                  height: 1.5,
                ),
              ),
              
              // 摘要项目
              if (props.items.isNotEmpty) ...[
                const SizedBox(height: 16),
                ...props.items.map((item) => _buildSummaryItem(context, item)),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(BuildContext context, SummaryItem item) {
    final theme = context.theme;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          // 图标（如果有）
          if (item.icon != null) ...[
            _buildIcon(context, item.icon!),
            const SizedBox(width: 8),
          ],
          
          // 标签
          SizedBox(
            width: 100,
            child: Text(
              item.label,
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 值
          Expanded(
            child: Text(
              item.value,
              style: theme.typography.sm.copyWith(
                color: item.color != null 
                  ? _parseColor(item.color!) ?? theme.colors.foreground
                  : theme.colors.foreground,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(BuildContext context, String iconName) {
    final theme = context.theme;
    
    // 简单的图标映射
    IconData iconData;
    switch (iconName.toLowerCase()) {
      case 'check':
        iconData = FIcons.check;
        break;
      case 'x':
        iconData = FIcons.x;
        break;
      case 'info':
        iconData = FIcons.info;
        break;
      case 'warning':
        iconData = FIcons.triangleAlert;
        break;
      case 'dollar':
        iconData = FIcons.dollarSign;
        break;
      case 'calendar':
        iconData = FIcons.calendar;
        break;
      case 'user':
        iconData = FIcons.user;
        break;
      default:
        iconData = FIcons.circle;
    }
    
    return Icon(
      iconData,
      size: 16,
      color: theme.colors.mutedForeground,
    );
  }

  Color? _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
