import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'dart:convert';

/// 自定义卡片组件 - 用于处理未知或自定义的组件类型
class CustomCard extends StatelessWidget {
  final String title;
  final Map<String, dynamic> props;

  const CustomCard({
    super.key,
    required this.title,
    required this.props,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0), // 减少底部边距
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(12.0), // 减少内边距
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                children: [
                  Icon(
                    FIcons.package,
                    color: theme.colors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.typography.lg.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const Sized<PERSON>ox(height: 16),
              
              // 属性展示
              _buildPropsDisplay(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPropsDisplay(BuildContext context) {
    final theme = context.theme;
    
    if (props.isEmpty) {
      return Text(
        '无属性数据',
        style: theme.typography.sm.copyWith(
          color: theme.colors.mutedForeground,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '组件属性:',
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colors.muted.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colors.border,
              width: 1,
            ),
          ),
          child: _buildPropsTree(context, props),
        ),
      ],
    );
  }

  Widget _buildPropsTree(BuildContext context, Map<String, dynamic> data, [int depth = 0]) {
    final indent = '  ' * depth;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: data.entries.map((entry) {
        final key = entry.key;
        final value = entry.value;
        
        return Padding(
          padding: EdgeInsets.only(bottom: depth == 0 ? 4.0 : 2.0),
          child: _buildPropertyItem(context, key, value, indent),
        );
      }).toList(),
    );
  }

  Widget _buildPropertyItem(BuildContext context, String key, dynamic value, String indent) {
    final theme = context.theme;
    
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$indent$key:',
            style: theme.typography.sm.copyWith(
              color: theme.colors.foreground,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: _buildPropsTree(context, value, indent.length ~/ 2 + 1),
          ),
        ],
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$indent$key: [${value.length} items]',
            style: theme.typography.sm.copyWith(
              color: theme.colors.foreground,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (value.isNotEmpty) ...[
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: value.asMap().entries.take(3).map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return Text(
                    '$indent  [$index]: ${_formatValue(item)}',
                    style: theme.typography.sm.copyWith(
                      color: theme.colors.mutedForeground,
                      fontSize: 11,
                    ),
                  );
                }).toList(),
              ),
            ),
            if (value.length > 3)
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  '$indent  ... and ${value.length - 3} more',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.mutedForeground,
                    fontSize: 11,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ],
      );
    } else {
      return RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: '$indent$key: ',
              style: theme.typography.sm.copyWith(
                color: theme.colors.foreground,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(
              text: _formatValue(value),
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),
          ],
        ),
      );
    }
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'null';
    if (value is String) return '"$value"';
    if (value is bool) return value.toString();
    if (value is num) return value.toString();
    
    try {
      return jsonEncode(value);
    } catch (e) {
      return value.toString();
    }
  }
}
