import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../../models/tool_ui_component.dart';

/// 数据表格卡片组件
class DataTableCard extends StatelessWidget {
  final DataTableProps props;

  const DataTableCard({
    super.key,
    required this.props,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0), // 减少底部边距
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(12.0), // 减少内边距
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                props.title,
                style: theme.typography.base.copyWith( // 使用更小的字体
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 12), // 减少间距

              // 使用 ShadTable 组件
              _buildShadTable(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShadTable(BuildContext context) {
    final theme = context.theme;
    if (props.rows.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        alignment: Alignment.center,
        child: Text(
          '暂无数据',
          style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground),
        ),
      );
    }

    // 计算表格高度：表头 + 数据行 + 一些边距（移动端优化）
    final rowHeight = 40.0; // 减少每行高度
    final headerHeight = 44.0; // 减少表头高度
    final maxRows = 8; // 移动端最多显示8行，减少占用空间

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowHeight: headerHeight,
        dataRowMinHeight: rowHeight,
        dataRowMaxHeight: rowHeight,
        columns: props.headers.map((header) => DataColumn(
          label: Text(
            header,
            style: theme.typography.sm.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colors.foreground,
            ),
          ),
        )).toList(),
        rows: props.rows.take(maxRows).map((row) => DataRow(
          cells: row.asMap().entries.map((entry) {
            final index = entry.key;
            final cell = entry.value;
            return DataCell(
              Text(
                cell,
                style: theme.typography.sm.copyWith(
                  fontWeight: index == 0 ? FontWeight.w500 : FontWeight.normal,
                  color: theme.colors.foreground,
                ),
              ),
            );
          }).toList(),
        )).toList(),
      ),
    );
  }


}
