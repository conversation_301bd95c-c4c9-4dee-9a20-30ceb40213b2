// features/chat/widgets/tool_components/transaction_feed.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../services/chat_service.dart';
import '/shared/config/category_config.dart';
import '/shared/l10n/app_strings.dart';

/// 交易数据流组件的属性模型
class TransactionFeedProps {
  final String title;
  final List<TransactionFeedItem> items;
  final Map<String, dynamic> searchContext;
  final bool hasNextPage;

  const TransactionFeedProps({
    required this.title,
    required this.items,
    required this.searchContext,
    this.hasNextPage = false,
  });

  factory TransactionFeedProps.fromJson(Map<String, dynamic> json) {
    try {
      final items = json['items'] as List<dynamic>? ?? [];

      final parsedItems = <TransactionFeedItem>[];
      for (int i = 0; i < items.length; i++) {
        try {
          final item = TransactionFeedItem.fromJson(items[i] as Map<String, dynamic>);
          parsedItems.add(item);
        } catch (e) {
          print('ERROR: Failed to parse transaction item $i: $e');
          // 继续处理其他项目，不让一个错误影响整个列表
        }
      }

      return TransactionFeedProps(
        title: json['title'] as String? ?? '交易记录',
        items: parsedItems,
        searchContext: json['searchContext'] as Map<String, dynamic>? ?? {},
        hasNextPage: json['hasNextPage'] as bool? ?? false,
      );
    } catch (e) {
      print('ERROR: TransactionFeedProps.fromJson failed: $e');
      rethrow;
    }
  }
}

/// 交易数据流项目模型
class TransactionFeedItem {
  final String id;
  final String type;
  final String timestamp;
  final String category;
  final String? categoryId;
  final String? categoryText;
  final String description;
  final String iconUrl;
  final double amount;
  final String location;
  final List<String> tags;
  final String paymentMethod;
  final String? paymentMethodText;
  final int commentCount;
  final bool isShared;

  final Map<String, dynamic>? display;

  const TransactionFeedItem({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.category,
    this.categoryId,
    this.categoryText,
    required this.description,
    required this.iconUrl,
    required this.amount,
    required this.location,
    required this.tags,
    required this.paymentMethod,
    this.paymentMethodText,
    required this.commentCount,
    required this.isShared,

    this.display,
  });

  factory TransactionFeedItem.fromJson(Map<String, dynamic> json) {
    try {
      // 解析金额，支持字符串和数字格式
      double parseAmount(dynamic value) {
        if (value == null) return 0.0;
        if (value is num) return value.toDouble();
        if (value is String) {
          final cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
          return double.tryParse(cleanValue) ?? 0.0;
        }
        return 0.0;
      }

      return TransactionFeedItem(
        id: json['id']?.toString() ?? '',
        type: json['type'] as String? ?? 'expense',
        timestamp: json['timestamp'] as String? ?? DateTime.now().toIso8601String(),
        category: json['categoryText'] as String? ?? json['category'] as String? ?? '其他',
        categoryId: json['categoryId'] as String?,
        categoryText: json['categoryText'] as String?,
        description: json['description'] as String? ?? '',
        iconUrl: json['iconUrl'] as String? ?? '',
        amount: parseAmount(json['amount']),
        location: json['location'] as String? ?? '',
        tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
        paymentMethod: json['paymentMethod'] as String? ?? '',
        paymentMethodText: json['paymentMethodText'] as String?,
        commentCount: json['commentCount'] as int? ?? 0,
        isShared: json['isShared'] as bool? ?? false,

        display: json['display'] as Map<String, dynamic>?,
      );
    } catch (e) {
      print('ERROR: TransactionFeedItem.fromJson failed: $e');
      rethrow;
    }
  }
}

/// 交易数据流组件
class TransactionFeed extends ConsumerStatefulWidget {
  final TransactionFeedProps props;

  const TransactionFeed({
    super.key,
    required this.props,
  });

  @override
  ConsumerState<TransactionFeed> createState() => _TransactionFeedState();
}

class _TransactionFeedState extends ConsumerState<TransactionFeed> {
  final ScrollController _scrollController = ScrollController();
  List<TransactionFeedItem> _allItems = [];
  bool _isLoadingMore = false;
  bool _hasNextPage = false;
  Map<String, dynamic> _searchContext = {};

  @override
  void initState() {
    super.initState();
    _allItems = List.from(widget.props.items);
    _hasNextPage = widget.props.hasNextPage;
    _searchContext = Map.from(widget.props.searchContext);

    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // 更严格的条件检查，防止无限循环
    if (_scrollController.hasClients &&
        _scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100 &&
        !_isLoadingMore &&
        _hasNextPage &&
        _scrollController.position.maxScrollExtent > 0) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasNextPage) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // 获取下一页数据
      final currentPage = int.tryParse(_searchContext['page']?.toString() ?? '1') ?? 1;
      final nextPage = currentPage + 1;

      // 构建搜索参数
      final searchParams = Map<String, dynamic>.from(_searchContext);
      searchParams['page'] = nextPage.toString();

      // 调用API获取下一页数据
      final chatService = ref.read(chatServiceProvider);
      final response = await chatService.searchTransactions(searchParams);
      
      // 检查响应格式，可能是直接的items数组，也可能是包装在componentForUi中
      List<dynamic> items = [];
      bool hasNextPage = false;
      Map<String, dynamic> newSearchContext = {};

      if (response.containsKey('componentForUi') && response['componentForUi'] is Map) {
        final componentData = response['componentForUi'] as Map<String, dynamic>;
        final props = componentData['props'] as Map<String, dynamic>? ?? {};
        items = props['items'] as List<dynamic>? ?? [];
        hasNextPage = props['hasNextPage'] as bool? ?? false;
        newSearchContext = props['searchContext'] as Map<String, dynamic>? ?? {};
      } else if (response.containsKey('items') && response['items'] is List) {
        items = response['items'] as List<dynamic>;
        hasNextPage = response['hasNextPage'] as bool? ?? false;
        newSearchContext = response['searchContext'] as Map<String, dynamic>? ?? {};
      }

      if (items.isNotEmpty) {
        final newItems = items
            .map((item) => TransactionFeedItem.fromJson(item as Map<String, dynamic>))
            .toList();

        setState(() {
          _allItems.addAll(newItems);
          _hasNextPage = hasNextPage;
          if (newSearchContext.isNotEmpty) {
            _searchContext = newSearchContext;
          } else {
            _searchContext['page'] = nextPage.toString();
          }
        });
      } else {
        setState(() {
          _hasNextPage = false;
        });
      }
    } catch (e) {
      // 错误处理
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载更多数据失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: FCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              widget.props.title,
              style: theme.typography.xl2.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // 交易列表或空状态
            SizedBox(
              height: 400, // 固定高度，支持滚动
              child: _allItems.isEmpty
                ? _buildEmptyState(context, colors, theme)
                : ListView.builder(
                    controller: _scrollController,
                    itemCount: _allItems.length + (_isLoadingMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index >= _allItems.length) {
                        // 加载更多指示器
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      final item = _allItems[index];
                      return _buildTransactionItem(context, item);
                    },
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建空状态
  Widget _buildEmptyState(BuildContext context, FColors colors, FThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FIcons.search,
            size: 64,
            color: colors.mutedForeground.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            AppStrings.get('noTransactionsFound'),
            style: theme.typography.xl.copyWith(
              color: colors.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppStrings.get('tryAdjustingSearch'),
            style: theme.typography.sm.copyWith(
              color: colors.mutedForeground.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(BuildContext context, TransactionFeedItem item) {
    final theme = context.theme;
    final colors = theme.colors;

    // 解析时间戳
    DateTime? timestamp = DateTime.tryParse(item.timestamp);

    /// 获取分类显示名称
    String getCategoryDisplayName() {
      // 优先使用服务端本地化的分类名称
      if (item.categoryText != null && item.categoryText!.isNotEmpty) {
        return item.categoryText!;
      }
      // 回退到客户端分类ID映射
      if (item.categoryId != null) {
        return CategoryConfig.getCategoryName(item.categoryId);
      }
      // 最后回退到原有的category字段
      return item.category;
    }

    /// 获取金额显示文本
    String getAmountDisplayText() {
      // 优先使用API返回的display字段
      if (item.display != null && item.display!['fullString'] != null) {
        return item.display!['fullString'] as String;
      }

      // 向后兼容：使用原有的amount字段格式化
      final isExpense = item.type.toLowerCase() == 'expense';
      final currencyFormat = NumberFormat.currency(locale: 'zh_CN', symbol: '¥', decimalDigits: 2);

      return '${isExpense ? '-' : '+'}${currencyFormat.format(item.amount.abs())}';
    }

    /// 获取金额颜色
    Color getAmountColor() {
      // 统一处理：支出用红色，收入用主色调
      return item.type.toLowerCase() == 'expense'
          ? colors.destructive
          : colors.primary;
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: FCard(
        child: InkWell(
          onTap: () {
            context.pushNamed(
              'transactionDetail',
              pathParameters: {'transactionId': item.id},
            );
          },
          child: Row(
            children: [
              // 类别图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: colors.primary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  item.categoryId != null
                    ? CategoryConfig.getCategoryIcon(item.categoryId)
                    : CategoryConfig.getCategoryIconByName(item.category),
                  size: 20,
                  color: colors.mutedForeground,
                ),
              ),
              const SizedBox(width: 12),

              // 中间内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      getCategoryDisplayName(),
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      timestamp != null
                        ? timeago.format(timestamp, locale: 'zh_CN')
                        : '未知时间',
                      style: theme.typography.sm.copyWith(
                        color: colors.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),

              // 金额
              Text(
                getAmountDisplayText(),
                style: theme.typography.base.copyWith(
                  fontWeight: FontWeight.bold,
                  color: getAmountColor(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
