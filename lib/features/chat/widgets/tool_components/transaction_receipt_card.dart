import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:intl/intl.dart';
import '../../../../shared/models/action_item_model.dart';
import '../../../../shared/widgets/dialogs/action_bottom_sheet.dart';
import '../../models/tool_ui_component.dart';

/// 交易收据卡片组件
class TransactionReceiptCard extends ConsumerWidget {
  final TransactionReceiptCardProps props;

  const TransactionReceiptCard({
    super.key,
    required this.props,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;

    // 解析金额和货币 - 简化判断逻辑
    // 检查amountColor：#ef4444是红色(支出)，#22c55e是绿色(收入)
    final isExpense = props.amountColor.contains('#ef4444') ||
                     props.amountColor.toLowerCase().contains('red');

    final currencyFormat = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: '¥',
      decimalDigits: 2,
    );

    final amount = double.tryParse(props.amount) ?? 0.0;

    // 解析时间戳
    DateTime? timestamp;
    if (props.timestamp != null) {
      timestamp = DateTime.tryParse(props.timestamp!);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部：类别图标、类别名称、时间、更多操作按钮
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 2.0),
                  child: FAvatar.raw(
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getCategoryIcon(),
                        color: colors.mutedForeground,
                        size: 24,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        props.category ?? props.title,
                        style: theme.typography.lg.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        timestamp != null
                          ? timeago.format(timestamp, locale: 'zh_CN')
                          : '刚刚',
                        style: theme.typography.sm.copyWith(
                          color: colors.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
                if (props.transactionId != null)
                  FButton.icon(
                    style: FButtonStyle.ghost(),
                    onPress: () => _showTransactionActions(context, ref),
                    child: Icon(
                      FIcons.ellipsis,
                      color: colors.mutedForeground,
                      size: 20,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24),

            // 金额
            Center(
              child: Text(
                '${isExpense ? '-' : '+'}${currencyFormat.format(amount)}',
                style: theme.typography.xl2.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isExpense
                      ? colors.destructive
                      : colors.primary,
                ),
              ),
            ),

            // 标签
            if (props.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: props.tags.map((tag) => _buildTag(context, tag)).toList(),
              ),
            ],
          ],
        ),
        ),
      ),
    );
  }

  // 根据类别获取图标
  IconData _getCategoryIcon() {
    final category = props.category?.toLowerCase() ?? '';

    if (category.contains('餐饮') || category.contains('食物')) {
      return FIcons.utensils;
    } else if (category.contains('交通') || category.contains('出行')) {
      return FIcons.car;
    } else if (category.contains('购物') || category.contains('商店')) {
      return FIcons.shoppingBag;
    } else if (category.contains('娱乐') || category.contains('游戏')) {
      return FIcons.gamepad2;
    } else if (category.contains('医疗') || category.contains('健康')) {
      return FIcons.heart;
    } else if (category.contains('教育') || category.contains('学习')) {
      return FIcons.graduationCap;
    } else if (category.contains('工资') || category.contains('收入')) {
      return FIcons.banknote;
    } else {
      return FIcons.receipt;
    }
  }

  // 显示操作菜单
  void _showTransactionActions(BuildContext context, WidgetRef ref) {
    if (props.transactionId == null) return;

    final List<ActionItem> primaryActions = [];
    final List<ActionItem> destructiveActions = [];

    primaryActions.add(ActionItem(
      title: '查看详情',
      icon: FIcons.eye,
      onTap: () {
        Navigator.of(context).pop();
        context.pushNamed(
          'transactionDetail',
          pathParameters: {'transactionId': props.transactionId!},
        );
      },
    ));

    primaryActions.add(ActionItem(
      title: '编辑',
      icon: FIcons.pencil,
      onTap: () {
        Navigator.of(context).pop();
        // TODO: 导航到编辑页面
      },
    ));

    destructiveActions.add(ActionItem(
      title: '删除',
      icon: FIcons.trash2,
      onTap: () {
        Navigator.of(context).pop();
        // TODO: 实现删除功能
      },
    ));

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return ActionBottomSheet(
          actions: primaryActions,
          destructiveActions: destructiveActions.isNotEmpty ? destructiveActions : null,
        );
      },
    );
  }

  Widget _buildTag(BuildContext context, String tag) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: colors.secondary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        tag,
        style: theme.typography.sm.copyWith(
          color: colors.secondaryForeground,
        ),
      ),
    );
  }
}
