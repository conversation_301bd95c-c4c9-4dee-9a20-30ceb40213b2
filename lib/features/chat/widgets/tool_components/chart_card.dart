import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../models/tool_ui_component.dart';
import '../../utils/chart_colors.dart';

/// 图表卡片组件
class ChartCard extends StatelessWidget {
  final ChartCardProps props;

  const ChartCard({super.key, required this.props});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0), // 减少底部边距
      child: FCard(
        child: Padding(
          padding: const EdgeInsets.all(12.0), // 减少内边距
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(props.title, style: theme.typography.lg.copyWith(fontWeight: FontWeight.w600)),

              const SizedBox(height: 16),

              // 图表
              SizedBox(height: 200, child: _buildChart(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChart(BuildContext context) {
    // 如果没有指定chartType，根据组件名称推断
    String chartType = props.chartType.toLowerCase();
    if (chartType.isEmpty) {
      // 从props中推断图表类型
      if (props.chartData.containsKey('sections')) {
        chartType = 'pie';
      } else if (props.chartData.containsKey('data')) {
        chartType = 'bar';
      }
    }

    switch (chartType) {
      case 'pie':
        return _buildPieChart(context);
      case 'bar':
        return _buildBarChart(context);
      case 'line':
        return _buildLineChart(context);
      case 'radar':
        return _buildRadarChart(context);
      default:
        return _buildUnsupportedChart(context);
    }
  }

  Widget _buildPieChart(BuildContext context) {
    final theme = context.theme;

    try {
      // 支持两种数据格式：sections 或 data
      List<dynamic>? data = props.chartData['sections'] as List<dynamic>?;
      data ??= props.chartData['data'] as List<dynamic>?;

      if (data == null || data.isEmpty) {
        return _buildNoDataChart(context);
      }

      // 获取总计和单位信息
      final total = props.chartData['total'] as num?;
      final unit = props.chartData['unit'] as String? ?? '';

      final sections = data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value as Map<String, dynamic>;
        final value = (item['value'] as num?)?.toDouble() ?? 0.0;
        final label = item['label'] as String? ?? 'Unknown';

        // 优先使用 colorKey，然后是 color，最后是默认颜色
        Color sectionColor;
        final colorKey = item['colorKey'] as String?;
        final colorString = item['color'] as String?;

        if (colorKey != null) {
          sectionColor = ChartColors.getColorByKey(colorKey);
        } else if (colorString != null) {
          sectionColor = _parseColor(colorString) ?? _getColorForIndex(index);
        } else {
          sectionColor = _getColorForIndex(index);
        }

        return PieChartSectionData(
          color: sectionColor,
          value: value,
          title: '$label\n${value.toStringAsFixed(0)}',
          radius: 60,
          titleStyle: theme.typography.sm.copyWith(color: Colors.white, fontWeight: FontWeight.w600, fontSize: 10),
        );
      }).toList();

      // 使用 Stack 来在饼图中心显示总计
      return Stack(
        alignment: Alignment.center,
        children: [
          PieChart(
            PieChartData(
              sections: sections,
              centerSpaceRadius: total != null ? 50 : 40, // 如果有总计，增大中心空间
              sectionsSpace: 2,
              borderData: FlBorderData(show: false),
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  // 可以在这里添加触摸交互逻辑
                },
              ),
            ),
          ),
          // 在中心显示总计
          if (total != null)
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '总计',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.mutedForeground,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${total.toStringAsFixed(0)}$unit',
                  style: theme.typography.lg.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colors.foreground,
                  ),
                ),
              ],
            ),
        ],
      );
    } catch (e) {
      return _buildErrorChart(context, e);
    }
  }

  Widget _buildBarChart(BuildContext context) {
    final theme = context.theme;

    try {
      // 支持分组条形图和简单条形图
      final chartType = props.chartData['type'] as String? ?? 'simple';

      if (chartType == 'grouped') {
        return _buildGroupedBarChart(context);
      }

      final data = props.chartData['data'] as List<dynamic>?;
      if (data == null || data.isEmpty) {
        return _buildNoDataChart(context);
      }

      final barGroups = data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value as Map<String, dynamic>;
        final value = (item['value'] as num?)?.toDouble() ?? 0.0;
        final colorKey = item['colorKey'] as String?;

        Color barColor;
        if (colorKey != null) {
          barColor = ChartColors.getColorByKey(colorKey);
        } else {
          barColor = _getColorForIndex(index);
        }

        return BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              toY: value,
              color: barColor,
              width: 20,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4)),
            ),
          ],
        );
      }).toList();

      return BarChart(
        BarChartData(
          barGroups: barGroups,
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(value.toInt().toString(), style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground, fontSize: 10));
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.length) {
                    final item = data[index] as Map<String, dynamic>;
                    final label = item['label'] as String? ?? '';
                    return Text(
                      label.length > 6 ? '${label.substring(0, 6)}...' : label,
                      style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground, fontSize: 10),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: const FlGridData(show: false),
        ),
      );
    } catch (e) {
      return _buildErrorChart(context, e);
    }
  }

  /// 构建分组条形图
  Widget _buildGroupedBarChart(BuildContext context) {
    final theme = context.theme;

    try {
      final groups = props.chartData['groups'] as List<dynamic>? ?? [];
      if (groups.isEmpty) {
        return _buildNoDataChart(context);
      }

      final barGroups = groups.map((group) {
        final x = (group['x'] as num?)?.toInt() ?? 0;
        final bars = group['bars'] as List<dynamic>? ?? [];

        final barRods = bars.asMap().entries.map((entry) {
          final index = entry.key;
          final bar = entry.value as Map<String, dynamic>;
          final value = (bar['value'] as num?)?.toDouble() ?? 0.0;
          final colorKey = bar['colorKey'] as String?;
          final isOverBudget = bar['is_over_budget'] as bool? ?? false;

          Color barColor;
          if (isOverBudget) {
            barColor = ChartColors.getColorByKey('over_budget');
          } else if (colorKey != null) {
            barColor = ChartColors.getColorByKey(colorKey);
          } else {
            barColor = _getColorForIndex(index);
          }

          return BarChartRodData(
            toY: value,
            color: barColor,
            width: 12,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(2),
              topRight: Radius.circular(2)
            ),
          );
        }).toList();

        return BarChartGroupData(
          x: x,
          barRods: barRods,
          barsSpace: 4,
        );
      }).toList();

      return BarChart(
        BarChartData(
          barGroups: barGroups,
          alignment: BarChartAlignment.spaceAround,
          maxY: _calculateMaxY(groups) * 1.2, // 留出20%的空间
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < groups.length) {
                    final group = groups[index] as Map<String, dynamic>;
                    final label = group['label'] as String? ?? '';
                    return Text(
                      label.length > 6 ? '${label.substring(0, 6)}...' : label,
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                        fontSize: 10
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: theme.typography.sm.copyWith(
                      color: theme.colors.mutedForeground,
                      fontSize: 10
                    )
                  );
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: const FlGridData(show: false),
        ),
      );
    } catch (e) {
      return _buildErrorChart(context, e);
    }
  }

  /// 计算分组条形图的最大Y值
  double _calculateMaxY(List<dynamic> groups) {
    double maxY = 0;
    for (final group in groups) {
      final bars = group['bars'] as List<dynamic>? ?? [];
      for (final bar in bars) {
        final value = (bar['value'] as num?)?.toDouble() ?? 0.0;
        if (value > maxY) maxY = value;
      }
    }
    return maxY;
  }

  // 简化的线图实现
  Widget _buildLineChart(BuildContext context) {
    final theme = context.theme;

    try {
      final xLabels = props.chartData['xAxis']?['labels'] as List<dynamic>? ?? [];
      final series = props.chartData['series'] as List<dynamic>?;

      if (xLabels.isEmpty || series == null || series.isEmpty) {
        return _buildNoDataChart(context);
      }

      final List<LineChartBarData> lineBars = [];

      for (final item in series) {
        final spotsRaw = item['spots'];
        final colorKey = item['colorKey'] as String?;
        final colorStr = item['color'] as String?;
        final seriesName = item['name'] as String? ?? '';

        // 获取颜色
        Color lineColor;
        if (colorKey != null) {
          lineColor = ChartColors.getColorByKey(colorKey);
        } else if (colorStr != null) {
          lineColor = _hexToColor(colorStr);
        } else {
          lineColor = const Color(0xFF36A2EB);
        }

        List<FlSpot> spots = [];

        // 处理新的数据格式：spots 可能是对象数组或简单的 [x, y] 数组
        if (spotsRaw is List) {
          spots = spotsRaw.asMap().entries.map<FlSpot>((entry) {
            final index = entry.key;
            final spotData = entry.value;

            if (spotData is Map<String, dynamic>) {
              // 新格式：包含详细信息的对象
              final total = (spotData['total'] as String?)?.replaceAll(',', '') ?? '0';
              final value = double.tryParse(total) ?? 0.0;
              return FlSpot(index.toDouble(), value);
            } else if (spotData is List && spotData.length >= 2) {
              // 旧格式：[x, y] 数组
              return FlSpot((spotData[0] as num).toDouble(), (spotData[1] as num).toDouble());
            } else {
              return FlSpot(index.toDouble(), 0.0);
            }
          }).toList();
        } else if (spotsRaw is Map<String, dynamic>) {
          // 处理 spots 为对象的情况（如示例中的支出数据）
          spots = spotsRaw.entries.map<FlSpot>((entry) {
            final index = int.tryParse(entry.key) ?? 0;
            final spotData = entry.value as Map<String, dynamic>;
            final total = (spotData['total'] as String?)?.replaceAll(',', '') ?? '0';
            final value = double.tryParse(total) ?? 0.0;
            return FlSpot(index.toDouble(), value);
          }).toList();
        }

        // 为支出线添加填充区域
        final isExpense = seriesName.contains('支出') || colorKey == 'expense';

        lineBars.add(
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: lineColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: isExpense
              ? BarAreaData(
                  show: true,
                  color: lineColor.withValues(alpha: 0.1),
                )
              : BarAreaData(show: false),
          ),
        );
      }

      return LineChart(
        LineChartData(
          lineBarsData: lineBars,
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < xLabels.length) {
                    // return Text(xLabels[index].toString(), style: theme.textTheme.small.copyWith(color: theme.colorScheme.mutedForeground, fontSize: 10));
                    return Text(
                      _formatDateLabel(xLabels[index].toString()),
                      style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground, fontSize: 10),
                    );
                  }
                  return const Text('');
                },
                interval: 1,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(value.toInt().toString(), style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground, fontSize: 10));
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: const FlGridData(show: false),
          minX: 0,
          maxX: (xLabels.length - 1).toDouble(),
        ),
      );
    } catch (e) {
      return _buildErrorChart(context, e);
    }
  }

  /// 构建雷达图
  Widget _buildRadarChart(BuildContext context) {
    final theme = context.theme;

    try {
      final ticks = props.chartData['ticks'] as List<dynamic>? ?? [];
      final series = props.chartData['series'] as List<dynamic>? ?? [];
      final dataRange = props.chartData['data_range'] as List<dynamic>? ?? [0, 100];

      if (ticks.isEmpty || series.isEmpty) {
        return _buildNoDataChart(context);
      }

      final minValue = (dataRange[0] as num?)?.toDouble() ?? 0.0;
      final maxValue = (dataRange[1] as num?)?.toDouble() ?? 100.0;

      final List<RadarDataSet> dataSets = series.map((item) {
        final name = item['name'] as String? ?? '';
        final values = (item['values'] as List<dynamic>? ?? [])
            .map((v) => (v as num?)?.toDouble() ?? 0.0)
            .toList();
        final colorKey = item['colorKey'] as String?;

        Color seriesColor;
        if (colorKey != null) {
          seriesColor = ChartColors.getColorByKey(colorKey);
        } else {
          seriesColor = ChartColors.getColorByKey('category_1');
        }

        return RadarDataSet(
          fillColor: seriesColor.withValues(alpha: 0.2),
          borderColor: seriesColor,
          entryRadius: 3,
          dataEntries: values.asMap().entries.map((entry) {
            return RadarEntry(value: entry.value);
          }).toList(),
        );
      }).toList();

      return RadarChart(
        RadarChartData(
          dataSets: dataSets,
          radarBackgroundColor: Colors.transparent,
          borderData: FlBorderData(show: false),
          radarBorderData: BorderSide(color: theme.colors.border, width: 1),
          titlePositionPercentageOffset: 0.2,
          titleTextStyle: theme.typography.sm.copyWith(
            color: theme.colors.foreground,
            fontSize: 12,
          ),
          getTitle: (index, angle) {
            if (index < ticks.length) {
              return RadarChartTitle(text: ticks[index].toString());
            }
            return const RadarChartTitle(text: '');
          },
          tickCount: 5,
          ticksTextStyle: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
            fontSize: 10,
          ),
          tickBorderData: BorderSide(color: theme.colors.border.withValues(alpha: 0.5), width: 1),
          gridBorderData: BorderSide(color: theme.colors.border.withValues(alpha: 0.3), width: 1),
        ),
      );
    } catch (e) {
      return _buildErrorChart(context, e);
    }
  }

  //return _buildUnsupportedChart(context, '线图功能开发中...');
}

String _formatDateLabel(String fullDate) {
  final date = DateTime.tryParse(fullDate);
  if (date == null) return fullDate;
  return '${date.month}/${date.day}'; // 例如 “6/29”
}

Color _hexToColor(String hex) {
  hex = hex.replaceAll('#', '');
  if (hex.length == 6) hex = 'FF$hex';
  return Color(int.parse(hex, radix: 16));
}

Widget _buildNoDataChart(BuildContext context) {
  final theme = context.theme;

  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(FIcons.chartArea, color: theme.colors.mutedForeground, size: 48),
        const SizedBox(height: 8),
        Text('暂无数据', style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground)),
      ],
    ),
  );
}

Widget _buildUnsupportedChart(BuildContext context) {
  final theme = context.theme;

  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(FIcons.info, color: theme.colors.mutedForeground, size: 48),
        const SizedBox(height: 8),
        // Text(message ?? '不支持的图表类型: ${props.chartType}', style: theme.textTheme.small.copyWith(color: theme.colorScheme.mutedForeground)),
      ],
    ),
  );
}

Widget _buildErrorChart(BuildContext context, dynamic error) {
  final theme = context.theme;

  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(FIcons.triangleAlert, color: theme.colors.destructive, size: 48),
        const SizedBox(height: 8),
        Text('图表渲染错误', style: theme.typography.sm.copyWith(color: theme.colors.destructive)),
      ],
    ),
  );
}

Color _getColorForIndex(int index) {
  final colors = [Colors.blue, Colors.green, Colors.orange, Colors.red, Colors.purple, Colors.teal, Colors.pink, Colors.indigo];
  return colors[index % colors.length];
}

Color? _parseColor(String colorString) {
  try {
    if (colorString.startsWith('#')) {
      return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
    }
    return null;
  } catch (e) {
    return null;
  }
}
