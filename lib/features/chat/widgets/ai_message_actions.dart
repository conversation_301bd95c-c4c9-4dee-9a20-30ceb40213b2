// features/chat/widgets/ai_message_actions.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // 假设 ConsumerWidget 会用到
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';

import '../models/chat_message.dart'; // 确保路径正确

class AiMessageActions extends ConsumerWidget { // 继承 ConsumerWidget
  final ChatMessage message;
  final Function(AIFeedbackStatus) onFeedback;
  final VoidCallback onShare;
  final VoidCallback? onRefresh; // 从 AiMessageBubble 传入
  final VoidCallback? onCopy;    // 新增：复制回调

  const AiMessageActions({
    super.key,
    required this.message,
    required this.onFeedback,
    required this.onShare,
    this.onRefresh,
    this.onCopy,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) { // 添加 WidgetRef ref
    final theme = context.theme;
    final colors = theme.colors;

    // 根据消息状态决定是否显示操作按钮
    final bool showActions =
        message.streamingStatus == StreamingStatus.completed ||
            message.streamingStatus == StreamingStatus.error ||
            (message.streamingStatus == StreamingStatus.none && message.sender == MessageSender.ai);

    if (!showActions) {
      return const SizedBox.shrink(); // 如果不显示操作，则返回空SizedBox
    }

    // 2. 重新排列按钮并使用 FButton.icon
    return Padding(
      // 调整整体内边距，使其与消息内容区域的边距协调
      padding: const EdgeInsets.only(top: 6.0, left: 4.0, right: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 复制按钮
          if (onCopy != null) // 仅当提供了回调时显示
            FButton.icon(
              style: FButtonStyle.ghost(),
              onPress: onCopy,
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: Icon(FIcons.copy, color: colors.mutedForeground, size: 15),
              ),
            ),
          if (onCopy != null) const SizedBox(width: 2),

          // 点赞按钮
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: () => onFeedback(AIFeedbackStatus.liked),
            child: Padding(
              padding: const EdgeInsets.all(5),
              child: Icon(
                FIcons.thumbsUp,
                color: message.feedbackStatus == AIFeedbackStatus.liked
                    ? colors.primary // 选中状态颜色
                    : colors.mutedForeground, // 未选中状态颜色
                size: 15,
              ),
            ),
          ),
          const SizedBox(width: 2),

          // 点踩按钮
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: () => onFeedback(AIFeedbackStatus.disliked),
            child: Padding(
              padding: const EdgeInsets.all(5),
              child: Icon(
                FIcons.thumbsDown,
                color: message.feedbackStatus == AIFeedbackStatus.disliked
                    ? colors.primary // 或者 colors.destructive
                    : colors.mutedForeground,
                size: 15,
              ),
            ),
          ),
          const SizedBox(width: 2),

          // 刷新按钮
          if (onRefresh != null) // 仅当提供了回调时显示
            FButton.icon(
              style: FButtonStyle.ghost(),
              onPress: onRefresh,
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: Icon(FIcons.refreshCw, color: colors.mutedForeground, size: 15),
              ),
            ),
          if (onRefresh != null) const SizedBox(width: 2),

          // 分享按钮
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: onShare,
            child: Padding(
              padding: const EdgeInsets.all(5),
              child: Icon(FIcons.share2, color: colors.mutedForeground, size: 15),
            ),
          ),
          const Spacer(), // 将时间戳推到右边

          // 时间戳
          Text(
            DateFormat('HH:mm').format(message.timestamp ?? DateTime.now()),
            style: theme.typography.sm.copyWith( // 3. 使用主题文本样式
              color: colors.mutedForeground.withValues(alpha: 0.8), // 时间戳颜色更柔和
              fontSize: 11, // 字体可以更小
            ),
          ),
        ],
      ),
    );
  }
}