// class ChatMessageList extends ConsumerWidget {
//   const ChatMessageList({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final chatState = ref.watch(chatHistoryProvider);
//     if (chatState.isLoadingHistory) {
//       return const Center(child: CircularProgressIndicator());
//     }
//     if (chatState.historyError != null) {
//       return Center(child: Text(chatState.historyError!));
//     }
//     // ... 返回你的 ListView.builder 来渲染 chatState.messages
//     // 如果列表为空，可以显示一个欢迎界面
//   }
// }