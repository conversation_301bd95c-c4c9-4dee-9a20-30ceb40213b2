import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../models/chat_message.dart';
import 'tool_ui_renderer.dart';

/// 工具消息气泡组件
class ToolMessageBubble extends ConsumerStatefulWidget {
  final ChatMessage message;

  const ToolMessageBubble({
    super.key,
    required this.message,
  });

  @override
  ConsumerState<ToolMessageBubble> createState() => _ToolMessageBubbleState();
}

class _ToolMessageBubbleState extends ConsumerState<ToolMessageBubble> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(duration: const Duration(milliseconds: 350), vsync: this);
    _fadeAnimation = CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic);
    _fadeController.forward(); // 消息出现时淡入
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 12.0), // 减少边距
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 简化的工具信息头部（只在有工具组件时显示）
            if (widget.message.toolComponents.isNotEmpty) ...[
              _buildCompactToolHeader(context),
              const SizedBox(height: 6),
            ],

            // 工具组件渲染
            if (widget.message.toolComponents.isNotEmpty)
              ToolUIRenderer(
                components: widget.message.toolComponents,
                padding: EdgeInsets.zero, // 移除额外的padding
              ),

            // 不显示 summary_for_llm 文本内容，因为这是给AI看的
          ],
        ),
      ),
    );
  }

  Widget _buildCompactToolHeader(BuildContext context) {
    final theme = context.theme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 更小的padding
      decoration: BoxDecoration(
        color: theme.colors.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12), // 更小的圆角
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 简化的工具图标
          _buildCompactToolIcon(context),

          const SizedBox(width: 6),

          // 简化的工具信息
          Text(
            _getToolDisplayName(),
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
              fontWeight: FontWeight.w500,
              fontSize: 12, // 更小的字体
            ),
          ),

          // 执行状态指示器
          if (widget.message.toolSuccess != null) ...[
            const SizedBox(width: 6),
            Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: widget.message.toolSuccess!
                  ? Colors.green
                  : theme.colors.destructive,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildToolHeader(BuildContext context) {
    final theme = context.theme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 工具图标
          _buildToolIcon(context),

          const SizedBox(width: 8),

          // 工具信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getToolDisplayName(),
                style: theme.typography.sm.copyWith(
                  color: theme.colors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (widget.message.toolSuccess != null)
                Text(
                  widget.message.toolSuccess! ? '执行成功' : '执行失败',
                  style: theme.typography.sm.copyWith(
                    color: widget.message.toolSuccess!
                      ? Colors.green
                      : theme.colors.destructive,
                    fontSize: 11,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompactToolIcon(BuildContext context) {
    final theme = context.theme;

    IconData iconData;
    Color iconColor = theme.colors.mutedForeground;

    // 根据工具名称选择图标
    switch (widget.message.toolName?.toLowerCase()) {
      case 'create_transaction':
        iconData = FIcons.plus;
        break;
      case 'get_transactions':
        iconData = FIcons.list;
        break;
      case 'update_transaction':
        iconData = FIcons.pencil;
        break;
      case 'delete_transaction':
        iconData = FIcons.trash2;
        break;
      case 'get_statistics':
        iconData = FIcons.chartArea;
        break;
      case 'search':
        iconData = FIcons.search;
        break;
      default:
        iconData = FIcons.wrench;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 12, // 更小的图标
    );
  }

  Widget _buildToolIcon(BuildContext context) {
    final theme = context.theme;

    IconData iconData;
    Color iconColor = theme.colors.primary;

    // 根据工具名称选择图标
    switch (widget.message.toolName?.toLowerCase()) {
      case 'create_transaction':
        iconData = FIcons.plus;
        break;
      case 'get_transactions':
        iconData = FIcons.list;
        break;
      case 'update_transaction':
        iconData = FIcons.pencil;
        break;
      case 'delete_transaction':
        iconData = FIcons.trash2;
        iconColor = theme.colors.destructive;
        break;
      case 'get_statistics':
        iconData = FIcons.chartArea;
        break;
      case 'search':
        iconData = FIcons.search;
        break;
      default:
        iconData = FIcons.wrench;
    }

    // 根据执行结果调整图标颜色
    if (widget.message.toolSuccess == false) {
      iconColor = theme.colors.destructive;
    } else if (widget.message.toolSuccess == true) {
      iconColor = Colors.green;
    }

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 16,
      ),
    );
  }

  String _getToolDisplayName() {
    switch (widget.message.toolName?.toLowerCase()) {
      case 'create_transaction':
        return '创建交易记录';
      case 'get_transactions':
        return '获取交易记录';
      case 'update_transaction':
        return '更新交易记录';
      case 'delete_transaction':
        return '删除交易记录';
      case 'get_statistics':
        return '获取统计数据';
      case 'search':
        return '搜索';
      default:
        return widget.message.toolName ?? '工具执行';
    }
  }
}
