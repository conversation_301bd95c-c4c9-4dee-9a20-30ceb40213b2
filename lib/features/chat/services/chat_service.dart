// features/chat/services/chat_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/network_client.dart';

class ChatService {
  final NetworkClient _networkClient;

  ChatService(this._networkClient);

  /// 搜索交易记录
  Future<Map<String, dynamic>> searchTransactions(Map<String, dynamic> searchParams) async {
    final response = await _networkClient.request<Map<String, dynamic>>(
      '/transactions/search',
      method: HttpMethod.get,
      queryParameters: searchParams,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          return json;
        }
        throw Exception("API /transactions/search 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );

    // 返回data字段中的内容，这应该包含items和hasNextPage等字段
    if (response.containsKey('data') && response['data'] is Map<String, dynamic>) {
      return response['data'] as Map<String, dynamic>;
    }

    // 如果没有data字段，直接返回响应（兼容性处理）
    return response;
  }
}

// Provider for ChatService
final chatServiceProvider = Provider<ChatService>((ref) {
  final networkClient = ref.read(networkClientProvider);
  return ChatService(networkClient);
});