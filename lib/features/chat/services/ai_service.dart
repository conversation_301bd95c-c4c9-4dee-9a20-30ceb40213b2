import 'dart:convert';
import 'dart:developer';
import 'package:flutter_client_sse/constants/sse_request_type_enum.dart';
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/exceptions/app_exception.dart';
import 'package:flutter_client_sse/flutter_client_sse.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/storage/secure_storage_service.dart'; // 导入存储服务


// 定义一个类或 typedef 来表示解析后的SSE事件，使其更具类型安全性
class ParsedSseEvent {
  final String type;
  final String? conversationId;
  final String? content;
  final String? title;
  final String? messageId;
  // 错误相关字段
  final int? errorCode; // 错误相关字段，改为可空类型
  final String? errorMessage;

  // 工具调用结果相关字段
  final String? toolName;
  final String? toolCallId;
  final bool? isSuccess;
  final Map<String, dynamic>? toolData;
  final Map<String, dynamic>? componentForUi; // 兼容旧格式
  final Map<String, dynamic>? toolComponent; // 新统一格式

  // 消息完成时的ID映射
  final List<Map<String, dynamic>>? toolComponentMappings;

  ParsedSseEvent({
    required this.type,
    this.conversationId,
    this.content,
    this.title,
    this.messageId,
    this.errorCode, // 构造函数也改为可空
    this.errorMessage,
    // 工具调用结果字段
    this.toolName,
    this.toolCallId,
    this.isSuccess,
    this.toolData,
    this.componentForUi,
    this.toolComponent,
    this.toolComponentMappings,
  });

  factory ParsedSseEvent.fromJson(Map<String, dynamic> json) {
    // 解析工具数据
    final toolData = json['data'] as Map<String, dynamic>?;

    // 支持新旧两种格式的工具组件数据
    // 新格式：tool_component (统一格式)
    Map<String, dynamic>? toolComponent = json['tool_component'] as Map<String, dynamic>?;
    toolComponent ??= toolData?['tool_component'] as Map<String, dynamic>?;

    // 旧格式：component_for_ui (兼容) - 支持多种命名格式
    Map<String, dynamic>? componentForUi = json['component_for_ui'] as Map<String, dynamic>?;
    componentForUi ??= json['componentForUi'] as Map<String, dynamic>?; // 驼峰命名
    componentForUi ??= toolData?['component_for_ui'] as Map<String, dynamic>?; // 下划线命名
    componentForUi ??= toolData?['componentForUi'] as Map<String, dynamic>?; // 驼峰命名

    // 工具组件ID映射（用于消息完成时更新临时ID）
    List<Map<String, dynamic>>? toolComponentMappings = json['tool_component_mappings'] as List<Map<String, dynamic>>?;

    return ParsedSseEvent(
      type: json['type'] as String,
      conversationId: json['conversation_id'] as String?,
      content: json['content'] as String?,
      title: json['title'] as String?,
      messageId: json['message_id'] as String?,
      errorCode: json['error_code'] as int?, // 这里也改为可空
      errorMessage: json['message'] as String?,
      // 工具调用结果字段解析
      toolName: json['tool_name'] as String?,
      toolCallId: json['tool_call_id'] as String?,
      isSuccess: json['is_success'] as bool?,
      toolData: toolData,
      componentForUi: componentForUi,
      toolComponent: toolComponent,
      toolComponentMappings: toolComponentMappings,
    );
  }
}

class AIService {

  final SecureStorageService _storageService;

  // 用于跟踪当前活跃的SSE连接
  StreamSubscription? _currentSseSubscription;
  StreamController<ParsedSseEvent>? _currentController;

  AIService(this._storageService);

  Future<Stream<ParsedSseEvent>> streamAIResponse(String userMessage, {String? conversationId}) async {
    // 首先关闭任何现有的连接
    await _closeCurrentConnection();

    // StreamController 现在发出 ParsedSseEvent
    final controller = StreamController<ParsedSseEvent>();
    _currentController = controller;

    // isClosed 用于确保在 controller 关闭后不再尝试发送数据或错误
    bool isClosed = false;

    // 持有 SSEClient 的订阅，以便在 controller 取消时可以取消 SSE 订阅
    StreamSubscription? sseSubscription;

    // --- 直接从存储服务异步获取 Token ---
    final String? token = await _storageService.getToken();

    if (token == null || token.isEmpty) {
      // 如果没有 token，立即返回一个包含错误的流
      controller.addError("用户未登录或Token无效。");
      controller.close();
      return controller.stream;
    }
    final Uri sseUri = Uri.parse("${ApiConstants.sseBaseUrl}${ApiConstants.aiChatSseEndpoint}");
    final Map<String, String> headers = {
      "Accept": "text/event-stream",
      "Cache-Control": "no-cache",
      "Content-Type": "application/json",
      "Authorization": "Bearer $token", // 使用获取到的 token
    };
    controller.onCancel = () {
      // 当外部取消监听 controller.stream 时，我们也应该关闭 SSE 连接
      if (!isClosed) {
        isClosed = true;
        sseSubscription?.cancel(); // 取消 SSE 客户端的订阅
        if (!controller.isClosed) {
          controller.close();
        }
        log("AIService: Stream cancelled by listener, SSE subscription cancelled.");
      }
    };

    // 构建请求体，如果 conversationId 存在则包含它
    final Map<String, dynamic> requestBody = {"message": userMessage};
    if (conversationId != null) {
      requestBody["conversation_id"] = conversationId;
    }

    sseSubscription =
        SSEClient.subscribeToSSE(
          method: SSERequestType.POST,
          url: sseUri.toString(),
          header: headers,
          body: requestBody, // 使用更新后的 body
        ).listen(
          (event) {
            log("AIService: Received raw SSE event - data: ${event.data}");

            // 确保 controller 未关闭，并且事件数据有效
            if (isClosed || event.data == null || event.data!.isEmpty) return;

            try {
              // 先检查是否为纯 [DONE] 标记 (大厂格式)
              if (event.data!.trim() == "[DONE]") {
                if (!isClosed) {
                  isClosed = true;
                  controller.close();
                  sseSubscription?.cancel();
                  log("AIService: Received [DONE], ParsedSseEvent stream closed.");
                }
                return;
              }

              // 解析JSON格式的事件 (统一处理，不依赖event字段)
              final jsonData = jsonDecode(event.data!);

              // 检查是否是认证错误
              if (jsonData is Map<String, dynamic> && jsonData.containsKey('code') && jsonData['code'] == 1000) {
                // 这是认证失败，不要继续处理
                if (!isClosed) {
                  controller.addError("认证失败: ${jsonData['message']}");
                  isClosed = true;
                  controller.close();
                  sseSubscription?.cancel();
                }
                return;
              }

              final parsedEvent = ParsedSseEvent.fromJson(jsonData);

              log("AIService: Received SSE event type: ${parsedEvent.type}");

              // 处理不同类型的事件
              if (parsedEvent.type == '[DONE]') {
                if (!isClosed) {
                  isClosed = true;
                  controller.close();
                  sseSubscription?.cancel();
                  log("AIService: Received JSON [DONE], ParsedSseEvent stream closed.");
                }
              } else {
                controller.add(parsedEvent); // 将解析后的事件添加到流中
                if (parsedEvent.type == 'error') {
                  log("AIService: Received error event: ${parsedEvent.errorCode} - ${parsedEvent.errorMessage}");
                }
              }
            } catch (e) {
              // JSON 解析错误等
              if (!isClosed) {
                log("AIService: Error processing SSE event data: $e");
                // 根据情况决定是否关闭流或发送错误
                // controller.addError(AppException.parsing("SSE数据解析错误: ${e.toString()}"));
              }
            }
          },
          onError: (error) {
            if (isClosed) return;
            isClosed = true;
            log("AIService: SSE stream error: $error");
            if (error is Exception) {
              controller.addError(NetworkException("SSE流错误: ${error.toString()}"));
            } else {
              controller.addError(NetworkException("SSE流发生未知错误: $error"));
            }
            if (!controller.isClosed) controller.close();
            sseSubscription?.cancel(); // 确保出错时也取消订阅
          },
          onDone: () {
            // 当 SSEClient 的流完成时（通常在 [DONE] 或错误后）
            if (!isClosed) {
              isClosed = true;
              if (!controller.isClosed) {
                // 再次检查，避免重复关闭
                controller.close();
              }
              log("AIService: SSE stream underlying connection done.");
            }
            // sseSubscription 会在此时自动取消，无需手动调用 cancel
          },
        );

    // 跟踪当前的SSE订阅
    _currentSseSubscription = sseSubscription;

    return controller.stream;
  }

  /// 关闭当前的SSE连接
  Future<void> _closeCurrentConnection() async {
    if (_currentSseSubscription != null) {
      print("AIService: Closing existing SSE connection");
      await _currentSseSubscription!.cancel();
      _currentSseSubscription = null;
    }

    if (_currentController != null && !_currentController!.isClosed) {
      print("AIService: Closing existing controller");
      _currentController!.close();
      _currentController = null;
    }
  }

  /// 公开方法，用于手动关闭连接
  Future<void> closeConnection() async {
    await _closeCurrentConnection();
  }
}

// 修改 aiServiceProvider 的定义
final aiServiceProvider = Provider<AIService>((ref) {
  // AIService 现在只依赖 SecureStorageService
  final storageService = ref.watch(secureStorageServiceProvider);
  return AIService(storageService);
});