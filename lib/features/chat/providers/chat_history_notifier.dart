// features/chat/providers/chat_history_notifier.dart
import 'dart:async';
import 'dart:developer';
import 'package:flutter_expense_tracker/features/chat/models/paginated_messages.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/chat_history_state.dart';
import '../models/chat_message.dart';
import '../models/tool_ui_component.dart';
import '../services/ai_service.dart';
import '../../../core/network/exceptions/app_exception.dart';
import 'package:characters/characters.dart';
import '../services/conversation_service.dart';
import '../models/conversation_info.dart';
import 'package:collection/collection.dart';

class ChatHistoryNotifier extends StateNotifier<ChatHistoryState> {
  final Ref ref;
  final _uuid = const Uuid();

  StreamSubscription<ParsedSseEvent>? _aiResponseSubscription;

  /// 定时器，用于在等待AI返回【第一个】数据块时，若超时则显示“AI正在思考...”
  Timer? _initialResponseDelayTimer;

  /// 定时器，用于逐字播放AI回复的打字动画。
  Timer? _typingAnimationTimer;

  String _currentStreamingAiMessageId = '';
  final _charQueue = <String>[]; // 存储待显示的字符队列
  String _currentAiMessageTitle = ''; // 用于拼接 title fragments

  /// 标记SSE数据流是否已从服务端结束（通过onDone或onError）。
  bool _streamIsDone = false;

  /// 标记是否已收到来自AI的第一个数据块。这是区分初始等待和后续流式处理的关键。
  bool _isFirstChunkReceived = false;

  /// 标记消息是否已经完成处理，避免重复调用 _handleStreamComplete
  bool _isMessageCompleted = false;

  ChatHistoryNotifier(this.ref) : super(const ChatHistoryState());



  // 加载第一页历史消息
  Future<void> loadConversation(String conversationId) async {
    print("DEBUG: loadConversation called for conversationId: $conversationId");
    // 如果已经是当前会话，并且没有在加载中，则不重复加载
    if (conversationId == state.currentConversationId && !state.isLoadingHistory) {
      print("DEBUG: loadConversation: Conversation $conversationId already loaded or loading. Skipping.");
      log("ChatHistoryNotifier: Conversation $conversationId is already loaded.");
      return;
    }
    // 切换会话前，取消任何正在进行的AI响应
    await _cancelCurrentStreamAndTimers();

    print("DEBUG: loadConversation: Setting state to loading for conversationId: $conversationId");
    // 立即更新UI状态为加载中
    state = state.copyWith(
      currentConversationId: conversationId,
      messages: [], // 清空旧会话的消息
      isLoadingHistory: true,
      historyError: null,
      currentConversationTitle: "加载中...", // 显示临时标题
    );
    try {
      final conversationService = ref.read(conversationServiceProvider);
      // 只调用一次API来获取所有初始数据
      print("DEBUG: loadConversation: Calling getConversationDetail for $conversationId");
      final conversationDetail = await conversationService.getConversationDetail(conversationId);

      if (mounted) {
        log("ChatHistoryNotifier: Loaded conversation $conversationId with ${conversationDetail.messages.length} messages");

        // 统计工具组件数量
        int totalToolComponents = 0;
        for (final msg in conversationDetail.messages) {
          totalToolComponents += msg.toolComponents.length;
        }
        if (totalToolComponents > 0) {
          log("ChatHistoryNotifier: Found $totalToolComponents tool components in historical messages");
        }

        // 由于新的API格式已经将工具组件包含在AI消息中，不需要额外的合并处理
        // 但我们仍然可以应用一些清理逻辑，比如设置默认时间戳
        final processedMessages = _processHistoricalMessages(conversationDetail.messages);

        log("ChatHistoryNotifier: Processed ${processedMessages.length} messages for display");

        state = state.copyWith(
          messages: processedMessages,
          currentConversationTitle: conversationDetail.title,
          isLoadingHistory: false,
          // 由于新API不返回分页信息，我们设置默认值
          historyCurrentPage: 1,
          historyHasMore: false, // 假设一次返回所有消息
        );
        print("DEBUG: ChatHistoryNotifier: State updated for conversation ${conversationDetail.id}.");
        log("ChatHistoryNotifier: Successfully loaded conversation ${conversationDetail.id} with ${processedMessages.length} messages.");
        if (processedMessages.isNotEmpty) {
          log("ChatHistoryNotifier: First message: ${processedMessages.first.text}");
          log("ChatHistoryNotifier: Last message: ${processedMessages.last.text}");
        }
      }
    } catch (e) {
      print("DEBUG: ChatHistoryNotifier: Error loading conversation $conversationId: $e");
      log("ChatHistoryNotifier: Error loading conversation $conversationId: $e");
      if (mounted) {
        state = state.copyWith(
          isLoadingHistory: false,
          historyError: e.toString(),
          currentConversationTitle: "加载失败",
        );
      }
    }
  }


  // 加载更多历史消息的方法 - 已禁用，因为新API返回所有消息
  Future<void> loadMoreMessages() async {
    // 新的API格式一次返回所有消息，不需要分页加载
    log("ChatHistoryNotifier: loadMoreMessages called but disabled - new API returns all messages at once");
    return;
  }

  /// 重置状态以准备一个新的聊天会话。
  Future<void> createNewConversation() async {
    // 取消任何正在进行的AI响应
    await _cancelCurrentStreamAndTimers();

    // 重置状态为一个全新的、没有ID的会话
    // 当用户发送第一条消息时，后端会自动创建会话并返回ID
    state = const ChatHistoryState(
      currentConversationTitle: '新聊天', // 设置默认标题
    );
    log("ChatHistoryNotifier: State reset for a new conversation.");
  }


  /// 用户发送消息，并触发AI响应流程。
  Future<void> addUserMessageAndGetResponse(String text) async {
    if (text.trim().isEmpty) return;

    // 清理上一次的流和状态
    await _cancelCurrentStreamAndTimers();
    _charQueue.clear();
    _currentAiMessageTitle = state.currentConversationTitle ?? "新聊天..."; // 保留或重置标题

    _streamIsDone = false;
    _isFirstChunkReceived = false; // 非常重要：重置此标志
    _isMessageCompleted = false; // 重置消息完成标志

    // 创建用户消息
    final userMessage = ChatMessage(id: _uuid.v4(), text: text, sender: MessageSender.user, timestamp: DateTime.now());

    // 创建AI消息占位符
    _currentStreamingAiMessageId = _uuid.v4();
    final aiMessagePlaceholder = ChatMessage(
      id: _currentStreamingAiMessageId,
      text: "",
      // 初始文本为空
      sender: MessageSender.ai,
      timestamp: DateTime.now(),
      streamingStatus: StreamingStatus.connecting,
      // 初始状态为连接中
      isTyping: true, // 重要：初始设为true，UI会显示“连接中/思考中”的文本指示器
    );

    List<ChatMessage> updatedMessages;
    // 如果是新会话 (currentConversationId 为 null)，则不保留旧消息
    // 后端会在收到这条消息后创建会话并返回ID
    // 关键点：检查 state.currentConversationId
    // 如果是 null，表示这是潜在的新会话的第一条用户消息。
    // 后端会在 AIService 调用中创建会话并返回 ID。
    if (state.currentConversationId == null) {
      state = state.copyWith(messages: [], currentConversationTitle: _currentAiMessageTitle); // 临时标题
      updatedMessages = [userMessage, aiMessagePlaceholder];
    } else {
      // 如果 currentConversationId 已存在，则是在现有会话中追加消息
      updatedMessages = [...state.messages, userMessage, aiMessagePlaceholder];
    }
    state = state.copyWith(messages: updatedMessages);
    // 调用 _startStreamingResponse，并传递当前的 conversationId (可能是 null)
    await _startStreamingResponse(text, state.currentConversationId);
  }

  /// 启动并监听AI的流式响应。
  Future<void> _startStreamingResponse(String userMessageText, String? currentConversationId) async {
    try {
      final aiService = ref.read(aiServiceProvider);
      // 首先 await 获取到 Stream 对象
      final responseStream = await aiService.streamAIResponse(userMessageText, conversationId: currentConversationId);

      // 更新AI消息状态为“流式传输中”
      // isTyping 仍为 true，因为此时仍处于等待第一个数据块或刚开始流的状态
      _updateAiMessageState(
        id: _currentStreamingAiMessageId,
        streamingStatus: StreamingStatus.streaming,
        isTyping: true, // 保持 true，直到第一个数据块明确到达
      );

      // 启动初始响应延迟定时器：如果一段时间内没收到第一个数据块，确保UI显示“思考中”
      _startInitialResponseDelayTimer();

      _aiResponseSubscription = responseStream.listen(
        (ParsedSseEvent data) {
          // 一旦收到任何数据块，就取消初始响应延迟定时器
          _initialResponseDelayTimer?.cancel();



          // 处理不同类型的事件
          switch (data.type) {
            case 'message_stream_complete':
              // 处理消息完成事件，可能包含工具组件ID映射
              _handleMessageComplete(data);
              break;
            case 'conversation_metadata':
              // 这是新会话，后端返回了ID
              if (data.conversationId != null) {
                // 当后端返回 conversation_id 时，更新到 state
                if (state.currentConversationId == null || state.currentConversationId != data.conversationId) {
                  state = state.copyWith(currentConversationId: data.conversationId);
                  log("ChatHistoryNotifier: Conversation ID updated/received: ${data.conversationId}");
                  // 如果后端也返回了用户消息的持久化ID，可以在这里更新
                }
                state = state.copyWith(currentConversationId: data.conversationId);
                // 可以处理 is_new, model, limits 等元数据
                if (data.title != null && data.title != state.currentConversationTitle) {
                  // 有些后端可能在元数据里就带标题
                  state = state.copyWith(currentConversationTitle: data.title);
                }
                log("ChatHistoryNotifier: Conversation ID received: ${data.conversationId}");
              }
              // 可以处理 is_new, model, limits 等元数据
              break;
            case 'text':
              if (data.content != null && data.content!.isNotEmpty) {
                if (!_isFirstChunkReceived) {
                  _isFirstChunkReceived = true; // 标记已收到第一个数据块
                  // 重要：收到第一个有效数据块后，AI就不再是"正在思考"的状态了，而是"正在回复"
                  // 将 isTyping 设为 false，UI上的"思考中"文本指示器将消失。
                  _updateAiMessageState(id: _currentStreamingAiMessageId, isTyping: false);

                  // 立即显示第一个字符，避免等待动画周期
                  final characters = data.content!.characters.toList();
                  if (characters.isNotEmpty) {
                    final firstChar = characters.removeAt(0);
                    _updateAiMessageState(id: _currentStreamingAiMessageId, text: firstChar, timestamp: DateTime.now());
                    // 将剩余字符加入队列
                    _charQueue.addAll(characters);
                  }
                } else {
                  // 后续数据块正常加入队列
                  _charQueue.addAll(data.content!.characters as Iterable<String>);
                }
                _startTypingAnimation(); // 启动或继续逐字打字动画
              }
              break;
            case 'title_generation_fragment':
              if (data.title != null) {
                _currentAiMessageTitle += data.title!; // 拼接标题片段
                // 可以选择是否实时更新UI上的标题，或者等 title_generation 事件
                state = state.copyWith(currentConversationTitle: _currentAiMessageTitle);
              }
              break;
            case 'title_generation':
              if (data.title != null) {
                _currentAiMessageTitle = data.title!; // 最终标题
                state = state.copyWith(currentConversationTitle: _currentAiMessageTitle);
                log("ChatHistoryNotifier: Final title received: ${data.title}");
              }
              break;
            case 'error':
              // 处理业务逻辑错误
              log("ChatHistoryNotifier: Received error event: ${data.errorCode} - ${data.errorMessage}");
              _handleBusinessError(data);
              break;
            case 'tool_result':
              // 处理工具调用结果
              _handleToolResult(data);
              break;
            case 'message_stream_complete':
              // AI消息流结束，这里可以获取到AI消息的持久化ID (data.messageId)
              // 用这个ID更新前端的 _currentStreamingAiMessageId 对应的消息
              if (data.messageId != null && data.messageId != _currentStreamingAiMessageId) {
                log("ChatHistoryNotifier: Received message_stream_complete with messageId: ${data.messageId}");
                _updateMessageIdLocally(_currentStreamingAiMessageId, data.messageId!);
                _currentStreamingAiMessageId = data.messageId!; // 更新当前流ID为后端ID
              }
              // 标记消息完成，避免在 onDone 中重复处理
              if (!_isMessageCompleted) {
                _isMessageCompleted = true;
                _handleStreamComplete(null); // 确保状态更新为 completed
                log("ChatHistoryNotifier: Message marked as completed via message_stream_complete");
              }
              break;
            default:
              log("ChatHistoryNotifier: Received unhandled ParsedSseEvent type: ${data.type}");
              break;
          }
        },
        onError: (error) {
          _streamIsDone = true; // 标记流因错误而结束
          _handleStreamError(error);
        },
        onDone: () {
          _streamIsDone = true; // 标记流正常结束
          _initialResponseDelayTimer?.cancel(); // 流结束，也取消此定时器

          // 只有在消息尚未完成时才处理完成逻辑
          if (!_isMessageCompleted) {
            if (!_isFirstChunkReceived && _charQueue.isEmpty) {
              // 如果从未收到任何数据块（可能网络问题或AI未响应）
              _isMessageCompleted = true;
              _handleStreamComplete("[AI助手没有回复任何内容]");
            } else {
              // 流已结束，但可能还有字符在队列中等待处理
              // 让打字动画继续处理剩余字符，它会在队列空时自动完成
              // 如果没有打字动画在运行且队列为空，则立即完成
              if (_charQueue.isEmpty && (_typingAnimationTimer == null || !_typingAnimationTimer!.isActive)) {
                _isMessageCompleted = true;
                _handleStreamComplete(null);
              }
              // 否则等待打字动画处理完剩余字符
            }
          }
          // else: 消息已经通过 message_stream_complete 完成
        },
        cancelOnError: true, // 发生错误时自动取消订阅
      );
    } catch (e) {
      // 处理 _startStreamingResponse 内部可能抛出的同步异常
      _streamIsDone = true;
      _handleStreamError(e);
    }
  }

  /// 启动逐字打字动画。
  void _startTypingAnimation() {
    // 如果动画已在运行，则无需重新启动
    if (_typingAnimationTimer != null && _typingAnimationTimer!.isActive) {
      return;
    }

    _typingAnimationTimer = Timer.periodic(const Duration(milliseconds: 18), (timer) {
      if (_charQueue.isEmpty) {
        timer.cancel(); // 当前队列中的字符已处理完毕
        if (_streamIsDone && !_isMessageCompleted) {
          // 如果数据流也已结束，且消息尚未完成，则表示所有内容都已显示完毕
          _isMessageCompleted = true;
          _handleStreamComplete(null);
        } else {
          // 数据流尚未结束，但当前批次的字符已显示完。
          // **关键点**：由于 _isFirstChunkReceived 应该已经是 true，
          // 我们不在此处将 isTyping 设置回 true。AI消息将保持 isTyping = false 的状态，
          // UI上不会再出现"AI正在思考..."的文本指示器。
          // 用户将只看到已显示的文本和可能持续的点点点动画（如果UI设计如此）。
        }
        return;
      }

      // 从队列中取出一个字符并更新消息文本
      final messageIndex = state.messages.indexWhere((m) => m.id == _currentStreamingAiMessageId);
      if (messageIndex == -1) {
        timer.cancel(); // 消息丢失，停止动画
        print("ChatHistoryNotifier: AI消息 ID '$_currentStreamingAiMessageId' 在动画中未找到。");
        if (_streamIsDone && !_isMessageCompleted) {
          _isMessageCompleted = true;
          _handleStreamComplete("[消息渲染时发生错误]");
        }
        return;
      }
      final currentMessage = state.messages[messageIndex];
      final nextChar = _charQueue.removeAt(0);
      _updateAiMessageState(
        id: _currentStreamingAiMessageId,
        text: currentMessage.text + nextChar,
        timestamp: DateTime.now(), // 更新时间戳以反映最新活动
        // isTyping 保持之前的状态（应为 false，因为已开始接收数据）
      );
    });
  }

  /// 更新指定ID的AI消息状态。
  void _updateAiMessageState({required String id, String? text, bool? isTyping, StreamingStatus? streamingStatus, DateTime? timestamp}) {
    if (id.isEmpty) return;
    state = state.copyWith(
      messages: state.messages.map((msg) {
        if (msg.id == id) {
          return msg.copyWith(
            text: text ?? msg.text,
            isTyping: isTyping ?? msg.isTyping,
            streamingStatus: streamingStatus ?? msg.streamingStatus,
            timestamp: timestamp ?? msg.timestamp,
          );
        }
        return msg;
      }).toList(),
    );
  }

  /// 将本地临时消息ID替换为后端返回的持久化消息ID
  void _updateMessageIdLocally(String oldId, String newId) {
    if (oldId.isEmpty || newId.isEmpty || oldId == newId) return;

    state = state.copyWith(
      messages: state.messages.map((msg) {
        if (msg.id == oldId) {
          return msg.copyWith(id: newId);
        }
        return msg;
      }).toList(),
    );

    log("ChatHistoryNotifier: Updated message ID from '$oldId' to '$newId'");
  }

  /// 启动一个定时器，如果在指定时间内（例如700ms）未收到AI的第一个数据块，
  /// 则确保 `isTyping` 为 `true`，以便UI显示“AI正在思考...”的文本指示。
  /// 此定时器仅在 `_isFirstChunkReceived` 为 `false` 时有效。
  void _startInitialResponseDelayTimer() {
    _initialResponseDelayTimer?.cancel();
    // 仅当尚未收到第一个数据块时，此定时器才有意义
    if (!_isFirstChunkReceived) {
      _initialResponseDelayTimer = Timer(const Duration(milliseconds: 500), () {
        // 再次检查，确保在这200ms内确实没有收到第一个数据块，并且流也未结束
        if (!_isFirstChunkReceived && !_streamIsDone) {
          // 确保当前消息的 isTyping 状态为 true
          final currentMessage = state.messages.firstWhere((m) => m.id == _currentStreamingAiMessageId, orElse: () => ChatMessage.empty());
          if (!currentMessage.isTyping || currentMessage.streamingStatus != StreamingStatus.streaming) {
            _updateAiMessageState(id: _currentStreamingAiMessageId, isTyping: true, streamingStatus: StreamingStatus.streaming);
          }
        }
      });
    }
  }

  /// 处理工具调用结果
  void _handleToolResult(ParsedSseEvent toolData) {
    // 将工具组件添加到当前的AI消息中，而不是创建独立的工具消息
    final messageIndex = state.messages.indexWhere((m) => m.id == _currentStreamingAiMessageId);
    if (messageIndex == -1) {
      log("ChatHistoryNotifier: AI消息 ID '$_currentStreamingAiMessageId' 未找到，无法添加工具组件");
      return;
    }

    final currentAiMessage = state.messages[messageIndex];

    // 统一处理工具组件数据，优先使用新格式
    Map<String, dynamic>? componentData;
    if (toolData.toolComponent != null) {
      // 新格式：完整的工具组件数据
      componentData = toolData.toolComponent!;
    } else if (toolData.componentForUi != null) {
      // 旧格式：兼容处理
      componentData = toolData.componentForUi!;
    }

    if (componentData != null) {
      try {
        final toolComponent = ToolUIComponent.fromComponentData(componentData);

        // 将工具组件添加到AI消息的toolComponents列表中
        final updatedToolComponents = [...currentAiMessage.toolComponents, toolComponent];

        final updatedAiMessage = currentAiMessage.copyWith(
          toolComponents: updatedToolComponents,
          // 保留工具调用的元数据
          toolName: toolData.toolName,
          toolCallId: toolData.toolCallId,
          toolSuccess: toolData.isSuccess,
          toolData: toolData.toolData,
        );

        final updatedMessages = [...state.messages];
        updatedMessages[messageIndex] = updatedAiMessage;
        state = state.copyWith(messages: updatedMessages);

        log("ChatHistoryNotifier: 工具组件已添加到AI消息 - ${toolComponent.name}");
      } catch (e, stackTrace) {
        log("ChatHistoryNotifier: 创建工具组件时出错: $e");
      }
    }
  }

  /// 处理业务逻辑错误 (来自后端的error事件)
  void _handleBusinessError(ParsedSseEvent errorData) {
    _initialResponseDelayTimer?.cancel();
    _typingAnimationTimer?.cancel();
    _charQueue.clear();

    // 直接使用后端返回的语义化错误消息
    final errorMessage = errorData.errorMessage ?? "AI服务暂时不可用，请稍后重试";

    final currentText = state.messages.firstWhere((m) => m.id == _currentStreamingAiMessageId, orElse: () => ChatMessage.empty()).text;

    _updateAiMessageState(
      id: _currentStreamingAiMessageId,
      text: currentText.isEmpty ? errorMessage : "$currentText\n\n$errorMessage",
      isTyping: false,
      streamingStatus: StreamingStatus.error,
    );

    // 标记消息已完成（虽然是错误完成）
    _isMessageCompleted = true;
    _cleanupAfterStream();
  }

  /// 处理流错误。
  void _handleStreamError(dynamic error) {
    _initialResponseDelayTimer?.cancel();
    _typingAnimationTimer?.cancel();
    _charQueue.clear();

    String errorMessageText;
    if (error is AppException) {
      errorMessageText = error.message;
    } else {
      errorMessageText = "发生未知错误: ${error.toString()}";
    }
    final displayError = "抱歉，AI助手通讯发生错误: $errorMessageText";

    final currentText = state.messages.firstWhere((m) => m.id == _currentStreamingAiMessageId, orElse: () => ChatMessage.empty()).text;

    _updateAiMessageState(
      id: _currentStreamingAiMessageId,
      text: currentText.isEmpty ? displayError : "$currentText\n\n$displayError",
      isTyping: false, // 出错后，不再是“思考中”
      streamingStatus: StreamingStatus.error,
    );
    _cleanupAfterStream();
  }

  /// 处理流正常完成。
  void _handleStreamComplete(String? finalTextOverride) {
    _initialResponseDelayTimer?.cancel();

    final messageIndex = state.messages.indexWhere((m) => m.id == _currentStreamingAiMessageId);
    if (messageIndex == -1) {
      log("ChatHistoryNotifier: AI消息 ID '$_currentStreamingAiMessageId' 在流完成时未找到。");
      _cleanupAfterStream();
      return;
    }
    final aiMessage = state.messages[messageIndex];

    // 如果有finalTextOverride，直接使用它并停止打字动画
    // 否则保持当前文本，让打字动画继续处理剩余字符
    if (finalTextOverride != null) {
      _typingAnimationTimer?.cancel();
      _updateAiMessageState(id: _currentStreamingAiMessageId, text: finalTextOverride, isTyping: false, streamingStatus: StreamingStatus.completed);
    } else {
      // 如果没有剩余字符需要处理，则立即完成
      if (_charQueue.isEmpty) {
        _typingAnimationTimer?.cancel();
        _updateAiMessageState(id: _currentStreamingAiMessageId, isTyping: false, streamingStatus: StreamingStatus.completed);
      } else {
        // 有剩余字符，让打字动画继续，但更新状态为completed
        _updateAiMessageState(id: _currentStreamingAiMessageId, streamingStatus: StreamingStatus.completed);
        // 打字动画会在队列空时自动停止
      }
    }
    _cleanupAfterStream();
  }

  /// 取消所有活动的订阅和定时器。
  Future<void> _cancelCurrentStreamAndTimers() async {
    await _aiResponseSubscription?.cancel();
    _aiResponseSubscription = null;
    _initialResponseDelayTimer?.cancel();
    _initialResponseDelayTimer = null;
    _typingAnimationTimer?.cancel();
    _typingAnimationTimer = null;
  }

  /// 流结束后（成功或失败）的清理工作。
  void _cleanupAfterStream() {
    // 定时器和订阅已在 _cancelCurrentStreamAndTimers 或各自处理函数中取消。
    // _streamIsDone 标志已在 onError/onDone 中设置。
    // _currentStreamingAiMessageId 等待下一次请求时被覆盖。
  }

  void updateAIFeedback(String messageId, AIFeedbackStatus newFeedbackStatus) {
    state = state.copyWith(
      messages: state.messages.map((msg) {
        if (msg.id == messageId && msg.sender == MessageSender.ai) {
          final toggledStatus = msg.feedbackStatus == newFeedbackStatus ? AIFeedbackStatus.none : newFeedbackStatus;
          return msg.copyWith(feedbackStatus: toggledStatus);
        }
        return msg;
      }).toList(),
    );
  }

  /// 处理历史消息，主要是清理和标准化数据
  /// 新的API格式已经将工具组件包含在AI消息中，所以主要做数据清理
  List<ChatMessage> _processHistoricalMessages(List<ChatMessage> rawMessages) {
    final processedMessages = <ChatMessage>[];

    for (final message in rawMessages) {
      // 为没有时间戳的消息设置默认时间戳
      final processedMessage = message.timestamp == null
          ? message.copyWith(timestamp: DateTime.now())
          : message;

      // 设置消息的流式状态为已完成（历史消息都是完成状态）
      final finalMessage = processedMessage.copyWith(
        streamingStatus: StreamingStatus.completed,
        isTyping: false,
      );

      processedMessages.add(finalMessage);

      // 调试信息
      if (finalMessage.toolComponents.isNotEmpty) {
        log("ChatHistoryNotifier: Message ${finalMessage.id} has ${finalMessage.toolComponents.length} tool components");
        for (final comp in finalMessage.toolComponents) {
          log("ChatHistoryNotifier: - Component: ${comp.name} (${comp.type})");
        }
      }
    }

    log("ChatHistoryNotifier: Processed ${rawMessages.length} historical messages");
    return processedMessages;
  }



  /// 处理消息完成事件，更新工具组件的临时ID为最终ID
  void _handleMessageComplete(ParsedSseEvent data) {
    if (data.toolComponentMappings == null || data.toolComponentMappings!.isEmpty) {
      return;
    }

    final messageIndex = state.messages.indexWhere((m) => m.id == _currentStreamingAiMessageId);
    if (messageIndex == -1) {
      log("ChatHistoryNotifier: AI消息 ID '$_currentStreamingAiMessageId' 未找到，无法更新工具组件ID");
      return;
    }

    final currentAiMessage = state.messages[messageIndex];
    if (currentAiMessage.toolComponents.isEmpty) {
      return;
    }

    // 创建ID映射表
    final idMappings = <String, String>{};
    for (final mapping in data.toolComponentMappings!) {
      final tempId = mapping['temp_id'] as String?;
      final finalId = mapping['final_id'] as String?;
      if (tempId != null && finalId != null) {
        idMappings[tempId] = finalId;
      }
    }

    // 更新工具组件的ID
    final updatedToolComponents = currentAiMessage.toolComponents.map((component) {
      final newId = idMappings[component.id];
      if (newId != null) {
        return component.copyWith(id: newId);
      }
      return component;
    }).toList();

    // 更新消息
    final updatedAiMessage = currentAiMessage.copyWith(
      toolComponents: updatedToolComponents,
    );

    final updatedMessages = [...state.messages];
    updatedMessages[messageIndex] = updatedAiMessage;
    state = state.copyWith(messages: updatedMessages);

    log("ChatHistoryNotifier: Updated ${idMappings.length} tool component IDs");
  }

  @override
  void dispose() {
    _cancelCurrentStreamAndTimers();
    _charQueue.clear();
    super.dispose();
  }
}

final chatHistoryProvider = StateNotifierProvider<ChatHistoryNotifier, ChatHistoryState>((ref) {
  return ChatHistoryNotifier(ref);
});
