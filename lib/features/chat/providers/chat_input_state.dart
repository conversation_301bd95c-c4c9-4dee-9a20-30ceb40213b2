// features/chat/providers/chat_input_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_input_state.freezed.dart';

@freezed
abstract class ChatInputState with _$ChatInputState {
  const factory ChatInputState({
    @Default('') String text, // 当前输入框的文本
    @Default(false) bool isListening, // 是否正在进行语音识别
    @Default(false) bool isSpeechAvailable, // 语音识别服务是否可用
    @Default(false) bool isLoadingResponse, // 是否正在等待AI回复
    @Default(false) bool showError, // 是否显示错误提示 (例如 SnackBar)
    @Default('') String errorMessage, // 错误信息内容
    @Default(HintType.normal) HintType hintType, // 用于控制输入框提示文本类型
  }) = _ChatInputState;
}

// 新增枚举，用于更精细地控制输入框的提示文本
enum HintType {
  normal, // "输入消息..."
  listening, // "正在聆听..."
  aiProcessing, // "AI思考中..."
  speechNotRecognized, // "未识别到语音，请重试" (短暂提示)
}
