// features/chat/providers/chat_input_notifier.dart
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'chat_input_state.dart';

class ChatInputNotifier extends StateNotifier<ChatInputState> {
  final stt.SpeechToText _speechToText;
  final Future<void> Function(String) onSendMessage;

  bool _isSpeechInitialized = false;
  String _textBeforeSpeechSession = '';
  Timer? _noSpeechInputTimer;

  ChatInputNotifier({required this.onSendMessage})
    : _speechToText = stt.SpeechToText(),
      super(const ChatInputState()) {
    _initializeSpeech();
  }

  Future<void> _initializeSpeech() async {
    if (_isSpeechInitialized && state.isSpeechAvailable) return;
    print("ChatInputNotifier: 初始化语音服务...");
    try {
      final available = await _speechToText.initialize(
        onError: _onSpeechError,
        onStatus: _onSpeechStatus,
        debugLogging: false,
      );
      _isSpeechInitialized = true;
      state = state.copyWith(
        isSpeechAvailable: available,
        showError: !available,
        errorMessage: available ? '' : '语音服务不可用或权限被拒绝',
        hintType: available ? HintType.normal : state.hintType,
      );
    } catch (e) {
      print("ChatInputNotifier: 语音服务初始化异常: $e");
      if (mounted) {
        _isSpeechInitialized = true;
        state = state.copyWith(
          isSpeechAvailable: false,
          showError: true,
          errorMessage: '语音初始化失败',
        );
      }
    }
  }

  void _onSpeechStatus(String status) {
    print("ChatInputNotifier: 语音状态: $status");

    final isCurrentlyListening = status == stt.SpeechToText.listeningStatus;

    if (state.isListening && !isCurrentlyListening) {
      _noSpeechInputTimer?.cancel();
      print(
        "ChatInputNotifier: 停止聆听。 _textBeforeSpeechSession: '${_textBeforeSpeechSession}', 当前 state.text: '${state.text}'",
      );
      String recognizedNewContent = state.text
          .replaceFirst(_textBeforeSpeechSession, '')
          .trim();

      if (recognizedNewContent.isEmpty && _textBeforeSpeechSession.isEmpty) {
        print("ChatInputNotifier: 聆听结束，未识别到语音 (开始前也为空)。");
        state = state.copyWith(
          isListening: false,
          text: '',
          hintType: HintType.speechNotRecognized,
        );
      } else if (recognizedNewContent.isEmpty &&
          _textBeforeSpeechSession.isNotEmpty) {
        print("ChatInputNotifier: 聆听结束，未新增识别内容 (基于已有文本聆听)。");
        state = state.copyWith(
          isListening: false,
          text: _textBeforeSpeechSession,
          hintType: HintType.speechNotRecognized,
        );
      } else {
        print("ChatInputNotifier: 聆听结束，有识别内容。最终文本: '${state.text}'");
        state = state.copyWith(isListening: false, hintType: HintType.normal);
        _textBeforeSpeechSession = state.text;
      }

      if (state.hintType == HintType.speechNotRecognized) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted &&
              state.hintType == HintType.speechNotRecognized &&
              !state.isListening &&
              !state.isLoadingResponse) {
            state = state.copyWith(hintType: HintType.normal);
          }
        });
      }
    } else if (!state.isListening && isCurrentlyListening) {
      print("ChatInputNotifier: 开始聆听。");
      state = state.copyWith(isListening: true, hintType: HintType.listening);
    } else if (mounted && state.isListening != isCurrentlyListening) {
      print("ChatInputNotifier: 状态同步，isListening: $isCurrentlyListening");
      state = state.copyWith(isListening: isCurrentlyListening);
    }
  }

  void _onSpeechError(SpeechRecognitionError error) {
    _noSpeechInputTimer?.cancel();
    print(
      "ChatInputNotifier: 语音错误: ${error.errorMsg}, permanent: ${error.permanent}",
    );
    String userMessage = '语音识别时发生错误';
    if (error.errorMsg.toLowerCase().contains('match') ||
        error.errorMsg.toLowerCase().contains('speech timeout')) {
      userMessage = '未识别到语音，请重试';
      _textBeforeSpeechSession = '';
      state = state.copyWith(text: '');
    } else if (error.errorMsg.toLowerCase().contains('permission')) {
      userMessage = '需要麦克风权限';
    } else if (error.errorMsg.toLowerCase().contains('network')) {
      userMessage = '网络连接问题';
    } else if (error.errorMsg.toLowerCase().contains('busy')) {
      userMessage = '语音服务正忙';
    }
    state = state.copyWith(
      isListening: false,
      showError: true,
      errorMessage: userMessage,
      hintType: HintType.normal,
    );
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    _noSpeechInputTimer?.cancel();
    String newText = _textBeforeSpeechSession.isEmpty
        ? result.recognizedWords
        : '${_textBeforeSpeechSession.trim()} ${result.recognizedWords}'.trim();
    print(
      "ChatInputNotifier: 语音结果: '${result.recognizedWords}', 拼接后文本: '$newText', Final: ${result.finalResult}",
    );
    state = state.copyWith(text: newText.trim());
    if (result.finalResult) {
      _textBeforeSpeechSession = newText.trim();
      print(
        "ChatInputNotifier: Final result. _textBeforeSpeechSession 更新为: '$_textBeforeSpeechSession'",
      );
    }
  }

  Future<void> onMainButtonPressed() async {
    print(
      "ChatInputNotifier: 主按钮点击。当前状态: isListening=${state.isListening}, isLoadingResponse=${state.isLoadingResponse}, text='${state.text}'",
    );
    if (state.isLoadingResponse) return;
    if (state.isListening) {
      _noSpeechInputTimer?.cancel();
      await _speechToText.stop();
    } else if (state.text.trim().isNotEmpty) {
      await _submitMessage();
    } else {
      if (!state.isSpeechAvailable) {
        await _initializeSpeech();
        if (!state.isSpeechAvailable) {
          print("ChatInputNotifier: 尝试开始聆听，但语音服务仍不可用。");
          return;
        }
      }
      _textBeforeSpeechSession = state.text.trim();
      if (mounted) {
        state = state.copyWith(
          isListening: true,
          showError: false,
          errorMessage: '',
          hintType: HintType.listening,
        );
      }
      try {
        await _speechToText.listen(
          onResult: _onSpeechResult,
          localeId: 'zh_CN',
          listenFor: const Duration(seconds: 60),
          pauseFor: const Duration(seconds: 5),
          partialResults: true,
          cancelOnError: true,
        );
        _noSpeechInputTimer?.cancel();
        _noSpeechInputTimer = Timer(const Duration(seconds: 7), () {
          if (mounted &&
              state.isListening &&
              state.text == _textBeforeSpeechSession) {
            print("ChatInputNotifier: 7秒后无有效语音输入，主动停止。");
            _speechToText.stop();
          }
        });
      } catch (e) {
        if (mounted) {
          _onSpeechError(SpeechRecognitionError("listen_failed", true));
        }
      }
    }
  }

  void onTextChanged(String newText) {
    if (state.isListening) {
      print("ChatInputNotifier: 用户手动输入，停止当前语音聆听。");
      _noSpeechInputTimer?.cancel();
      _speechToText.stop();
    }
    _textBeforeSpeechSession = newText;
    state = state.copyWith(text: newText, hintType: HintType.normal);
  }

  Future<void> _submitMessage() async {
    if (state.isLoadingResponse) {
      print("ChatInputNotifier: _submitMessage - 正在加载或未挂载，返回。");
      return;
    }
    final textToSend = state.text.trim();
    if (textToSend.isEmpty) {
      print("ChatInputNotifier: _submitMessage - 文本为空，不发送。");
      return;
    }
    if (state.isListening) {
      _noSpeechInputTimer?.cancel();
      await _speechToText.stop();
      if (mounted && state.isListening) {
        print(
          "ChatInputNotifier: _submitMessage - 尝试停止聆听后，isListening 仍为 true。放弃本次发送。",
        );
        return;
      }
    }
    final currentTextAfterStop = state.text.trim();
    if (currentTextAfterStop.isEmpty) {
      print("ChatInputNotifier: _submitMessage - 停止聆听后最终文本为空，取消发送。");
      return;
    }
    print("ChatInputNotifier: 准备发送消息: '$currentTextAfterStop'");
    state = state.copyWith(
      isLoadingResponse: true,
      hintType: HintType.aiProcessing,
    );
    try {
      await onSendMessage(currentTextAfterStop);
      if (mounted) {
        print("ChatInputNotifier: 消息发送成功，AI回复处理完毕。重置输入框。");
        _textBeforeSpeechSession = '';
        state = state.copyWith(
          text: '',
          isLoadingResponse: false,
          hintType: HintType.normal,
        );
      }
    } catch (e, s) {
      print("ChatInputNotifier: 消息发送或AI处理异常: $e\n$s");
      if (mounted) {
        state = state.copyWith(
          isLoadingResponse: false,
          showError: true,
          errorMessage: '发送失败，请检查网络或稍后重试',
          hintType: HintType.normal,
        );
      }
    }
  }

  void clearError() {
    if (mounted && state.showError) {
      state = state.copyWith(showError: false, errorMessage: '');
    }
  }

  @override
  void dispose() {
    print("ChatInputNotifier: dispose called.");
    _noSpeechInputTimer?.cancel();
    if (_speechToText.isListening) {
      _speechToText.stop();
    }
    super.dispose();
  }
}




final chatInputNotifierProviderFamily = StateNotifierProvider.autoDispose
    .family<ChatInputNotifier, ChatInputState, Future<void> Function(String)>((
      ref,
      onSendMessageCallback,
    ) {
      return ChatInputNotifier(onSendMessage: onSendMessageCallback);
    });
