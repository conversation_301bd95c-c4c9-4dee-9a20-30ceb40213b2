import 'package:freezed_annotation/freezed_annotation.dart';
import 'chat_message.dart'; // 导入ChatMessage模型

part 'conversation_detail.freezed.dart';

part 'conversation_detail.g.dart';

@freezed
abstract class ConversationDetail with _$ConversationDetail {
  const factory ConversationDetail({
    required String id,
    required String title,
    @JsonKey(fromJson: _dateTimeFromJson, toJson: _dateTimeToJson) required DateTime updatedAt,
    // 直接包含消息列表，不再使用分页结构
    @Default([]) List<ChatMessage> messages,
  }) = _ConversationDetail;

  factory ConversationDetail.fromJson(Map<String, dynamic> json) => _$ConversationDetailFromJson(json);
}

// Custom deserializer for DateTime
DateTime _dateTimeFromJson(dynamic json) {
  if (json == null) {
    return DateTime.now(); // Provide a default if null
  }
  if (json is String) {
    return DateTime.parse(json);
  }
  throw FormatException('Invalid DateTime format: $json');
}

// Custom serializer for DateTime
String _dateTimeToJson(DateTime dateTime) => dateTime.toIso8601String();
