import 'package:freezed_annotation/freezed_annotation.dart';

part 'tool_ui_component.freezed.dart';
part 'tool_ui_component.g.dart';

/// 工具UI组件类型枚举
enum ToolUIComponentType {
  transactionReceiptCard,
  transactionFeed,
  dataTable,
  chartCard,
  summaryCard,
  listCard,
  imageCard,
  customCard,
}

/// 工具UI组件模型
@freezed
abstract class ToolUIComponent with _$ToolUIComponent {
  const factory ToolUIComponent({
    String? id, // 可选，只有需要编辑的组件才有ID
    required ToolUIComponentType type,
    required String name,
    @JsonKey(fromJson: _mapFromJson) required Map<String, dynamic> props,
    @JsonKey(fromJson: _mapFromJson) @Default({}) Map<String, dynamic> metadata,
    String? transactionId, // 交易ID，用于交易组件的编辑
  }) = _ToolUIComponent;

  factory ToolUIComponent.fromJson(Map<String, dynamic> json) => _$ToolUIComponentFromJson(json);

  /// 从后端返回的component_for_ui数据创建ToolUIComponent
  factory ToolUIComponent.fromComponentData(Map<String, dynamic> componentData, {String? id}) {
    final name = componentData['name'] as String? ?? 'UnknownComponent';
    final props = componentData['props'] as Map<String, dynamic>? ?? {};

    // 根据组件名称映射到类型
    final type = _mapComponentNameToType(name);

    // 检查是否是交易组件，如果是则提取transactionId
    String? transactionId;
    String? componentId;

    if (name.toLowerCase().contains('transaction') || name.toLowerCase().contains('receipt')) {
      // 安全地提取 transactionId，优先从顶层查找，然后从props中查找
      final transactionIdValue = componentData['transactionId'] ?? props['transactionId'];

      if (transactionIdValue != null) {
        transactionId = transactionIdValue.toString();
        // 交易组件需要ID用于编辑
        componentId = 'transaction-$transactionId';
      } else {
        componentId = id;
      }
    }
    // 其他组件不需要ID（纯展示用）

    return ToolUIComponent(
      id: componentId, // 只有交易组件有ID
      type: type,
      name: name,
      props: props,
      transactionId: transactionId,
    );
  }

  /// 将组件名称映射到类型枚举
  static ToolUIComponentType _mapComponentNameToType(String name) {
    print('DEBUG: _mapComponentNameToType called with name: $name');
    final lowerName = name.toLowerCase();
    print('DEBUG: Converted to lowercase: $lowerName');

    switch (lowerName) {
      case 'transactionreceiptcard':
        return ToolUIComponentType.transactionReceiptCard;
      case 'transactionfeed':
      case 'transaction_feed':
      case 'transactionlist':
      case 'transaction_list':
        print('DEBUG: Mapped $lowerName to transactionFeed');
        return ToolUIComponentType.transactionFeed;
      case 'datatable':
      case 'table':
      case 'shadtable':
      case 'transactiontable':
      case 'expenselist':
        return ToolUIComponentType.dataTable;
      case 'chartcard':
      case 'piechart':
      case 'barchart':
      case 'linechart':
      case 'radarchart':
        return ToolUIComponentType.chartCard;
      case 'summarycard':
        return ToolUIComponentType.summaryCard;
      case 'listcard':
        return ToolUIComponentType.listCard;
      case 'imagecard':
        return ToolUIComponentType.imageCard;
      default:
        print('DEBUG: No mapping found for $lowerName, using customCard');
        return ToolUIComponentType.customCard;
    }
  }
}

/// 交易收据卡片的属性模型
@freezed
abstract class TransactionReceiptCardProps with _$TransactionReceiptCardProps {
  const factory TransactionReceiptCardProps({
    required String status,
    required String title,
    required String amount,
    required String currency,
    required String amountColor,
    required List<TransactionDetail> details,
    @Default([]) List<String> tags,
    String? transactionId, // 添加交易ID用于跳转详情页
    String? category, // 添加类别
    String? timestamp, // 添加时间戳
  }) = _TransactionReceiptCardProps;

  factory TransactionReceiptCardProps.fromJson(Map<String, dynamic> json) => _$TransactionReceiptCardPropsFromJson(json);
}

/// 交易详情模型
@freezed
abstract class TransactionDetail with _$TransactionDetail {
  const factory TransactionDetail({
    required String label,
    required String value,
  }) = _TransactionDetail;

  factory TransactionDetail.fromJson(Map<String, dynamic> json) => _$TransactionDetailFromJson(json);
}

/// 数据表格的属性模型
@freezed
abstract class DataTableProps with _$DataTableProps {
  const factory DataTableProps({
    required String title,
    required List<String> headers,
    required List<List<String>> rows,
    @Default({}) Map<String, dynamic> styling,
    // 分页信息
    PaginationInfo? pagination,
  }) = _DataTableProps;

  factory DataTableProps.fromJson(Map<String, dynamic> json) => _$DataTablePropsFromJson(json);
}

/// 分页信息模型
@freezed
abstract class PaginationInfo with _$PaginationInfo {
  const factory PaginationInfo({
    required int currentPage,
    required int totalPages,
    required int totalItems,
    required int itemsPerPage,
    @Default(false) bool hasNextPage,
    @Default(false) bool hasPreviousPage,
  }) = _PaginationInfo;

  factory PaginationInfo.fromJson(Map<String, dynamic> json) => _$PaginationInfoFromJson(json);
}

/// 图表卡片的属性模型
@freezed
abstract class ChartCardProps with _$ChartCardProps {
  const factory ChartCardProps({
    required String title,
    required String chartType, // 'pie', 'bar', 'line', etc.
    required Map<String, dynamic> chartData,
    @Default({}) Map<String, dynamic> chartOptions,
  }) = _ChartCardProps;

  factory ChartCardProps.fromJson(Map<String, dynamic> json) => _$ChartCardPropsFromJson(json);
}

/// 摘要卡片的属性模型
@freezed
abstract class SummaryCardProps with _$SummaryCardProps {
  const factory SummaryCardProps({
    required String title,
    required String summary,
    @Default([]) List<SummaryItem> items,
    @Default({}) Map<String, dynamic> styling,
  }) = _SummaryCardProps;

  factory SummaryCardProps.fromJson(Map<String, dynamic> json) => _$SummaryCardPropsFromJson(json);
}

/// 摘要项目模型
@freezed
abstract class SummaryItem with _$SummaryItem {
  const factory SummaryItem({
    required String label,
    required String value,
    String? color,
    String? icon,
  }) = _SummaryItem;

  factory SummaryItem.fromJson(Map<String, dynamic> json) => _$SummaryItemFromJson(json);
}

// Custom deserializer for Map fields
Map<String, dynamic> _mapFromJson(dynamic json) {
  if (json == null) return {};
  if (json is Map<String, dynamic>) return json;
  if (json is Map) return Map<String, dynamic>.from(json);
  throw FormatException('Invalid Map format: $json');
}
