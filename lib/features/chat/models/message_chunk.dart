// features/chat/models/message_chunk.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'message_chunk.freezed.dart';
part 'message_chunk.g.dart';

@freezed
abstract class MessageChunk with _$MessageChunk {
  const factory MessageChunk({
    required String type, // 如 text / tool_call / image 等
    required dynamic content, // 内容可以是 String / Map / List 等
  }) = _MessageChunk;

  factory MessageChunk.fromJson(Map<String, dynamic> json) =>
      _$MessageChunkFromJson(json);
}
