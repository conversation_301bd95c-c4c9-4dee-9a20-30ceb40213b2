import 'package:freezed_annotation/freezed_annotation.dart';
import 'message_chunk.dart';
import 'tool_ui_component.dart';

part 'chat_message.freezed.dart';
part 'chat_message.g.dart';

@JsonEnum()
enum MessageSender { user, ai, system, tool, assistant}

enum AIFeedbackStatus { none, liked, disliked }

enum MessageType { text, aiThinking, toolResult }

enum StreamingStatus { none, connecting, streaming, completed, error }

@freezed
abstract class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    @JsonKey(defaultValue: '') required String id,
    @JsonKey(fromJson: _senderFromJson, toJson: _senderToJson, readValue: _readSenderValue) required MessageSender sender,

    @JsonKey(fromJson: _dateTimeNullableFromJson, toJson: _dateTimeNullableToJson) DateTime? timestamp,
    @Default("") String text,

    @JsonKey(name: 'message_type') @Default(MessageType.text) MessageType messageType,
    @Json<PERSON>ey(name: 'feedback_status') @Default(AIFeedbackStatus.none) AIFeedbackStatus feedbackStatus,
    @JsonKey(name: 'streaming_status') @Default(StreamingStatus.none) StreamingStatus streamingStatus,
    @JsonKey(name: 'is_typing') @Default(false) bool isTyping,
    // conversationId 在API响应的每个消息中都有，可以加进来
    @JsonKey(name: 'conversation_id') String? conversationId,

    // 工具调用结果相关字段
    @JsonKey(name: 'tool_name') String? toolName,
    @JsonKey(name: 'tool_call_id') String? toolCallId,
    @JsonKey(name: 'tool_success') bool? toolSuccess,
    @JsonKey(name: 'tool_data', fromJson: _mapFromJson) Map<String, dynamic>? toolData,
    // UI组件渲染数据
    @JsonKey(
      fromJson: _toolComponentsFromJson,
      toJson: _toolComponentsToJson,
      readValue: _readToolComponentsValue,
    ) @Default([]) List<ToolUIComponent> toolComponents,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);

  factory ChatMessage.empty() =>
      ChatMessage(id: '', text: '', sender: MessageSender.ai, timestamp: DateTime.now(), streamingStatus: StreamingStatus.completed, isTyping: false);
}

// Custom deserializer for DateTime
DateTime _dateTimeFromJson(dynamic json) {
  if (json == null) {
    return DateTime.now(); // Provide a default if null
  }
  if (json is String) {
    return DateTime.parse(json);
  }
  throw FormatException('Invalid DateTime format: $json');
}

// Custom serializer for DateTime (optional, but good practice)
String _dateTimeToJson(DateTime dateTime) => dateTime.toIso8601String();

// Custom serializers for ToolUIComponent list
List<ToolUIComponent> _toolComponentsFromJson(dynamic json) {
  if (json == null) return [];
  if (json is List) {
    return json.map((item) {
      if (item is Map<String, dynamic>) {
        // 统一使用完整格式，如果缺少字段则自动补充
        return _createToolUIComponentFromData(item);
      } else if (item is Map) {
        // Convert Map<dynamic, dynamic> to Map<String, dynamic>
        final convertedMap = Map<String, dynamic>.from(item);
        return _createToolUIComponentFromData(convertedMap);
      }
      throw FormatException('Invalid ToolUIComponent format: $item');
    }).toList();
  }
  throw FormatException('Invalid ToolUIComponent list format: $json');
}

/// 统一创建 ToolUIComponent 的方法，自动补充缺失字段
ToolUIComponent _createToolUIComponentFromData(Map<String, dynamic> data) {
  // 检查是否是历史记录格式（包含完整字段）
  if (data.containsKey('name') && data.containsKey('props')) {
    final name = data['name'] as String;
    final props = data['props'] as Map<String, dynamic>;

    // 检查是否是交易组件
    String? transactionId;
    String? componentId;

    if (name.toLowerCase().contains('transaction') || name.toLowerCase().contains('receipt')) {
      // 安全地提取 transactionId
      final transactionIdFromData = data['transactionId'];
      final transactionIdFromProps = props['transactionId'];

      if (transactionIdFromData != null) {
        transactionId = transactionIdFromData.toString();
      } else if (transactionIdFromProps != null) {
        transactionId = transactionIdFromProps.toString();
      }

      componentId = data['id'] as String? ?? (transactionId != null ? 'transaction-$transactionId' : null);
    } else {
      // 其他组件不需要ID
      componentId = null;
    }

    return ToolUIComponent(
      id: componentId,
      type: data['type'] != null
        ? ToolUIComponentType.values.firstWhere(
            (e) => e.toString().split('.').last == data['type'],
            orElse: () => _mapComponentNameToType(name)
          )
        : _mapComponentNameToType(name),
      name: name,
      props: props,
      metadata: data['metadata'] as Map<String, dynamic>? ?? {},
      transactionId: transactionId,
    );
  } else {
    // 兼容SSE格式：使用 fromComponentData
    return ToolUIComponent.fromComponentData(data);
  }
}

/// 将组件名称映射到类型枚举
ToolUIComponentType _mapComponentNameToType(String name) {
  switch (name.toLowerCase()) {
    case 'transactionreceiptcard':
      return ToolUIComponentType.transactionReceiptCard;
    case 'datatable':
    case 'table':
    case 'shadtable':
    case 'transactiontable':
    case 'expenselist':
      return ToolUIComponentType.dataTable;
    case 'chartcard':
    case 'piechart':
    case 'barchart':
    case 'linechart':
    case 'radarchart':
      return ToolUIComponentType.chartCard;
    case 'summarycard':
      return ToolUIComponentType.summaryCard;
    case 'listcard':
      return ToolUIComponentType.listCard;
    case 'imagecard':
      return ToolUIComponentType.imageCard;
    default:
      return ToolUIComponentType.customCard;
  }
}

List<Map<String, dynamic>> _toolComponentsToJson(List<ToolUIComponent> components) {
  return components.map((component) => component.toJson()).toList();
}

// Custom deserializer for Map fields
Map<String, dynamic>? _mapFromJson(dynamic json) {
  if (json == null) return null;
  if (json is Map<String, dynamic>) return json;
  if (json is Map) return Map<String, dynamic>.from(json);
  throw FormatException('Invalid Map format: $json');
}

// Custom serializers for MessageSender (处理 role/sender 字段差异)
MessageSender _senderFromJson(dynamic json) {
  if (json == null) return MessageSender.ai;

  final String value = json.toString().toLowerCase();
  switch (value) {
    case 'user':
      return MessageSender.user;
    case 'assistant':
    case 'ai':
      return MessageSender.ai;
    case 'system':
      return MessageSender.system;
    case 'tool':
      return MessageSender.tool;
    default:
      return MessageSender.ai;
  }
}

String _senderToJson(MessageSender sender) {
  switch (sender) {
    case MessageSender.user:
      return 'user';
    case MessageSender.ai:
    case MessageSender.assistant:
      return 'assistant';
    case MessageSender.system:
      return 'system';
    case MessageSender.tool:
      return 'tool';
  }
}

// Custom serializers for nullable DateTime
DateTime? _dateTimeNullableFromJson(dynamic json) {
  if (json == null) return null;
  if (json is String) {
    return DateTime.parse(json);
  }
  throw FormatException('Invalid DateTime format: $json');
}

String? _dateTimeNullableToJson(DateTime? dateTime) => dateTime?.toIso8601String();

// Custom readValue function to handle both 'role' and 'sender' fields
Object? _readSenderValue(Map json, String key) {
  // 优先读取 'role' 字段，如果不存在则读取 'sender' 字段
  return json['role'] ?? json['sender'];
}

// Custom readValue function to handle both 'toolComponents' and 'tool_components' fields
Object? _readToolComponentsValue(Map json, String key) {
  // 优先读取 'toolComponents' 字段（驼峰命名），如果不存在则读取 'tool_components' 字段（下划线命名）
  return json['toolComponents'] ?? json['tool_components'];
}