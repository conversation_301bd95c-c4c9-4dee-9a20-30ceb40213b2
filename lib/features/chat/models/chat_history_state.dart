// features/chat/providers/chat_history_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'chat_message.dart';

part 'chat_history_state.freezed.dart';

@freezed
abstract class ChatHistoryState with _$ChatHistoryState {
  const factory ChatHistoryState({
    String? currentConversationId,
    String? currentConversationTitle,
    @Default(false) bool isLoadingHistory,
    @Default([]) List<ChatMessage> messages,
    String? historyError,
    @Default(1) int historyCurrentPage, // 当前加载的历史消息页码
    @Default(true) bool historyHasMore, // 是否还有更多历史消息可加载
    // AI 响应的特定状态，例如是否正在等待AI的第一个回复块
  }) = _ChatHistoryState;
}