import 'package:flutter/material.dart';

/// 图表颜色映射工具类
class ChartColors {
  /// 根据 colorKey 获取对应的颜色
  static Color getColorByKey(String colorKey) {
    switch (colorKey.toLowerCase()) {
      // 收入/支出颜色
      case 'income':
        return const Color(0xFF10B981); // 绿色
      case 'expense':
        return const Color(0xFFEF4444); // 红色
      
      // 分类颜色
      case 'category_1':
        return const Color(0xFF3B82F6); // 蓝色
      case 'category_2':
        return const Color(0xFF10B981); // 绿色
      case 'category_3':
        return const Color(0xFFF59E0B); // 橙色
      case 'category_4':
        return const Color(0xFF8B5CF6); // 紫色
      case 'category_5':
        return const Color(0xFFEF4444); // 红色
      case 'category_6':
        return const Color(0xFF06B6D4); // 青色
      case 'category_7':
        return const Color(0xFFEC4899); // 粉色
      case 'category_8':
        return const Color(0xFF84CC16); // 青绿色
      case 'category_9':
        return const Color(0xFFF97316); // 深橙色
      case 'category_10':
        return const Color(0xFF6366F1); // 靛蓝色
      
      // 预算相关颜色
      case 'budget':
        return const Color(0xFF6B7280); // 灰色
      case 'actual':
        return const Color(0xFF3B82F6); // 蓝色
      case 'over_budget':
        return const Color(0xFFEF4444); // 红色
      case 'under_budget':
        return const Color(0xFF10B981); // 绿色
      
      // 财务健康度颜色
      case 'excellent':
        return const Color(0xFF10B981); // 绿色
      case 'good':
        return const Color(0xFF84CC16); // 青绿色
      case 'average':
        return const Color(0xFFF59E0B); // 橙色
      case 'poor':
        return const Color(0xFFEF4444); // 红色
      
      // 默认颜色序列
      default:
        return _getDefaultColorByIndex(colorKey.hashCode % 10);
    }
  }
  
  /// 根据索引获取默认颜色
  static Color _getDefaultColorByIndex(int index) {
    final colors = [
      const Color(0xFF3B82F6), // 蓝色
      const Color(0xFF10B981), // 绿色
      const Color(0xFFF59E0B), // 橙色
      const Color(0xFF8B5CF6), // 紫色
      const Color(0xFFEF4444), // 红色
      const Color(0xFF06B6D4), // 青色
      const Color(0xFFEC4899), // 粉色
      const Color(0xFF84CC16), // 青绿色
      const Color(0xFFF97316), // 深橙色
      const Color(0xFF6366F1), // 靛蓝色
    ];
    return colors[index % colors.length];
  }
  
  /// 获取颜色的半透明版本
  static Color getColorWithOpacity(String colorKey, double opacity) {
    return getColorByKey(colorKey).withOpacity(opacity);
  }
  
  /// 获取渐变色
  static LinearGradient getGradientByKey(String colorKey) {
    final baseColor = getColorByKey(colorKey);
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        baseColor.withOpacity(0.8),
        baseColor.withOpacity(0.3),
      ],
    );
  }
}
