// features/footprint/models/footprint_models.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../home/<USER>/transaction_model.dart';

part 'footprint_models.freezed.dart';
part 'footprint_models.g.dart';

/// 足迹时间轴的日期分组模型
@freezed
abstract class FootprintDateGroup with _$FootprintDateGroup {
  const factory FootprintDateGroup({
    required DateTime date,
    required String displayTitle, // "今天", "昨天", "6月28日"
    required List<TransactionModel> transactions,
    @Default(0) double totalAmount, // 当日总金额
  }) = _FootprintDateGroup;

  factory FootprintDateGroup.fromJson(Map<String, dynamic> json) =>
      _$FootprintDateGroupFromJson(json);
}

/// 月份总结模型
@freezed
abstract class MonthlySummary with _$MonthlySummary {
  const factory MonthlySummary({
    required int year,
    required int month,
    required String title, // AI生成的月份标题，如"六月，是探索与分享的季节"
    required String description, // AI生成的月份描述
    required List<String> highlights, // 月份亮点标签，如["旅行的足迹", "与朋友的晚餐"]
    required double totalExpense,
    required double totalIncome,
    required int transactionCount,
    @Default([]) List<CategorySummary> topCategories, // 主要消费类别
  }) = _MonthlySummary;

  factory MonthlySummary.fromJson(Map<String, dynamic> json) =>
      _$MonthlySummaryFromJson(json);
}

/// 类别汇总模型
@freezed
abstract class CategorySummary with _$CategorySummary {
  const factory CategorySummary({
    required String category,
    required String iconUrl,
    required double amount,
    required int count,
    required double percentage, // 占总支出的百分比
  }) = _CategorySummary;

  factory CategorySummary.fromJson(Map<String, dynamic> json) =>
      _$CategorySummaryFromJson(json);
}

/// 足迹搜索筛选条件
@freezed
abstract class FootprintFilter with _$FootprintFilter {
  const factory FootprintFilter({
    String? searchQuery, // 自然语言搜索，如"上个月和小明吃饭"
    DateTime? startDate,
    DateTime? endDate,
    List<String>? categories,
    List<String>? participants,
    @Default(false) bool onlyWithPhotos, // 只显示有照片的记录
    @Default(false) bool onlyWithMood, // 只显示有心情的记录
    TransactionType? type, // 收入或支出
  }) = _FootprintFilter;

  factory FootprintFilter.fromJson(Map<String, dynamic> json) =>
      _$FootprintFilterFromJson(json);
}

/// 足迹页面状态模型
@freezed
abstract class FootprintState with _$FootprintState {
  const factory FootprintState({
    @Default([]) List<FootprintDateGroup> dateGroups,
    @Default([]) List<MonthlySummary> monthlySummaries,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(false) bool hasReachedEnd,
    @Default(1) int currentPage,
    FootprintFilter? currentFilter,
    String? errorMessage,
    @Default(false) bool hasNewFootprint, // 是否有新的足迹（来自AI聊天）
  }) = _FootprintState;

  factory FootprintState.fromJson(Map<String, dynamic> json) =>
      _$FootprintStateFromJson(json);
}

/// AI叙事生成请求模型
@freezed
abstract class NarrativeGenerationRequest with _$NarrativeGenerationRequest {
  const factory NarrativeGenerationRequest({
    required String category,
    required double amount,
    required TransactionType type,
    String? merchantName,
    String? location,
    List<String>? participants,
    String? description,
    DateTime? timestamp,
  }) = _NarrativeGenerationRequest;

  factory NarrativeGenerationRequest.fromJson(Map<String, dynamic> json) =>
      _$NarrativeGenerationRequestFromJson(json);
}

/// AI叙事生成响应模型
@freezed
abstract class NarrativeGenerationResponse with _$NarrativeGenerationResponse {
  const factory NarrativeGenerationResponse({
    required String narrativeTitle, // 生成的叙事标题
    required String suggestedMood, // 建议的心情表情
    @Default([]) List<String> suggestedTags, // 建议的标签
  }) = _NarrativeGenerationResponse;

  factory NarrativeGenerationResponse.fromJson(Map<String, dynamic> json) =>
      _$NarrativeGenerationResponseFromJson(json);
}
