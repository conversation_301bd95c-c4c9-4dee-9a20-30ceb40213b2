// features/footprint/providers/footprint_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/footprint_models.dart';
import '../services/footprint_service.dart';
import '../../home/<USER>/transaction_model.dart';
import '../../auth/providers/auth_provider.dart';

/// 足迹状态管理 Provider
final footprintProvider = StateNotifierProvider<FootprintNotifier, FootprintState>((ref) {
  final footprintService = ref.watch(footprintServiceProvider);
  return FootprintNotifier(footprintService, ref);
});

/// 足迹状态通知器
class FootprintNotifier extends StateNotifier<FootprintState> {
  final FootprintService _footprintService;
  final Ref _ref;

  FootprintNotifier(this._footprintService, this._ref) : super(const FootprintState()) {
    _initialize();
  }

  /// 初始化足迹数据
  Future<void> _initialize() async {
    await loadFootprints();
    await loadMonthlySummaries();
  }

  /// 加载足迹数据
  Future<void> loadFootprints({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(isLoading: true, currentPage: 1, hasReachedEnd: false);
    } else if (state.isLoading || state.isLoadingMore) {
      return;
    }

    try {
      if (state.currentPage == 1) {
        state = state.copyWith(isLoading: true, errorMessage: null);
      } else {
        state = state.copyWith(isLoadingMore: true);
      }

      final transactions = await _footprintService.getFootprintTransactions(
        page: state.currentPage,
        filter: state.currentFilter,
      );

      final dateGroups = _groupTransactionsByDate(transactions);
      
      if (state.currentPage == 1) {
        state = state.copyWith(
          dateGroups: dateGroups,
          isLoading: false,
          currentPage: state.currentPage + 1,
          hasReachedEnd: transactions.length < 20, // 假设每页20条
        );
      } else {
        final updatedGroups = [...state.dateGroups];
        for (final newGroup in dateGroups) {
          final existingIndex = updatedGroups.indexWhere(
            (group) => _isSameDay(group.date, newGroup.date),
          );
          
          if (existingIndex >= 0) {
            // 合并同一天的数据
            updatedGroups[existingIndex] = updatedGroups[existingIndex].copyWith(
              transactions: [...updatedGroups[existingIndex].transactions, ...newGroup.transactions],
              totalAmount: updatedGroups[existingIndex].totalAmount + newGroup.totalAmount,
            );
          } else {
            updatedGroups.add(newGroup);
          }
        }
        
        state = state.copyWith(
          dateGroups: updatedGroups,
          isLoadingMore: false,
          currentPage: state.currentPage + 1,
          hasReachedEnd: transactions.length < 20,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 加载月份总结
  Future<void> loadMonthlySummaries() async {
    try {
      final summaries = await _footprintService.getMonthlySummaries();
      state = state.copyWith(monthlySummaries: summaries);
    } catch (e) {
      // 月份总结加载失败不影响主要功能
      print('加载月份总结失败: $e');
    }
  }

  /// 应用筛选条件
  Future<void> applyFilter(FootprintFilter filter) async {
    state = state.copyWith(currentFilter: filter, currentPage: 1);
    await loadFootprints(refresh: true);
  }

  /// 清除筛选条件
  Future<void> clearFilter() async {
    state = state.copyWith(currentFilter: null, currentPage: 1);
    await loadFootprints(refresh: true);
  }

  /// 添加新的足迹（来自AI聊天）
  void addNewFootprint(TransactionModel transaction) {
    final updatedTransaction = transaction.copyWith(
      aiNarrativeTitle: transaction.aiNarrativeTitle ?? _generateNarrativeTitle(transaction),
    );

    final dateGroups = [...state.dateGroups];
    final transactionDate = DateTime(
      updatedTransaction.timestamp.year,
      updatedTransaction.timestamp.month,
      updatedTransaction.timestamp.day,
    );

    // 查找是否已存在相同日期的分组
    final existingGroupIndex = dateGroups.indexWhere(
      (group) => _isSameDay(group.date, transactionDate),
    );

    if (existingGroupIndex >= 0) {
      // 添加到现有分组
      final existingGroup = dateGroups[existingGroupIndex];
      final updatedTransactions = [updatedTransaction, ...existingGroup.transactions];
      
      dateGroups[existingGroupIndex] = existingGroup.copyWith(
        transactions: updatedTransactions,
        totalAmount: existingGroup.totalAmount + 
            (updatedTransaction.type == TransactionType.expense 
                ? -updatedTransaction.amount 
                : updatedTransaction.amount),
      );
    } else {
      // 创建新的分组
      final newGroup = FootprintDateGroup(
        date: transactionDate,
        displayTitle: _getDateDisplayTitle(transactionDate),
        transactions: [updatedTransaction],
        totalAmount: updatedTransaction.type == TransactionType.expense 
            ? -updatedTransaction.amount 
            : updatedTransaction.amount,
      );
      
      // 按日期排序插入
      int insertIndex = 0;
      for (int i = 0; i < dateGroups.length; i++) {
        if (transactionDate.isAfter(dateGroups[i].date)) {
          insertIndex = i;
          break;
        }
        insertIndex = i + 1;
      }
      dateGroups.insert(insertIndex, newGroup);
    }

    state = state.copyWith(
      dateGroups: dateGroups,
      hasNewFootprint: true,
    );

    // 3秒后清除新足迹标识
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        state = state.copyWith(hasNewFootprint: false);
      }
    });
  }

  /// 更新交易的故事内容
  Future<void> updateTransactionStory({
    required String transactionId,
    String? photoPath,
    String? moodEmoji,
    String? customNarrative,
  }) async {
    try {
      await _footprintService.updateTransactionStory(
        transactionId: transactionId,
        photoPath: photoPath,
        moodEmoji: moodEmoji,
        customNarrative: customNarrative,
      );

      // 更新本地状态
      final updatedGroups = state.dateGroups.map((group) {
        final updatedTransactions = group.transactions.map((transaction) {
          if (transaction.id == transactionId) {
            return transaction.copyWith(
              photoPath: photoPath ?? transaction.photoPath,
              moodEmoji: moodEmoji ?? transaction.moodEmoji,
              aiNarrativeTitle: customNarrative ?? transaction.aiNarrativeTitle,
              hasStoryContent: photoPath != null || moodEmoji != null || transaction.hasStoryContent,
            );
          }
          return transaction;
        }).toList();

        return group.copyWith(transactions: updatedTransactions);
      }).toList();

      state = state.copyWith(dateGroups: updatedGroups);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
    }
  }

  /// 按日期分组交易
  List<FootprintDateGroup> _groupTransactionsByDate(List<TransactionModel> transactions) {
    final Map<String, List<TransactionModel>> groupedMap = {};
    
    for (final transaction in transactions) {
      final dateKey = DateFormat('yyyy-MM-dd').format(transaction.timestamp);
      groupedMap.putIfAbsent(dateKey, () => []).add(transaction);
    }

    return groupedMap.entries.map((entry) {
      final date = DateTime.parse(entry.key);
      final dayTransactions = entry.value;
      final totalAmount = dayTransactions.fold<double>(0, (sum, transaction) {
        return sum + (transaction.type == TransactionType.expense 
            ? -transaction.amount 
            : transaction.amount);
      });

      return FootprintDateGroup(
        date: date,
        displayTitle: _getDateDisplayTitle(date),
        transactions: dayTransactions,
        totalAmount: totalAmount,
      );
    }).toList()
      ..sort((a, b) => b.date.compareTo(a.date)); // 按日期倒序排列
  }

  /// 获取日期显示标题
  String _getDateDisplayTitle(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = today.difference(targetDate).inDays;
    
    switch (difference) {
      case 0:
        return '今天';
      case 1:
        return '昨天';
      case 2:
        return '前天';
      default:
        if (difference < 7) {
          return '${difference}天前';
        } else if (date.year == now.year) {
          return DateFormat('M月d日', 'zh_CN').format(date);
        } else {
          return DateFormat('yyyy年M月d日', 'zh_CN').format(date);
        }
    }
  }

  /// 生成叙事标题
  String _generateNarrativeTitle(TransactionModel transaction) {
    // 这里可以调用AI服务生成更智能的标题
    // 暂时使用简单的规则生成
    final category = transaction.category;
    final amount = transaction.amount;
    final participants = transaction.participants;
    
    if (participants.isNotEmpty) {
      return '与${participants.join('、')}一起的${category}时光';
    }
    
    switch (category.toLowerCase()) {
      case '餐饮':
        return amount > 100 ? '享受了一顿丰盛的美食' : '品尝了简单的美味';
      case '交通':
        return '踏上了一段旅程';
      case '购物':
        return amount > 200 ? '进行了一次愉快的购物' : '买到了心仪的小物件';
      case '娱乐':
        return '度过了快乐的休闲时光';
      case '工资':
        return '收获了辛勤工作的回报';
      default:
        return '记录了生活中的一笔${transaction.type == TransactionType.expense ? '支出' : '收入'}';
    }
  }

  /// 判断是否为同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
