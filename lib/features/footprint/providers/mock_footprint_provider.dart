// features/footprint/providers/mock_footprint_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/footprint_models.dart';
import '../../home/<USER>/transaction_model.dart';

/// 模拟足迹数据提供器 - 用于演示
final mockFootprintProvider = StateNotifierProvider<MockFootprintNotifier, FootprintState>((ref) {
  return MockFootprintNotifier();
});

class MockFootprintNotifier extends StateNotifier<FootprintState> {
  MockFootprintNotifier() : super(const FootprintState()) {
    _loadMockData();
  }

  void _loadMockData() {
    final now = DateTime.now();
    
    // 创建模拟交易数据
    final mockTransactions = [
      // 今天的交易
      TransactionModel(
        id: '1',
        type: TransactionType.expense,
        category: '餐饮',
        iconUrl: 'assets/icons/food.png',
        amount: 128.50,
        timestamp: DateTime(now.year, now.month, now.day, 12, 30),
        aiNarrativeTitle: '与小明一起享受了丰盛的午餐',
        merchantName: '海底捞火锅',
        geoLocation: '北京三里屯店',
        participants: ['小明'],
        moodEmoji: '😋',
        hasStoryContent: true,
        isAiBuild: true,
      ),
      TransactionModel(
        id: '2',
        type: TransactionType.expense,
        category: '交通',
        iconUrl: 'assets/icons/transport.png',
        amount: 15.00,
        timestamp: DateTime(now.year, now.month, now.day, 9, 15),
        aiNarrativeTitle: '踏上了前往公司的旅程',
        merchantName: '滴滴出行',
        moodEmoji: '🚗',
        hasStoryContent: true,
        isAiBuild: true,
      ),
      
      // 昨天的交易
      TransactionModel(
        id: '3',
        type: TransactionType.expense,
        category: '购物',
        iconUrl: 'assets/icons/shopping.png',
        amount: 299.00,
        timestamp: DateTime(now.year, now.month, now.day - 1, 16, 45),
        aiNarrativeTitle: '在优衣库买到了心仪的衣服',
        merchantName: '优衣库',
        geoLocation: '王府井店',
        moodEmoji: '🛍️',
        hasStoryContent: true,
        isAiBuild: true,
      ),
      TransactionModel(
        id: '4',
        type: TransactionType.expense,
        category: '娱乐',
        iconUrl: 'assets/icons/entertainment.png',
        amount: 89.00,
        timestamp: DateTime(now.year, now.month, now.day - 1, 20, 30),
        aiNarrativeTitle: '与朋友们度过了快乐的电影时光',
        merchantName: '万达影城',
        participants: ['小红', '小李'],
        moodEmoji: '🎬',
        hasStoryContent: true,
        isAiBuild: true,
      ),
      
      // 前天的交易
      TransactionModel(
        id: '5',
        type: TransactionType.income,
        category: '工资',
        iconUrl: 'assets/icons/salary.png',
        amount: 8500.00,
        timestamp: DateTime(now.year, now.month, now.day - 2, 10, 0),
        aiNarrativeTitle: '收获了辛勤工作的回报',
        description: '月度工资',
        moodEmoji: '💰',
        hasStoryContent: true,
        isAiBuild: true,
      ),
      TransactionModel(
        id: '6',
        type: TransactionType.expense,
        category: '餐饮',
        iconUrl: 'assets/icons/food.png',
        amount: 45.50,
        timestamp: DateTime(now.year, now.month, now.day - 2, 18, 20),
        aiNarrativeTitle: '品尝了街角咖啡店的美味',
        merchantName: '星巴克',
        moodEmoji: '☕',
        hasStoryContent: true,
        isAiBuild: true,
      ),
    ];

    // 创建模拟月份总结
    final mockMonthlySummaries = [
      MonthlySummary(
        year: now.year,
        month: now.month,
        title: '${now.month}月，是探索与分享的季节',
        description: '这个月你与朋友们分享了许多美好时光，也为自己的成长投资了不少。生活充满了色彩和温度。',
        highlights: ['与朋友的聚餐', '购物的快乐', '工作的收获'],
        totalExpense: 2156.80,
        totalIncome: 8500.00,
        transactionCount: 28,
        topCategories: [
          const CategorySummary(
            category: '餐饮',
            iconUrl: 'assets/icons/food.png',
            amount: 856.30,
            count: 12,
            percentage: 39.7,
          ),
          const CategorySummary(
            category: '交通',
            iconUrl: 'assets/icons/transport.png',
            amount: 445.20,
            count: 8,
            percentage: 20.6,
          ),
          const CategorySummary(
            category: '购物',
            iconUrl: 'assets/icons/shopping.png',
            amount: 855.30,
            count: 5,
            percentage: 39.7,
          ),
        ],
      ),
      MonthlySummary(
        year: now.month == 1 ? now.year - 1 : now.year,
        month: now.month == 1 ? 12 : now.month - 1,
        title: '${now.month == 1 ? 12 : now.month - 1}月，是成长与收获的时光',
        description: '上个月你专注于自我提升，在学习和健康方面投入了更多时间和精力。',
        highlights: ['健身的坚持', '学习的投入', '健康的关注'],
        totalExpense: 1890.50,
        totalIncome: 8500.00,
        transactionCount: 24,
        topCategories: [
          const CategorySummary(
            category: '教育',
            iconUrl: 'assets/icons/education.png',
            amount: 680.00,
            count: 3,
            percentage: 36.0,
          ),
          const CategorySummary(
            category: '医疗',
            iconUrl: 'assets/icons/medical.png',
            amount: 520.50,
            count: 4,
            percentage: 27.5,
          ),
          const CategorySummary(
            category: '餐饮',
            iconUrl: 'assets/icons/food.png',
            amount: 690.00,
            count: 17,
            percentage: 36.5,
          ),
        ],
      ),
    ];

    // 按日期分组
    final dateGroups = _groupTransactionsByDate(mockTransactions);

    state = state.copyWith(
      dateGroups: dateGroups,
      monthlySummaries: mockMonthlySummaries,
      isLoading: false,
    );
  }

  /// 按日期分组交易
  List<FootprintDateGroup> _groupTransactionsByDate(List<TransactionModel> transactions) {
    final Map<String, List<TransactionModel>> groupedMap = {};
    
    for (final transaction in transactions) {
      final dateKey = '${transaction.timestamp.year}-${transaction.timestamp.month.toString().padLeft(2, '0')}-${transaction.timestamp.day.toString().padLeft(2, '0')}';
      groupedMap.putIfAbsent(dateKey, () => []).add(transaction);
    }

    return groupedMap.entries.map((entry) {
      final date = DateTime.parse(entry.key);
      final dayTransactions = entry.value;
      final totalAmount = dayTransactions.fold<double>(0, (sum, transaction) {
        return sum + (transaction.type == TransactionType.expense 
            ? -transaction.amount 
            : transaction.amount);
      });

      return FootprintDateGroup(
        date: date,
        displayTitle: _getDateDisplayTitle(date),
        transactions: dayTransactions,
        totalAmount: totalAmount,
      );
    }).toList()
      ..sort((a, b) => b.date.compareTo(a.date)); // 按日期倒序排列
  }

  /// 获取日期显示标题
  String _getDateDisplayTitle(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = today.difference(targetDate).inDays;
    
    switch (difference) {
      case 0:
        return '今天';
      case 1:
        return '昨天';
      case 2:
        return '前天';
      default:
        if (difference < 7) {
          return '${difference}天前';
        } else if (date.year == now.year) {
          return '${date.month}月${date.day}日';
        } else {
          return '${date.year}年${date.month}月${date.day}日';
        }
    }
  }

  /// 模拟加载更多数据
  Future<void> loadFootprints({bool refresh = false}) async {
    if (refresh) {
      _loadMockData();
    }
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// 模拟添加新足迹
  void addNewFootprint(TransactionModel transaction) {
    final updatedTransaction = transaction.copyWith(
      aiNarrativeTitle: transaction.aiNarrativeTitle ?? '记录了新的生活足迹',
    );

    final dateGroups = [...state.dateGroups];
    final transactionDate = DateTime(
      updatedTransaction.timestamp.year,
      updatedTransaction.timestamp.month,
      updatedTransaction.timestamp.day,
    );

    // 查找是否已存在相同日期的分组
    final existingGroupIndex = dateGroups.indexWhere(
      (group) => _isSameDay(group.date, transactionDate),
    );

    if (existingGroupIndex >= 0) {
      // 添加到现有分组
      final existingGroup = dateGroups[existingGroupIndex];
      final updatedTransactions = [updatedTransaction, ...existingGroup.transactions];
      
      dateGroups[existingGroupIndex] = existingGroup.copyWith(
        transactions: updatedTransactions,
        totalAmount: existingGroup.totalAmount + 
            (updatedTransaction.type == TransactionType.expense 
                ? -updatedTransaction.amount 
                : updatedTransaction.amount),
      );
    } else {
      // 创建新的分组
      final newGroup = FootprintDateGroup(
        date: transactionDate,
        displayTitle: _getDateDisplayTitle(transactionDate),
        transactions: [updatedTransaction],
        totalAmount: updatedTransaction.type == TransactionType.expense 
            ? -updatedTransaction.amount 
            : updatedTransaction.amount,
      );
      
      // 按日期排序插入
      int insertIndex = 0;
      for (int i = 0; i < dateGroups.length; i++) {
        if (transactionDate.isAfter(dateGroups[i].date)) {
          insertIndex = i;
          break;
        }
        insertIndex = i + 1;
      }
      dateGroups.insert(insertIndex, newGroup);
    }

    state = state.copyWith(
      dateGroups: dateGroups,
      hasNewFootprint: true,
    );

    // 3秒后清除新足迹标识
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        state = state.copyWith(hasNewFootprint: false);
      }
    });
  }

  /// 判断是否为同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
