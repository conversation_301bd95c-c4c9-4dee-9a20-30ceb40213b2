// features/footprint/services/footprint_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/footprint_models.dart';
import '../../home/<USER>/transaction_model.dart';
import '../../../core/network/network_client.dart';

/// 足迹服务 Provider
final footprintServiceProvider = Provider<FootprintService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return FootprintService(networkClient);
});

/// 足迹服务类
class FootprintService {
  final NetworkClient _networkClient;

  FootprintService(this._networkClient);

  /// 获取足迹交易数据
  Future<List<TransactionModel>> getFootprintTransactions({
    int page = 1,
    int limit = 20,
    FootprintFilter? filter,
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    // 添加筛选条件
    if (filter != null) {
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        queryParameters['search'] = filter.searchQuery;
      }
      if (filter.startDate != null) {
        queryParameters['start_date'] = filter.startDate!.toIso8601String();
      }
      if (filter.endDate != null) {
        queryParameters['end_date'] = filter.endDate!.toIso8601String();
      }
      if (filter.categories != null && filter.categories!.isNotEmpty) {
        queryParameters['categories'] = filter.categories!.join(',');
      }
      if (filter.participants != null && filter.participants!.isNotEmpty) {
        queryParameters['participants'] = filter.participants!.join(',');
      }
      if (filter.onlyWithPhotos) {
        queryParameters['has_photos'] = true;
      }
      if (filter.onlyWithMood) {
        queryParameters['has_mood'] = true;
      }
      if (filter.type != null) {
        queryParameters['type'] = filter.type!.name;
      }
    }

    final response = await _networkClient.request<List<dynamic>>(
      '/footprint/transactions',
      method: HttpMethod.get,
      queryParameters: queryParameters,
      fromJsonT: (json) => json as List<dynamic>,
    );

    return response.map((item) => TransactionModel.fromJson(item as Map<String, dynamic>)).toList();
  }

  /// 获取月份总结
  Future<List<MonthlySummary>> getMonthlySummaries({
    int limit = 6,
  }) async {
    final response = await _networkClient.request<List<dynamic>>(
      '/footprint/monthly-summaries',
      method: HttpMethod.get,
      queryParameters: {'limit': limit},
      fromJsonT: (json) => json as List<dynamic>,
    );

    return response.map((item) => MonthlySummary.fromJson(item as Map<String, dynamic>)).toList();
  }

  /// 更新交易的故事内容
  Future<void> updateTransactionStory({
    required String transactionId,
    String? photoPath,
    String? moodEmoji,
    String? customNarrative,
  }) async {
    final data = <String, dynamic>{};
    
    if (photoPath != null) {
      data['photo_path'] = photoPath;
    }
    if (moodEmoji != null) {
      data['mood_emoji'] = moodEmoji;
    }
    if (customNarrative != null) {
      data['ai_narrative_title'] = customNarrative;
    }

    await _networkClient.request<void>(
      '/footprint/transactions/$transactionId/story',
      method: HttpMethod.patch,
      data: data,
    );
  }

  /// 生成AI叙事标题
  Future<NarrativeGenerationResponse> generateNarrativeTitle(
    NarrativeGenerationRequest request,
  ) async {
    final response = await _networkClient.request<Map<String, dynamic>>(
      '/footprint/generate-narrative',
      method: HttpMethod.post,
      data: request.toJson(),
      fromJsonT: (json) => json as Map<String, dynamic>,
    );

    return NarrativeGenerationResponse.fromJson(response);
  }

  /// 搜索足迹（自然语言搜索）
  Future<List<TransactionModel>> searchFootprints(String query) async {
    final response = await _networkClient.request<List<dynamic>>(
      '/footprint/search',
      method: HttpMethod.post,
      data: {'query': query},
      fromJsonT: (json) => json as List<dynamic>,
    );

    return response.map((item) => TransactionModel.fromJson(item as Map<String, dynamic>)).toList();
  }

  /// 获取特定月份的详细数据
  Future<MonthlySummary> getMonthlyDetail(int year, int month) async {
    final response = await _networkClient.request<Map<String, dynamic>>(
      '/footprint/monthly-detail',
      method: HttpMethod.get,
      queryParameters: {
        'year': year,
        'month': month,
      },
      fromJsonT: (json) => json as Map<String, dynamic>,
    );

    return MonthlySummary.fromJson(response);
  }

  /// 上传照片
  Future<String> uploadPhoto(String filePath) async {
    // 这里应该实现文件上传逻辑
    // 暂时返回模拟的URL
    await Future.delayed(const Duration(seconds: 1));
    return 'https://example.com/photos/${DateTime.now().millisecondsSinceEpoch}.jpg';
  }

  /// 获取推荐的心情表情
  Future<List<String>> getRecommendedMoods(String category) async {
    final response = await _networkClient.request<List<dynamic>>(
      '/footprint/recommended-moods',
      method: HttpMethod.get,
      queryParameters: {'category': category},
      fromJsonT: (json) => json as List<dynamic>,
    );

    return response.cast<String>();
  }

  /// 获取参与人员建议
  Future<List<String>> getParticipantSuggestions(String query) async {
    final response = await _networkClient.request<List<dynamic>>(
      '/footprint/participant-suggestions',
      method: HttpMethod.get,
      queryParameters: {'query': query},
      fromJsonT: (json) => json as List<dynamic>,
    );

    return response.cast<String>();
  }

  /// 删除交易
  Future<void> deleteTransaction(String transactionId) async {
    await _networkClient.request<void>(
      '/footprint/transactions/$transactionId',
      method: HttpMethod.delete,
    );
  }

  /// 分享交易
  Future<String> shareTransaction(String transactionId) async {
    final response = await _networkClient.request<Map<String, dynamic>>(
      '/footprint/transactions/$transactionId/share',
      method: HttpMethod.post,
      fromJsonT: (json) => json as Map<String, dynamic>,
    );

    return response['share_url'] as String;
  }
}
