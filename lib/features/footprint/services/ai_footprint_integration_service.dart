// features/footprint/services/ai_footprint_integration_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/footprint_models.dart';
import '../providers/footprint_provider.dart';
import '../../home/<USER>/transaction_model.dart';
import '../../chat/models/tool_ui_component.dart';

/// AI聊天与足迹联动服务 Provider
final aiFootprintIntegrationServiceProvider = Provider<AIFootprintIntegrationService>((ref) {
  return AIFootprintIntegrationService(ref);
});

/// AI聊天与足迹联动服务
/// 负责处理AI聊天中的记账操作与足迹模块的联动
class AIFootprintIntegrationService {
  final Ref _ref;

  AIFootprintIntegrationService(this._ref);

  /// 处理AI聊天中的交易记录，生成足迹
  Future<void> handleTransactionFromAI({
    required String category,
    required double amount,
    required TransactionType type,
    String? description,
    String? merchantName,
    String? location,
    List<String>? participants,
    DateTime? timestamp,
  }) async {
    try {
      // 1. 生成AI叙事标题
      final narrativeRequest = NarrativeGenerationRequest(
        category: category,
        amount: amount,
        type: type,
        merchantName: merchantName,
        location: location,
        participants: participants ?? [],
        description: description,
        timestamp: timestamp ?? DateTime.now(),
      );

      final narrativeResponse = await _generateNarrativeTitle(narrativeRequest);

      // 2. 创建交易模型
      final transaction = TransactionModel(
        id: _generateTransactionId(),
        type: type,
        category: category,
        iconUrl: _getCategoryIconUrl(category),
        amount: amount,
        timestamp: timestamp ?? DateTime.now(),
        description: description,
        merchantName: merchantName,
        geoLocation: location,
        participants: participants ?? [],
        aiNarrativeTitle: narrativeResponse.narrativeTitle,
        moodEmoji: _getDefaultMoodForCategory(category),
        isAiBuild: true,
        hasStoryContent: false,
      );

      // 3. 添加到足迹
      _ref.read(footprintProvider.notifier).addNewFootprint(transaction);

      // 4. 可选：发送通知给用户
      _notifyNewFootprintAdded(transaction);

    } catch (e) {
      print('处理AI交易记录失败: $e');
      // 可以选择显示错误提示或记录日志
    }
  }

  /// 处理工具调用结果，提取交易信息
  Future<void> handleToolResult(ToolUIComponent toolComponent) async {
    switch (toolComponent.name) {
      case 'TransactionReceiptCard':
        await _handleTransactionReceiptCard(toolComponent);
        break;
      case 'ExpenseAnalysisCard':
        // 分析卡片不需要创建新足迹
        break;
      default:
        // 其他工具组件暂不处理
        break;
    }
  }

  /// 处理交易收据卡片
  Future<void> _handleTransactionReceiptCard(ToolUIComponent toolComponent) async {
    // 暂时注释掉，因为TransactionReceiptCardProps的字段需要确认
    // final props = toolComponent.props as TransactionReceiptCardProps;

    // await handleTransactionFromAI(
    //   category: props.category,
    //   amount: props.amount,
    //   type: props.type == 'expense' ? TransactionType.expense : TransactionType.income,
    //   description: props.description,
    //   merchantName: props.merchant,
    //   location: props.location,
    //   participants: props.participants,
    //   timestamp: props.timestamp,
    // );
  }

  /// 生成AI叙事标题
  Future<NarrativeGenerationResponse> _generateNarrativeTitle(
    NarrativeGenerationRequest request,
  ) async {
    // 这里可以调用实际的AI服务
    // 暂时使用本地生成逻辑
    return _generateLocalNarrative(request);
  }

  /// 本地生成叙事标题（备用方案）
  NarrativeGenerationResponse _generateLocalNarrative(NarrativeGenerationRequest request) {
    String narrativeTitle;
    String suggestedMood = '💳'; // 默认值
    List<String> suggestedTags = [];

    final category = request.category.toLowerCase();
    final amount = request.amount;
    final participants = request.participants;
    final merchantName = request.merchantName;
    final location = request.location;

    // 根据参与人员生成标题
    if (participants != null && participants.isNotEmpty) {
      if (participants.length == 1) {
        narrativeTitle = '与${participants.first}一起的${_getCategoryDisplayName(category)}时光';
      } else {
        narrativeTitle = '与${participants.join('、')}一起的${_getCategoryDisplayName(category)}聚会';
      }
      suggestedTags.addAll(participants);
    } else {
      // 根据类别和金额生成标题
      switch (category) {
        case '餐饮':
        case 'food':
          if (amount > 200) {
            narrativeTitle = merchantName != null 
                ? '在${merchantName}享受了一顿丰盛的美食'
                : '享受了一顿丰盛的美食';
            suggestedMood = '😋';
          } else if (amount > 50) {
            narrativeTitle = merchantName != null
                ? '在${merchantName}品尝了美味的料理'
                : '品尝了美味的料理';
            suggestedMood = '😊';
          } else {
            narrativeTitle = '享受了简单而美味的一餐';
            suggestedMood = '🙂';
          }
          break;

        case '交通':
        case 'transport':
          if (location != null) {
            narrativeTitle = '踏上了前往${location}的旅程';
          } else {
            narrativeTitle = '踏上了一段旅程';
          }
          suggestedMood = '🚗';
          break;

        case '购物':
        case 'shopping':
          if (amount > 500) {
            narrativeTitle = merchantName != null
                ? '在${merchantName}进行了一次愉快的购物'
                : '进行了一次愉快的购物';
            suggestedMood = '🛍️';
          } else {
            narrativeTitle = '买到了心仪的小物件';
            suggestedMood = '😄';
          }
          break;

        case '娱乐':
        case 'entertainment':
          narrativeTitle = merchantName != null
              ? '在${merchantName}度过了快乐的休闲时光'
              : '度过了快乐的休闲时光';
          suggestedMood = '🎉';
          break;

        case '工资':
        case 'salary':
          narrativeTitle = '收获了辛勤工作的回报';
          suggestedMood = '💰';
          break;

        case '医疗':
        case 'medical':
          narrativeTitle = '关爱自己的健康';
          suggestedMood = '🏥';
          break;

        case '教育':
        case 'education':
          narrativeTitle = '投资于自己的成长';
          suggestedMood = '📚';
          break;

        default:
          narrativeTitle = request.type == TransactionType.expense
              ? '记录了生活中的一笔支出'
              : '记录了生活中的一笔收入';
          suggestedMood = '💳';
      }
    }

    // 添加地点标签
    if (location != null) {
      suggestedTags.add(location);
    }

    // 添加商家标签
    if (merchantName != null) {
      suggestedTags.add(merchantName);
    }

    return NarrativeGenerationResponse(
      narrativeTitle: narrativeTitle,
      suggestedMood: suggestedMood,
      suggestedTags: suggestedTags,
    );
  }

  /// 获取类别显示名称
  String _getCategoryDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return '餐饮';
      case 'transport':
        return '交通';
      case 'shopping':
        return '购物';
      case 'entertainment':
        return '娱乐';
      case 'medical':
        return '医疗';
      case 'education':
        return '教育';
      default:
        return category;
    }
  }

  /// 获取类别图标URL
  String _getCategoryIconUrl(String category) {
    // 这里应该返回实际的图标URL
    // 暂时返回占位符
    return 'assets/icons/${category.toLowerCase()}.png';
  }

  /// 获取类别默认心情
  String _getDefaultMoodForCategory(String category) {
    switch (category.toLowerCase()) {
      case '餐饮':
      case 'food':
        return '😋';
      case '交通':
      case 'transport':
        return '🚗';
      case '购物':
      case 'shopping':
        return '🛍️';
      case '娱乐':
      case 'entertainment':
        return '🎉';
      case '工资':
      case 'salary':
        return '💰';
      case '医疗':
      case 'medical':
        return '🏥';
      case '教育':
      case 'education':
        return '📚';
      default:
        return '💳';
    }
  }

  /// 生成交易ID
  String _generateTransactionId() {
    return 'txn_${DateTime.now().millisecondsSinceEpoch}_${(DateTime.now().microsecond % 1000).toString().padLeft(3, '0')}';
  }

  /// 通知新足迹已添加
  void _notifyNewFootprintAdded(TransactionModel transaction) {
    // 这里可以显示一个轻量级的通知
    // 或者更新足迹页面的红点标识
    print('新足迹已添加: ${transaction.aiNarrativeTitle}');
  }
}
