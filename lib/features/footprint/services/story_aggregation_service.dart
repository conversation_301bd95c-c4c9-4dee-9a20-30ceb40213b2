// features/footprint/services/story_aggregation_service.dart
import '../../home/<USER>/transaction_model.dart';

/// 故事聚合服务 - 负责将交易记录聚合成有意义的故事章节
class StoryAggregationService {
  /// 识别并聚合故事章节
  static List<StoryChapter> aggregateStories(List<TransactionModel> transactions) {
    final chapters = <StoryChapter>[];
    final processedTransactions = <String>{};
    
    // 按时间排序
    final sortedTransactions = List<TransactionModel>.from(transactions)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    for (final transaction in sortedTransactions) {
      if (processedTransactions.contains(transaction.id)) continue;
      
      // 尝试识别旅行故事
      final travelStory = _identifyTravelStory(transaction, sortedTransactions, processedTransactions);
      if (travelStory != null) {
        chapters.add(travelStory);
        continue;
      }
      
      // 尝试识别项目故事（如装修、婚礼等）
      final projectStory = _identifyProjectStory(transaction, sortedTransactions, processedTransactions);
      if (projectStory != null) {
        chapters.add(projectStory);
        continue;
      }
      
      // 尝试识别社交故事
      final socialStory = _identifySocialStory(transaction, sortedTransactions, processedTransactions);
      if (socialStory != null) {
        chapters.add(socialStory);
        continue;
      }
      
      // 尝试识别习惯故事
      final habitStory = _identifyHabitStory(transaction, sortedTransactions, processedTransactions);
      if (habitStory != null) {
        chapters.add(habitStory);
        continue;
      }
    }
    
    return chapters;
  }
  
  /// 识别旅行故事
  static StoryChapter? _identifyTravelStory(
    TransactionModel anchor,
    List<TransactionModel> allTransactions,
    Set<String> processedTransactions,
  ) {
    // 旅行识别逻辑：在短时间内有交通、住宿、餐饮、景点等消费
    if (anchor.category.toLowerCase() != '交通' && anchor.category.toLowerCase() != 'transport') {
      return null;
    }
    
    final relatedTransactions = <TransactionModel>[anchor];
    final timeWindow = Duration(days: 7); // 7天内的相关消费
    
    for (final transaction in allTransactions) {
      if (processedTransactions.contains(transaction.id) || transaction.id == anchor.id) continue;
      
      final timeDiff = anchor.timestamp.difference(transaction.timestamp).abs();
      if (timeDiff > timeWindow) continue;
      
      // 检查是否为旅行相关类别
      final category = transaction.category.toLowerCase();
      if (['交通', 'transport', '住宿', 'hotel', '餐饮', 'food', '娱乐', 'entertainment', '购物', 'shopping'].contains(category)) {
        // 检查地理位置是否不同（如果有的话）
        if (transaction.geoLocation != null && anchor.geoLocation != null) {
          if (transaction.geoLocation != anchor.geoLocation) {
            relatedTransactions.add(transaction);
          }
        } else {
          relatedTransactions.add(transaction);
        }
      }
    }
    
    // 至少需要3笔相关消费才能构成旅行故事
    if (relatedTransactions.length < 3) return null;
    
    // 标记为已处理
    for (final transaction in relatedTransactions) {
      processedTransactions.add(transaction.id);
    }
    
    final totalAmount = relatedTransactions.fold<double>(0, (sum, t) => sum + t.amount);
    final destinations = relatedTransactions
        .where((t) => t.geoLocation != null)
        .map((t) => t.geoLocation!)
        .toSet()
        .toList();
    
    return StoryChapter(
      id: 'travel_${anchor.id}',
      title: _generateTravelTitle(destinations, relatedTransactions),
      description: '这次旅途共消费${totalAmount.toStringAsFixed(0)}元，足迹遍布${destinations.length}个地方',
      transactions: relatedTransactions,
      type: StoryChapterType.travel,
      highlights: _generateTravelHighlights(relatedTransactions),
      coverImagePath: null, // 可以后续添加地图截图
    );
  }
  
  /// 识别项目故事（如装修、婚礼等）
  static StoryChapter? _identifyProjectStory(
    TransactionModel anchor,
    List<TransactionModel> allTransactions,
    Set<String> processedTransactions,
  ) {
    // 基于商家名称或描述中的关键词识别项目
    final projectKeywords = {
      '装修': ['装修', '建材', '家具', '电器', '五金'],
      '婚礼': ['婚礼', '婚纱', '酒店', '摄影', '花艺'],
      '健身': ['健身', '运动', '瑜伽', '游泳'],
      '学习': ['培训', '课程', '书籍', '教育'],
    };
    
    String? projectType;
    List<String>? keywords;
    
    for (final entry in projectKeywords.entries) {
      for (final keyword in entry.value) {
        if (anchor.merchantName?.contains(keyword) == true || 
            anchor.description?.contains(keyword) == true) {
          projectType = entry.key;
          keywords = entry.value;
          break;
        }
      }
      if (projectType != null) break;
    }
    
    if (projectType == null) return null;
    
    final relatedTransactions = <TransactionModel>[anchor];
    final timeWindow = Duration(days: 30); // 30天内的相关消费
    
    for (final transaction in allTransactions) {
      if (processedTransactions.contains(transaction.id) || transaction.id == anchor.id) continue;
      
      final timeDiff = anchor.timestamp.difference(transaction.timestamp).abs();
      if (timeDiff > timeWindow) continue;
      
      // 检查是否包含项目关键词
      for (final keyword in keywords!) {
        if (transaction.merchantName?.contains(keyword) == true || 
            transaction.description?.contains(keyword) == true) {
          relatedTransactions.add(transaction);
          break;
        }
      }
    }
    
    if (relatedTransactions.length < 2) return null;
    
    // 标记为已处理
    for (final transaction in relatedTransactions) {
      processedTransactions.add(transaction.id);
    }
    
    final totalAmount = relatedTransactions.fold<double>(0, (sum, t) => sum + t.amount);
    
    return StoryChapter(
      id: 'project_${projectType}_${anchor.id}',
      title: _generateProjectTitle(projectType, relatedTransactions),
      description: '$projectType项目共投入${totalAmount.toStringAsFixed(0)}元，${relatedTransactions.length}次消费',
      transactions: relatedTransactions,
      type: StoryChapterType.project,
      highlights: [projectType, '${relatedTransactions.length}次消费'],
    );
  }
  
  /// 识别社交故事
  static StoryChapter? _identifySocialStory(
    TransactionModel anchor,
    List<TransactionModel> allTransactions,
    Set<String> processedTransactions,
  ) {
    if (anchor.participants.isEmpty) return null;
    
    final relatedTransactions = <TransactionModel>[anchor];
    final timeWindow = Duration(days: 1); // 同一天的社交活动
    
    for (final transaction in allTransactions) {
      if (processedTransactions.contains(transaction.id) || transaction.id == anchor.id) continue;
      
      final timeDiff = anchor.timestamp.difference(transaction.timestamp).abs();
      if (timeDiff > timeWindow) continue;
      
      // 检查是否有共同参与者
      final commonParticipants = anchor.participants.toSet().intersection(transaction.participants.toSet());
      if (commonParticipants.isNotEmpty) {
        relatedTransactions.add(transaction);
      }
    }
    
    if (relatedTransactions.length < 2) return null;
    
    // 标记为已处理
    for (final transaction in relatedTransactions) {
      processedTransactions.add(transaction.id);
    }
    
    final participants = anchor.participants;
    final totalAmount = relatedTransactions.fold<double>(0, (sum, t) => sum + t.amount);
    
    return StoryChapter(
      id: 'social_${anchor.id}',
      title: '与${participants.join('、')}的美好时光',
      description: '一起度过的快乐时光，共消费${totalAmount.toStringAsFixed(0)}元',
      transactions: relatedTransactions,
      type: StoryChapterType.social,
      highlights: participants,
    );
  }
  
  /// 识别习惯故事
  static StoryChapter? _identifyHabitStory(
    TransactionModel anchor,
    List<TransactionModel> allTransactions,
    Set<String> processedTransactions,
  ) {
    // 识别重复的消费习惯，如每周的咖啡、每月的电影等
    final habitPatterns = {
      '咖啡时光': ['咖啡', '星巴克', 'Starbucks', '瑞幸'],
      '电影之夜': ['电影', '影院', '票务'],
      '健身日记': ['健身', '运动', '瑜伽'],
    };
    
    String? habitType;
    List<String>? keywords;
    
    for (final entry in habitPatterns.entries) {
      for (final keyword in entry.value) {
        if (anchor.merchantName?.contains(keyword) == true) {
          habitType = entry.key;
          keywords = entry.value;
          break;
        }
      }
      if (habitType != null) break;
    }
    
    if (habitType == null) return null;
    
    final relatedTransactions = <TransactionModel>[anchor];
    final timeWindow = Duration(days: 30); // 30天内的相似消费
    
    for (final transaction in allTransactions) {
      if (processedTransactions.contains(transaction.id) || transaction.id == anchor.id) continue;
      
      final timeDiff = anchor.timestamp.difference(transaction.timestamp).abs();
      if (timeDiff > timeWindow) continue;
      
      // 检查是否匹配习惯关键词
      for (final keyword in keywords!) {
        if (transaction.merchantName?.contains(keyword) == true) {
          relatedTransactions.add(transaction);
          break;
        }
      }
    }
    
    if (relatedTransactions.length < 3) return null; // 至少3次才算习惯
    
    // 标记为已处理
    for (final transaction in relatedTransactions) {
      processedTransactions.add(transaction.id);
    }
    
    final totalAmount = relatedTransactions.fold<double>(0, (sum, t) => sum + t.amount);
    final avgAmount = totalAmount / relatedTransactions.length;
    
    return StoryChapter(
      id: 'habit_${habitType}_${anchor.id}',
      title: habitType,
      description: '${relatedTransactions.length}次消费，平均每次${avgAmount.toStringAsFixed(0)}元',
      transactions: relatedTransactions,
      type: StoryChapterType.habit,
      highlights: ['${relatedTransactions.length}次', '平均¥${avgAmount.toStringAsFixed(0)}'],
    );
  }
  
  /// 生成旅行标题
  static String _generateTravelTitle(List<String> destinations, List<TransactionModel> transactions) {
    if (destinations.isEmpty) return '一次难忘的旅程';
    
    final duration = _calculateDuration(transactions);
    if (destinations.length == 1) {
      return '$duration${destinations.first}之旅';
    } else {
      return '$duration${destinations.length}城记';
    }
  }
  
  /// 生成项目标题
  static String _generateProjectTitle(String projectType, List<TransactionModel> transactions) {
    final duration = _calculateDuration(transactions);
    return '$duration的$projectType之路';
  }
  
  /// 生成旅行亮点
  static List<String> _generateTravelHighlights(List<TransactionModel> transactions) {
    final highlights = <String>[];
    final categories = transactions.map((t) => t.category).toSet();
    
    if (categories.contains('餐饮')) highlights.add('美食探索');
    if (categories.contains('娱乐')) highlights.add('休闲娱乐');
    if (categories.contains('购物')) highlights.add('购物收获');
    if (categories.contains('交通')) highlights.add('行程足迹');
    
    return highlights;
  }
  
  /// 计算持续时间描述
  static String _calculateDuration(List<TransactionModel> transactions) {
    if (transactions.length <= 1) return '';
    
    final dates = transactions.map((t) => t.timestamp).toList()..sort();
    final duration = dates.last.difference(dates.first).inDays;
    
    if (duration == 0) return '一日';
    if (duration <= 2) return '${duration + 1}日';
    if (duration <= 7) return '一周';
    if (duration <= 30) return '一月';
    return '长期';
  }
}

/// 故事章节模型
class StoryChapter {
  final String id;
  final String title;
  final String description;
  final List<TransactionModel> transactions;
  final StoryChapterType type;
  final List<String> highlights;
  final String? coverImagePath;
  
  const StoryChapter({
    required this.id,
    required this.title,
    required this.description,
    required this.transactions,
    required this.type,
    this.highlights = const [],
    this.coverImagePath,
  });
}

/// 故事章节类型
enum StoryChapterType {
  travel,    // 旅行
  project,   // 项目
  social,    // 社交
  habit,     // 习惯
}
