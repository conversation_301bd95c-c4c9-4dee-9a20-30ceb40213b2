// features/footprint/pages/footprint_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../providers/mock_footprint_provider.dart';
import '../widgets/simple_date_header.dart';
import '../widgets/simple_story_card.dart';
import '../widgets/smart_search_bar.dart';
import '../models/footprint_models.dart';
import '../../home/<USER>/transaction_model.dart';

/// 足迹页面 - 简化版本
class FootprintPage extends ConsumerStatefulWidget {
  const FootprintPage({super.key});

  @override
  ConsumerState<FootprintPage> createState() => _FootprintPageState();
}

class _FootprintPageState extends ConsumerState<FootprintPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(mockFootprintProvider.notifier).loadFootprints();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final footprintState = ref.watch(mockFootprintProvider);

    return Scaffold(
      backgroundColor: colors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F46E5),
              const Color(0xFF7C3AED),
              colors.background,
            ],
            stops: const [0.0, 0.3, 0.6],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部标题栏
              _buildHeader(theme, colors),

              // 搜索栏（可选显示）
              if (_showSearchBar) _buildSearchBar(theme, colors),

              // 主要内容区域
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: colors.background,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: footprintState.isLoading
                      ? _buildLoadingState()
                      : footprintState.dateGroups.isEmpty
                          ? _buildEmptyState(theme, colors)
                          : _buildSimpleContent(footprintState, theme, colors),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建顶部标题栏
  Widget _buildHeader(FThemeData theme, FColorScheme colors) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '足迹',
                  style: theme.typography.xl2.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '记录生活的每一个瞬间',
                  style: theme.typography.base.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: () {
              setState(() {
                _showSearchBar = !_showSearchBar;
                if (!_showSearchBar) {
                  _searchController.clear();
                }
              });
            },
            child: Icon(
              _showSearchBar ? Icons.close : Icons.search,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar(FThemeData theme, FColorScheme colors) {
    return SmartSearchBar(
      controller: _searchController,
      onSearch: (filter) {
        // 应用搜索筛选
        setState(() {
          _showSearchBar = false;
        });
      },
      onClear: () {
        // 清除筛选
      },
    );
  }

  /// 构建简化的内容区域
  Widget _buildSimpleContent(FootprintState state, FThemeData theme, FColorScheme colors) {
    return RefreshIndicator(
      onRefresh: () => ref.read(mockFootprintProvider.notifier).loadFootprints(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.only(top: 8, bottom: 100),
        itemCount: _calculateItemCount(state.dateGroups),
        itemBuilder: (context, index) {
          return _buildListItem(index, state.dateGroups, theme, colors);
        },
      ),
    );
  }

  /// 计算列表项总数
  int _calculateItemCount(List<FootprintDateGroup> dateGroups) {
    int count = 0;
    for (final group in dateGroups) {
      count += 1; // 日期头部
      count += group.transactions.length; // 交易卡片
    }
    return count;
  }

  /// 构建列表项
  Widget _buildListItem(int index, List<FootprintDateGroup> dateGroups, FThemeData theme, FColorScheme colors) {
    int currentIndex = 0;
    
    for (final group in dateGroups) {
      // 检查是否是日期头部
      if (currentIndex == index) {
        return SimpleDateHeader(
          date: group.date,
          totalAmount: group.totalAmount,
          transactionCount: group.transactions.length,
        );
      }
      currentIndex++;
      
      // 检查是否是交易卡片
      for (int i = 0; i < group.transactions.length; i++) {
        if (currentIndex == index) {
          return SimpleStoryCard(
            transaction: group.transactions[i],
            showDate: false, // 不显示日期，因为有日期头部
            onTap: () => _showTransactionDetail(group.transactions[i]),
          );
        }
        currentIndex++;
      }
    }
    
    return const SizedBox.shrink();
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(FThemeData theme, FColorScheme colors) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 64,
            color: colors.mutedForeground,
          ),
          const SizedBox(height: 16),
          Text(
            '还没有足迹记录',
            style: theme.typography.xl.copyWith(
              color: colors.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始记录你的生活故事吧',
            style: theme.typography.base.copyWith(
              color: colors.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示交易详情
  void _showTransactionDetail(TransactionModel transaction) {
    context.push('/transaction/${transaction.id}');
  }
}
