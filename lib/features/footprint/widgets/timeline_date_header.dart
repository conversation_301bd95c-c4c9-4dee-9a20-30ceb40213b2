// features/footprint/widgets/timeline_date_header.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';

/// 时间轴日期头部组件 - 支持粘性头部效果
class TimelineDateHeader extends StatelessWidget {
  final DateTime date;
  final double totalAmount;
  final int transactionCount;
  final bool isSticky;

  const TimelineDateHeader({
    super.key,
    required this.date,
    required this.totalAmount,
    required this.transactionCount,
    this.isSticky = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isSticky
            ? colors.background.withValues(alpha: 0.95)
            : colors.background,
        border: isSticky 
            ? Border(bottom: BorderSide(color: colors.border, width: 1))
            : null,
      ),
      child: Row(
        children: [
          // 日期信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getDisplayTitle(),
                  style: theme.typography.xl.copyWith(
                    color: colors.foreground,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                if (_getDisplayTitle() != _getFullDateString()) ...[
                  const SizedBox(height: 2),
                  Text(
                    _getFullDateString(),
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 统计信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (totalAmount != 0) ...[
                Text(
                  '${totalAmount > 0 ? '+' : ''}¥${totalAmount.abs().toStringAsFixed(2)}',
                  style: theme.typography.base.copyWith(
                    color: totalAmount > 0 ? Colors.green : colors.destructive,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
              ],
              Text(
                '$transactionCount笔记录',
                style: theme.typography.sm.copyWith(
                  color: colors.mutedForeground,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取显示标题（今天、昨天、具体日期）
  String _getDisplayTitle() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = today.difference(targetDate).inDays;
    
    switch (difference) {
      case 0:
        return '今天';
      case 1:
        return '昨天';
      case 2:
        return '前天';
      default:
        if (difference < 7) {
          return '$difference天前';
        } else if (date.year == now.year) {
          return DateFormat('M月d日', 'zh_CN').format(date);
        } else {
          return DateFormat('yyyy年M月d日', 'zh_CN').format(date);
        }
    }
  }

  /// 获取完整日期字符串
  String _getFullDateString() {
    final now = DateTime.now();
    
    if (date.year == now.year) {
      return DateFormat('M月d日 EEEE', 'zh_CN').format(date);
    } else {
      return DateFormat('yyyy年M月d日 EEEE', 'zh_CN').format(date);
    }
  }
}
