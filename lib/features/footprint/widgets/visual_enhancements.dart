// features/footprint/widgets/visual_enhancements.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

/// 渐变背景装饰器
class GradientBackgroundDecorator extends StatelessWidget {
  final Widget child;
  final List<Color> colors;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;

  const GradientBackgroundDecorator({
    super.key,
    required this.child,
    required this.colors,
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: colors,
        ),
      ),
      child: child,
    );
  }
}

/// 浮动动作按钮
class FloatingStoryButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String tooltip;
  final IconData icon;

  const FloatingStoryButton({
    super.key,
    this.onPressed,
    this.tooltip = '添加故事',
    this.icon = FIcons.plus,
  });

  @override
  State<FloatingStoryButton> createState() => _FloatingStoryButtonState();
}

class _FloatingStoryButtonState extends State<FloatingStoryButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: FloatingActionButton(
              onPressed: () {
                _controller.forward().then((_) {
                  _controller.reverse();
                });
                widget.onPressed?.call();
              },
              tooltip: widget.tooltip,
              backgroundColor: const Color(0xFF4F46E5),
              foregroundColor: Colors.white,
              elevation: 8,
              child: Icon(widget.icon),
            ),
          ),
        );
      },
    );
  }
}

/// 故事卡片装饰器
class StoryCardDecorator extends StatelessWidget {
  final Widget child;
  final Color accentColor;
  final bool isHighlighted;

  const StoryCardDecorator({
    super.key,
    required this.child,
    required this.accentColor,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          if (isHighlighted) ...[
            BoxShadow(
              color: accentColor.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
          ],
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: child,
    );
  }
}

/// 时间轴连接线装饰器
class TimelineConnectorDecorator extends StatelessWidget {
  final Widget child;
  final Color color;
  final double thickness;
  final bool isDashed;

  const TimelineConnectorDecorator({
    super.key,
    required this.child,
    required this.color,
    this.thickness = 2.0,
    this.isDashed = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: isDashed 
          ? DashedLinePainter(color: color, thickness: thickness)
          : SolidLinePainter(color: color, thickness: thickness),
      child: child,
    );
  }
}

/// 虚线画笔
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double thickness;

  DashedLinePainter({required this.color, required this.thickness});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    const dashWidth = 5.0;
    const dashSpace = 3.0;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashWidth),
        paint,
      );
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 实线画笔
class SolidLinePainter extends CustomPainter {
  final Color color;
  final double thickness;

  SolidLinePainter({required this.color, required this.thickness});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 情感色彩指示器
class EmotionalColorIndicator extends StatelessWidget {
  final String emotion;
  final double intensity;
  final double size;

  const EmotionalColorIndicator({
    super.key,
    required this.emotion,
    required this.intensity,
    this.size = 12.0,
  });

  Color get _emotionColor {
    switch (emotion.toLowerCase()) {
      case 'happy':
      case '开心':
        return Colors.yellow;
      case 'excited':
      case '兴奋':
        return Colors.orange;
      case 'calm':
      case '平静':
        return Colors.blue;
      case 'romantic':
      case '浪漫':
        return Colors.pink;
      case 'nostalgic':
      case '怀念':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _emotionColor.withValues(alpha: intensity),
        border: Border.all(
          color: _emotionColor,
          width: 1,
        ),
      ),
    );
  }
}

/// 故事标签
class StoryTag extends StatelessWidget {
  final String text;
  final Color color;
  final IconData? icon;

  const StoryTag({
    super.key,
    required this.text,
    required this.color,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 12,
              color: color,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: theme.typography.sm.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }
}

/// 交互式卡片包装器
class InteractiveCardWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool enableHoverEffect;

  const InteractiveCardWrapper({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.enableHoverEffect = true,
  });

  @override
  State<InteractiveCardWrapper> createState() => _InteractiveCardWrapperState();
}

class _InteractiveCardWrapperState extends State<InteractiveCardWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _controller.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _controller.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _controller.reverse();
      },
      onLongPress: widget.onLongPress,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}
