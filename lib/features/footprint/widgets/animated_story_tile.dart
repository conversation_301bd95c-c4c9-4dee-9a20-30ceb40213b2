// features/footprint/widgets/animated_story_tile.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'timeline_story_tile.dart';
import '../../home/<USER>/transaction_model.dart';

/// 带动画效果的故事瓦片包装器
class AnimatedStoryTile extends StatefulWidget {
  final TransactionModel transaction;
  final bool isFirst;
  final bool isLast;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onAddContent;
  final int index;
  final bool isAlternating; // 是否交错显示

  const AnimatedStoryTile({
    super.key,
    required this.transaction,
    required this.index,
    this.isFirst = false,
    this.isLast = false,
    this.onTap,
    this.onLongPress,
    this.onAddContent,
    this.isAlternating = true,
  });

  @override
  State<AnimatedStoryTile> createState() => _AnimatedStoryTileState();
}

class _AnimatedStoryTileState extends State<AnimatedStoryTile>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _slideController = AnimationController(
      duration: Duration(milliseconds: 600 + (widget.index * 100)),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: Duration(milliseconds: 400 + (widget.index * 50)),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 800 + (widget.index * 80)),
      vsync: this,
    );
    
    // 设置动画
    _slideAnimation = Tween<Offset>(
      begin: widget.isAlternating && widget.index % 2 == 0
          ? const Offset(-1.0, 0.0)  // 从左侧滑入
          : const Offset(1.0, 0.0),  // 从右侧滑入
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
    
    // 启动动画
    _startAnimations();
  }

  void _startAnimations() async {
    // 添加延迟以创建瀑布效果
    await Future.delayed(Duration(milliseconds: widget.index * 150));
    
    if (mounted) {
      _fadeController.forward();
      await Future.delayed(const Duration(milliseconds: 100));
      
      if (mounted) {
        _slideController.forward();
        await Future.delayed(const Duration(milliseconds: 200));
        
        if (mounted) {
          _scaleController.forward();
        }
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_slideController, _scaleController, _fadeController]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onTap?.call();
                },
                onLongPress: () {
                  HapticFeedback.mediumImpact();
                  widget.onLongPress?.call();
                },
                child: TimelineStoryTile(
                  transaction: widget.transaction,
                  isFirst: widget.isFirst,
                  isLast: widget.isLast,
                  onTap: widget.onTap,
                  onLongPress: widget.onLongPress,
                  onAddContent: widget.onAddContent,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 视差滚动容器
class ParallaxContainer extends StatelessWidget {
  final Widget child;
  final double parallaxFactor;
  final ScrollController scrollController;

  const ParallaxContainer({
    super.key,
    required this.child,
    required this.scrollController,
    this.parallaxFactor = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: scrollController,
      builder: (context, child) {
        final scrollOffset = scrollController.hasClients 
            ? scrollController.offset 
            : 0.0;
        
        return Transform.translate(
          offset: Offset(0, scrollOffset * parallaxFactor),
          child: this.child,
        );
      },
      child: child,
    );
  }
}

/// 脉冲动画指示器
class PulsingIndicator extends StatefulWidget {
  final Widget child;
  final Color color;
  final Duration duration;

  const PulsingIndicator({
    super.key,
    required this.child,
    required this.color,
    this.duration = const Duration(seconds: 2),
  });

  @override
  State<PulsingIndicator> createState() => _PulsingIndicatorState();
}

class _PulsingIndicatorState extends State<PulsingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: widget.color.withValues(alpha: 0.3 * _animation.value),
                blurRadius: 20 * _animation.value,
                spreadRadius: 5 * _animation.value,
              ),
            ],
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// 交错布局动画
class StaggeredAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration duration;
  final Curve curve;

  const StaggeredAnimation({
    super.key,
    required this.children,
    this.delay = const Duration(milliseconds: 100),
    this.duration = const Duration(milliseconds: 600),
    this.curve = Curves.easeOutCubic,
  });

  @override
  State<StaggeredAnimation> createState() => _StaggeredAnimationState();
}

class _StaggeredAnimationState extends State<StaggeredAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        duration: widget.duration,
        vsync: this,
      ),
    );
    
    _animations = _controllers.map((controller) =>
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: widget.curve),
      ),
    ).toList();
    
    _startStaggeredAnimation();
  }

  void _startStaggeredAnimation() async {
    for (int i = 0; i < _controllers.length; i++) {
      if (mounted) {
        _controllers[i].forward();
        await Future.delayed(widget.delay);
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        widget.children.length,
        (index) => AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 50 * (1 - _animations[index].value)),
              child: Opacity(
                opacity: _animations[index].value,
                child: widget.children[index],
              ),
            );
          },
        ),
      ),
    );
  }
}
