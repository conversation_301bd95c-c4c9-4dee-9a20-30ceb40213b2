// features/footprint/widgets/story_chapter_card.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import '../../home/<USER>/transaction_model.dart';

/// 故事章节卡片 - 用于显示AI聚合的消费故事
class StoryChapterCard extends StatelessWidget {
  final String chapterTitle;
  final String chapterDescription;
  final List<TransactionModel> transactions;
  final String? coverImagePath;
  final List<String> highlights;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const StoryChapterCard({
    super.key,
    required this.chapterTitle,
    required this.chapterDescription,
    required this.transactions,
    this.coverImagePath,
    this.highlights = const [],
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    final totalAmount = transactions.fold<double>(
      0, 
      (sum, t) => sum + (t.type == TransactionType.expense ? t.amount : -t.amount),
    );
    
    final dateRange = _getDateRange();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: InkWell(
        onTap: onTap,
        onLongPress: () {
          HapticFeedback.mediumImpact();
          onLongPress?.call();
        },
        borderRadius: BorderRadius.circular(24),
        child: Container(
          height: 280,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF4F46E5),
                const Color(0xFF7C3AED),
                const Color(0xFF8B5CF6),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4F46E5).withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: Stack(
              children: [
                // 背景图片或装饰
                if (coverImagePath != null)
                  Positioned.fill(
                    child: Image.asset(
                      coverImagePath!,
                      fit: BoxFit.cover,
                    ),
                  ),
                
                // 渐变遮罩
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.3),
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // 装饰图案
                Positioned(
                  top: -50,
                  right: -50,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                ),
                
                Positioned(
                  bottom: -30,
                  left: -30,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.05),
                    ),
                  ),
                ),
                
                // 主要内容
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 章节标题
                      Text(
                        chapterTitle,
                        style: theme.typography.xl.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w800,
                          height: 1.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // 日期范围
                      Text(
                        dateRange,
                        style: theme.typography.sm.copyWith(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      
                      const Spacer(),
                      
                      // 亮点标签
                      if (highlights.isNotEmpty) ...[
                        Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children: highlights.take(3).map((highlight) => 
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                highlight,
                                style: theme.typography.sm.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // 底部信息
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              chapterDescription,
                              style: theme.typography.base.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '¥${totalAmount.abs().toStringAsFixed(0)}',
                                style: theme.typography.xl.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                '${transactions.length}笔记录',
                                style: theme.typography.sm.copyWith(
                                  color: Colors.white.withValues(alpha: 0.8),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 右上角图标
                Positioned(
                  top: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      FIcons.bookOpen,
                      size: 20,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取日期范围描述
  String _getDateRange() {
    if (transactions.isEmpty) return '';
    
    final dates = transactions.map((t) => t.timestamp).toList()..sort();
    final startDate = dates.first;
    final endDate = dates.last;
    
    if (startDate.day == endDate.day && 
        startDate.month == endDate.month && 
        startDate.year == endDate.year) {
      return DateFormat('M月d日').format(startDate);
    }
    
    if (startDate.month == endDate.month && startDate.year == endDate.year) {
      return '${DateFormat('M月d日').format(startDate)} - ${DateFormat('d日').format(endDate)}';
    }
    
    return '${DateFormat('M月d日').format(startDate)} - ${DateFormat('M月d日').format(endDate)}';
  }
}
