// features/footprint/widgets/story_card.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import '../../home/<USER>/transaction_model.dart';

/// 故事卡片组件 - 足迹模块的核心UI元素
class StoryCard extends StatelessWidget {
  final TransactionModel transaction;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onAddPhoto;
  final VoidCallback? onAddMood;

  const StoryCard({
    super.key,
    required this.transaction,
    this.onTap,
    this.onLongPress,
    this.onAddPhoto,
    this.onAddMood,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: FCard.raw(
        child: InkWell(
          onTap: onTap,
          onLongPress: () {
            HapticFeedback.mediumImpact();
            onLongPress?.call();
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: transaction.hasStoryContent
                ? _buildPhotoStoryCard(context, theme, colors)
                : _buildRegularStoryCard(context, theme, colors),
          ),
        ),
      ),
    );
  }

  /// 构建带照片的故事卡片
  Widget _buildPhotoStoryCard(BuildContext context, ShadThemeData theme, ShadColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 照片区域
        if (transaction.photoPath != null)
          Container(
            width: double.infinity,
            height: 200,
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(transaction.photoPath!), // 实际项目中应该使用网络图片或文件图片
                fit: BoxFit.cover,
              ),
            ),
          ),
        
        // 故事内容
        _buildStoryContent(context, theme, colorScheme),
      ],
    );
  }

  /// 构建常规故事卡片
  Widget _buildRegularStoryCard(BuildContext context, ShadThemeData theme, ShadColorScheme colorScheme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧图标
        _buildCategoryIcon(colorScheme),
        const SizedBox(width: 12),
        
        // 右侧内容
        Expanded(
          child: _buildStoryContent(context, theme, colorScheme),
        ),
        
        // 右侧操作按钮
        _buildActionButton(colorScheme),
      ],
    );
  }

  /// 构建类别图标
  Widget _buildCategoryIcon(ShadColorScheme colorScheme) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: colorScheme.accent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        _getCategoryIcon(),
        size: 24,
        color: colorScheme.accentForeground,
      ),
    );
  }

  /// 构建故事内容
  Widget _buildStoryContent(BuildContext context, ShadThemeData theme, ShadColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 叙事标题
        Text(
          transaction.aiNarrativeTitle ?? _generateDefaultNarrative(),
          style: theme.textTheme.p.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.foreground,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        
        // 数据细节
        Row(
          children: [
            if (transaction.merchantName != null) ...[
              Text(
                transaction.merchantName!,
                style: theme.textTheme.small.copyWith(
                  color: colorScheme.mutedForeground,
                ),
              ),
              Text(
                ' · ',
                style: theme.textTheme.small.copyWith(
                  color: colorScheme.mutedForeground,
                ),
              ),
            ],
            Text(
              '${transaction.type == TransactionType.expense ? '-' : '+'}¥${transaction.amount.toStringAsFixed(2)}',
              style: theme.textTheme.small.copyWith(
                color: transaction.type == TransactionType.expense 
                    ? colorScheme.destructive 
                    : Colors.green,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        
        // 附加信息
        Row(
          children: [
            Text(
              DateFormat('HH:mm').format(transaction.timestamp),
              style: theme.textTheme.small.copyWith(
                color: colorScheme.mutedForeground,
              ),
            ),
            if (transaction.geoLocation != null) ...[
              Text(
                ' · ',
                style: theme.textTheme.small.copyWith(
                  color: colorScheme.mutedForeground,
                ),
              ),
              Flexible(
                child: Text(
                  transaction.geoLocation!,
                  style: theme.textTheme.small.copyWith(
                    color: colorScheme.mutedForeground,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
        
        // 参与人员标签
        if (transaction.participants.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 4,
            children: transaction.participants.map((participant) => 
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: colorScheme.secondary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  participant,
                  style: theme.textTheme.small.copyWith(
                    color: colorScheme.secondaryForeground,
                    fontSize: 10,
                  ),
                ),
              ),
            ).toList(),
          ),
        ],
        
        // 心情表情
        if (transaction.moodEmoji != null) ...[
          const SizedBox(height: 8),
          Text(
            transaction.moodEmoji!,
            style: const TextStyle(fontSize: 20),
          ),
        ],
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(FColorScheme colors) {
    return FButton.icon(
      style: FButtonStyle.ghost(),
      onPress: () {
        if (transaction.hasStoryContent) {
          // 查看详情
          onTap?.call();
        } else {
          // 添加照片或心情
          _showAddContentBottomSheet();
        }
      },
      child: Icon(
        transaction.hasStoryContent ? Icons.image : Icons.add,
        size: 16,
        color: colors.mutedForeground,
      ),
    );
  }

  /// 显示添加内容的底部弹窗
  void _showAddContentBottomSheet() {
    // 导入添加故事内容弹窗
    // showAddStoryContentSheet(context, transaction);
    onAddPhoto?.call();
  }

  /// 根据类别获取图标
  IconData _getCategoryIcon() {
    switch (transaction.category.toLowerCase()) {
      case '餐饮':
      case 'food':
        return Icons.restaurant;
      case '交通':
      case 'transport':
        return Icons.directions_car;
      case '购物':
      case 'shopping':
        return Icons.shopping_bag;
      case '娱乐':
      case 'entertainment':
        return Icons.games;
      case '医疗':
      case 'medical':
        return Icons.favorite;
      case '教育':
      case 'education':
        return Icons.school;
      case '旅行':
      case 'travel':
        return Icons.flight;
      case '工资':
      case 'salary':
        return Icons.attach_money;
      default:
        return Icons.account_balance_wallet;
    }
  }

  /// 生成默认叙事标题
  String _generateDefaultNarrative() {
    final category = transaction.category;
    final amount = transaction.amount;
    final participants = transaction.participants;
    
    if (participants.isNotEmpty) {
      return '与${participants.join('、')}一起的$category时光';
    }
    
    switch (category.toLowerCase()) {
      case '餐饮':
        return amount > 100 ? '享受了一顿丰盛的美食' : '品尝了简单的美味';
      case '交通':
        return '踏上了一段旅程';
      case '购物':
        return amount > 200 ? '进行了一次愉快的购物' : '买到了心仪的小物件';
      case '娱乐':
        return '度过了快乐的休闲时光';
      case '工资':
        return '收获了辛勤工作的回报';
      default:
        return '记录了生活中的一笔${transaction.type == TransactionType.expense ? '支出' : '收入'}';
    }
  }
}
