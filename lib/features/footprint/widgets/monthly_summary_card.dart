// features/footprint/widgets/monthly_summary_card.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../models/footprint_models.dart';

/// 月份总结卡片组件
class MonthlySummaryCard extends StatelessWidget {
  final MonthlySummary summary;
  final VoidCallback? onTap;

  const MonthlySummaryCard({
    super.key,
    required this.summary,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colors.primary.withValues(alpha: 0.1),
              colors.secondary.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: colors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 月份标识
                _buildMonthHeader(theme, colors),
                const SizedBox(height: 12),
                
                // AI生成的标题
                Text(
                  summary.title,
                  style: theme.typography.xl.copyWith(
                    color: colors.foreground,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 8),

                // AI生成的描述
                Text(
                  summary.description,
                  style: theme.typography.base.copyWith(
                    color: colors.mutedForeground,
                    height: 1.5,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 16),
                
                // 亮点标签
                if (summary.highlights.isNotEmpty) ...[
                  Wrap(
                    spacing: 8,
                    runSpacing: 6,
                    children: summary.highlights.map((highlight) => 
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: colors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: colors.primary.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          highlight,
                          style: theme.typography.sm.copyWith(
                            color: colors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ).toList(),
                  ),
                  const SizedBox(height: 16),
                ],
                
                // 统计数据
                _buildStatistics(theme, colors),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建月份头部
  Widget _buildMonthHeader(FThemeData theme, FColorScheme colors) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: colors.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '${summary.year}年${summary.month}月',
            style: theme.typography.sm.copyWith(
              color: colors.primaryForeground,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const Spacer(),
        Icon(
          Icons.calendar_today,
          size: 20,
          color: colors.mutedForeground,
        ),
      ],
    );
  }

  /// 构建统计数据
  Widget _buildStatistics(FThemeData theme, FColorScheme colors) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.background.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.border.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // 收支统计
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  theme,
                  colors,
                  '总支出',
                  '¥${summary.totalExpense.toStringAsFixed(2)}',
                  colors.destructive,
                  Icons.trending_down,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: colors.border,
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  colors,
                  '总收入',
                  '¥${summary.totalIncome.toStringAsFixed(2)}',
                  Colors.green,
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 交易次数
          _buildStatItem(
            theme,
            colors,
            '交易次数',
            '${summary.transactionCount}笔',
            colors.primary,
            Icons.show_chart,
          ),
          
          // 主要类别
          if (summary.topCategories.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildTopCategories(theme, colors),
          ],
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    FThemeData theme,
    FColorScheme colors,
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.typography.sm.copyWith(
                  color: colors.mutedForeground,
                ),
              ),
              Text(
                value,
                style: theme.typography.base.copyWith(
                  color: colors.foreground,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建主要类别
  Widget _buildTopCategories(FThemeData theme, FColorScheme colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主要支出类别',
          style: theme.typography.sm.copyWith(
            color: colors.mutedForeground,
          ),
        ),
        const SizedBox(height: 8),
        ...summary.topCategories.take(3).map((category) => 
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: colors.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  category.category,
                  style: theme.typography.sm.copyWith(
                    color: colors.foreground,
                  ),
                ),
                const Spacer(),
                Text(
                  '¥${category.amount.toStringAsFixed(0)}',
                  style: theme.typography.sm.copyWith(
                    color: colors.mutedForeground,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${category.percentage.toStringAsFixed(1)}%',
                  style: theme.typography.sm.copyWith(
                    color: colors.mutedForeground,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
