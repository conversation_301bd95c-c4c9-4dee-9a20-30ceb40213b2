// features/footprint/widgets/timeline_date_separator.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:intl/intl.dart';

/// 时间轴日期分隔符组件
class TimelineDateSeparator extends StatelessWidget {
  final DateTime date;
  final double totalAmount;
  final int transactionCount;
  final bool isFirst;

  const TimelineDateSeparator({
    super.key,
    required this.date,
    required this.totalAmount,
    required this.transactionCount,
    this.isFirst = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return TimelineTile(
      oppositeContents: const SizedBox(width: 60), // 占位符，保持对齐
      contents: _buildDateContent(theme, colors),
      node: TimelineNode(
        indicator: _buildDateIndicator(theme, colors),
        startConnector: isFirst ? null : SolidLineConnector(
          color: colors.border,
          thickness: 2,
        ),
        endConnector: SolidLineConnector(
          color: colors.border,
          thickness: 2,
        ),
      ),
    );
  }

  /// 构建日期指示器
  Widget _buildDateIndicator(ShadThemeData theme, FColors colors) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4F46E5),
            const Color(0xFF7C3AED),
          ],
        ),
        shape: BoxShape.circle,
        border: Border.all(
          color: colors.background,
          width: 4,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4F46E5).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          date.day.toString(),
          style: theme.typography.base.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w700,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  /// 构建日期内容
  Widget _buildDateContent(FThemeData theme, FColorScheme colors) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.border.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期标题
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getDisplayTitle(),
                      style: theme.typography.xl.copyWith(
                        color: colors.foreground,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    if (_getDisplayTitle() != _getFullDateString()) ...[
                      const SizedBox(height: 2),
                      Text(
                        _getFullDateString(),
                        style: theme.typography.sm.copyWith(
                          color: colors.mutedForeground,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // 统计信息
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (totalAmount != 0) ...[
                    Text(
                      '${totalAmount > 0 ? '+' : ''}¥${totalAmount.abs().toStringAsFixed(2)}',
                      style: theme.typography.base.copyWith(
                        color: totalAmount > 0 ? Colors.green : colors.destructive,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                  ],
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: colors.secondary.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '$transactionCount笔记录',
                      style: theme.typography.sm.copyWith(
                        color: colors.secondaryForeground,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          // 分隔线
          const SizedBox(height: 12),
          Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colors.border.withValues(alpha: 0.1),
                  colors.border.withValues(alpha: 0.5),
                  colors.border.withValues(alpha: 0.1),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取显示标题（今天、昨天、具体日期）
  String _getDisplayTitle() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = today.difference(targetDate).inDays;
    
    switch (difference) {
      case 0:
        return '今天';
      case 1:
        return '昨天';
      case 2:
        return '前天';
      default:
        if (difference < 7) {
          return '$difference天前';
        } else if (date.year == now.year) {
          return DateFormat('M月d日', 'zh_CN').format(date);
        } else {
          return DateFormat('yyyy年M月d日', 'zh_CN').format(date);
        }
    }
  }

  /// 获取完整日期字符串
  String _getFullDateString() {
    final now = DateTime.now();
    
    if (date.year == now.year) {
      return DateFormat('M月d日 EEEE', 'zh_CN').format(date);
    } else {
      return DateFormat('yyyy年M月d日 EEEE', 'zh_CN').format(date);
    }
  }
}
