// features/footprint/widgets/simple_date_header.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

/// 简洁的日期头部组件
class SimpleDateHeader extends StatelessWidget {
  final DateTime date;
  final double totalAmount;
  final int transactionCount;

  const SimpleDateHeader({
    super.key,
    required this.date,
    required this.totalAmount,
    required this.transactionCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colors.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.border.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 日期信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatDate(),
                  style: theme.typography.base.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colors.foreground,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '$transactionCount笔记录',
                  style: theme.typography.sm.copyWith(
                    color: colors.mutedForeground,
                  ),
                ),
              ],
            ),
          ),
          
          // 总金额
          if (totalAmount != 0) ...[
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${totalAmount > 0 ? '+' : ''}¥${totalAmount.abs().toStringAsFixed(0)}',
                  style: theme.typography.base.copyWith(
                    fontWeight: FontWeight.w700,
                    color: totalAmount > 0
                        ? Colors.green
                        : colors.destructive,
                  ),
                ),
                Text(
                  '当日${totalAmount > 0 ? '收入' : '支出'}',
                  style: theme.typography.sm.copyWith(
                    color: colors.mutedForeground,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    final diff = today.difference(targetDate).inDays;
    
    if (diff == 0) {
      return '今天';
    } else if (diff == 1) {
      return '昨天';
    } else if (diff == 2) {
      return '前天';
    } else if (diff < 7) {
      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      return weekdays[date.weekday - 1];
    } else if (date.year == now.year) {
      return '${date.month}月${date.day}日';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }
}
