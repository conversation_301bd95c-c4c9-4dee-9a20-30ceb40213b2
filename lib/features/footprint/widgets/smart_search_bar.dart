// features/footprint/widgets/smart_search_bar.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../models/footprint_models.dart';
import '/shared/l10n/app_strings.dart';

/// 智能搜索栏 - 支持自然语言搜索
class SmartSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final Function(FootprintFilter) onSearch;
  final VoidCallback? onClear;

  const SmartSearchBar({
    super.key,
    required this.controller,
    required this.onSearch,
    this.onClear,
  });

  @override
  State<SmartSearchBar> createState() => _SmartSearchBarState();
}

class _SmartSearchBarState extends State<SmartSearchBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _showSuggestions = false;
  List<SearchSuggestion> _suggestions = [];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    if (text.isEmpty) {
      setState(() {
        _showSuggestions = false;
        _suggestions = [];
      });
      _animationController.reverse();
    } else {
      _generateSuggestions(text);
      if (!_showSuggestions) {
        setState(() {
          _showSuggestions = true;
        });
        _animationController.forward();
      }
    }
  }

  void _generateSuggestions(String query) {
    final suggestions = <SearchSuggestion>[];
    
    // 基于查询内容生成智能建议
    if (query.contains('上个月') || query.contains('上月')) {
      suggestions.add(SearchSuggestion(
        title: '上个月的所有记录',
        description: '查看上个月的消费足迹',
        filter: FootprintFilter(
          startDate: DateTime.now().subtract(const Duration(days: 60)),
          endDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        icon: Icons.calendar_today,
      ));
    }
    
    if (query.contains('和') || query.contains('与')) {
      // 提取人名
      final names = _extractNames(query);
      if (names.isNotEmpty) {
        suggestions.add(SearchSuggestion(
          title: '与${names.join('、')}的记录',
          description: '查看与这些朋友的消费记录',
          filter: FootprintFilter(participants: names),
          icon: Icons.people,
        ));
      }
    }
    
    if (query.contains('吃饭') || query.contains('餐厅') || query.contains('美食')) {
      suggestions.add(SearchSuggestion(
        title: '餐饮消费记录',
        description: '查看所有餐饮相关的消费',
        filter: const FootprintFilter(categories: ['餐饮', 'food']),
        icon: Icons.restaurant,
      ));
    }
    
    if (query.contains('旅行') || query.contains('旅游') || query.contains('出行')) {
      suggestions.add(SearchSuggestion(
        title: '旅行足迹',
        description: '查看旅行相关的所有消费',
        filter: const FootprintFilter(categories: ['交通', '住宿', '旅行']),
        icon: Icons.flight,
      ));
    }
    
    if (query.contains('购物') || query.contains('买')) {
      suggestions.add(SearchSuggestion(
        title: '购物记录',
        description: '查看购物消费记录',
        filter: const FootprintFilter(categories: ['购物', 'shopping']),
        icon: Icons.shopping_bag,
      ));
    }
    
    // 添加通用搜索建议
    suggestions.add(SearchSuggestion(
      title: '${AppStrings.get('searchIn')} "$query"',
      description: AppStrings.get('searchInAllRecords'),
      filter: FootprintFilter(searchQuery: query),
      icon: Icons.search,
    ));
    
    setState(() {
      _suggestions = suggestions;
    });
  }

  List<String> _extractNames(String query) {
    // 简单的人名提取逻辑，实际项目中可以使用更复杂的NLP
    final names = <String>[];
    final patterns = [
      RegExp(r'和(\w+)'),
      RegExp(r'与(\w+)'),
      RegExp(r'跟(\w+)'),
    ];
    
    for (final pattern in patterns) {
      final matches = pattern.allMatches(query);
      for (final match in matches) {
        if (match.group(1) != null) {
          names.add(match.group(1)!);
        }
      }
    }
    
    return names;
  }

  void _onSuggestionTap(SearchSuggestion suggestion) {
    widget.controller.text = suggestion.title;
    widget.onSearch(suggestion.filter);
    setState(() {
      _showSuggestions = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Column(
      children: [
        // 搜索输入框
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // 搜索图标
              Padding(
                padding: const EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.search,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 18,
                ),
              ),
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  style: theme.typography.base.copyWith(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: '搜索你的足迹，如"上个月和小明吃饭"',
                    hintStyle: theme.typography.base.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      final filter = FootprintFilter(searchQuery: value);
                      widget.onSearch(filter);
                    }
                  },
                ),
              ),
              if (widget.controller.text.isNotEmpty) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    widget.controller.clear();
                    widget.onClear?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.close,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 16,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // 搜索建议
        if (_showSuggestions)
          FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: _suggestions.map((suggestion) =>
                  _buildSuggestionItem(suggestion, theme, colors),
                ).toList(),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSuggestionItem(
    SearchSuggestion suggestion,
    FThemeData theme,
    FColorScheme colors,
  ) {
    return InkWell(
      onTap: () => _onSuggestionTap(suggestion),
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                suggestion.icon,
                size: 16,
                color: colors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.title,
                    style: theme.typography.base.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.foreground,
                    ),
                  ),
                  if (suggestion.description.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      suggestion.description,
                      style: theme.typography.sm.copyWith(
                        color: colors.mutedForeground,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              Icons.arrow_outward,
              size: 16,
              color: colors.mutedForeground,
            ),
          ],
        ),
      ),
    );
  }
}

/// 搜索建议模型
class SearchSuggestion {
  final String title;
  final String description;
  final FootprintFilter filter;
  final IconData icon;

  const SearchSuggestion({
    required this.title,
    required this.description,
    required this.filter,
    required this.icon,
  });
}
