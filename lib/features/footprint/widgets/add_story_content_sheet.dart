// features/footprint/widgets/add_story_content_sheet.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../../home/<USER>/transaction_model.dart';
import '../providers/footprint_provider.dart';

/// 添加故事内容的底部弹窗
class AddStoryContentSheet extends ConsumerStatefulWidget {
  final TransactionModel transaction;

  const AddStoryContentSheet({
    super.key,
    required this.transaction,
  });

  @override
  ConsumerState<AddStoryContentSheet> createState() => _AddStoryContentSheetState();
}

class _AddStoryContentSheetState extends ConsumerState<AddStoryContentSheet> {
  String? selectedMood;
  final TextEditingController _narrativeController = TextEditingController();

  // 常用心情表情
  final List<String> _commonMoods = [
    '😊', '😄', '🥰', '😍', '🤗', '😌', '😎', '🤔',
    '😋', '🤤', '😴', '🥳', '🎉', '💪', '👍', '❤️',
    '🔥', '✨', '🌟', '💰', '🛍️', '🎯', '📚', '🏆',
  ];

  @override
  void initState() {
    super.initState();
    _narrativeController.text = widget.transaction.aiNarrativeTitle ?? '';
    selectedMood = widget.transaction.moodEmoji;
  }

  @override
  void dispose() {
    _narrativeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 拖拽指示器
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: colors.border,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 标题
          Text(
            '为这个足迹添加故事',
            style: theme.typography.xl.copyWith(
              color: colors.foreground,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '让这个记录更有温度',
            style: theme.typography.base.copyWith(
              color: colors.mutedForeground,
            ),
          ),
          const SizedBox(height: 24),

          // 编辑叙事标题
          Text(
            '故事标题',
            style: theme.typography.base.copyWith(
              color: colors.foreground,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _narrativeController,
            decoration: const InputDecoration(
              hintText: '为这个记录写一个有趣的标题...',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 24),

          // 选择心情
          Text(
            '心情表情',
            style: theme.typography.base.copyWith(
              color: colors.foreground,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 120,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: colors.border),
              borderRadius: BorderRadius.circular(8),
            ),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
              ),
              itemCount: _commonMoods.length,
              itemBuilder: (context, index) {
                final mood = _commonMoods[index];
                final isSelected = selectedMood == mood;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedMood = isSelected ? null : mood;
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? colors.primary.withValues(alpha: 0.1)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected 
                            ? colors.primary
                            : Colors.transparent,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        mood,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 24),

          // 添加照片按钮
          FButton(
            style: FButtonStyle.outline(),
            onPress: _addPhoto,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.camera_alt,
                  size: 20,
                  color: colors.foreground,
                ),
                const SizedBox(width: 8),
                Text(
                  '添加照片',
                  style: theme.typography.base.copyWith(
                    color: colors.foreground,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: FButton(
                  style: FButtonStyle.outline(),
                  onPress: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: FButton(
                  onPress: _saveStoryContent,
                  child: const Text('保存'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// 添加照片
  void _addPhoto() {
    // TODO: 实现照片选择功能
    // 可以使用 image_picker 包
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加照片'),
        content: const Text('照片功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 保存故事内容
  void _saveStoryContent() {
    final customNarrative = _narrativeController.text.trim();
    
    // 更新交易的故事内容
    ref.read(footprintProvider.notifier).updateTransactionStory(
      transactionId: widget.transaction.id,
      moodEmoji: selectedMood,
      customNarrative: customNarrative.isNotEmpty ? customNarrative : null,
    );

    Navigator.of(context).pop();

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('故事内容已保存'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// 显示添加故事内容弹窗的辅助函数
void showAddStoryContentSheet(BuildContext context, TransactionModel transaction) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => AddStoryContentSheet(transaction: transaction),
  );
}
