// features/footprint/widgets/timeline_story_tile.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:intl/intl.dart';
import '../../home/<USER>/transaction_model.dart';

/// 时间轴故事瓦片组件 - 使用 timelines_plus 库
class TimelineStoryTile extends StatelessWidget {
  final TransactionModel transaction;
  final bool isFirst;
  final bool isLast;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onAddContent;

  const TimelineStoryTile({
    super.key,
    required this.transaction,
    this.isFirst = false,
    this.isLast = false,
    this.onTap,
    this.onLongPress,
    this.onAddContent,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return TimelineTile(
      oppositeContents: Container(
        width: 60,
        alignment: Alignment.center,
        child: Text(
          DateFormat('HH:mm').format(transaction.timestamp),
          style: theme.typography.sm.copyWith(
            color: colors.mutedForeground,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      contents: _buildContent(context, theme, colors),
      node: TimelineNode(
        indicator: _buildIndicator(colors),
        startConnector: isFirst ? null : SolidLineConnector(
          color: colors.border,
          thickness: 2,
        ),
        endConnector: isLast ? null : SolidLineConnector(
          color: colors.border,
          thickness: 2,
        ),
      ),
    );
  }

  /// 构建时间轴指示器 - 增强版
  Widget _buildIndicator(FColorScheme colors) {
    final indicatorColor = _getIndicatorColor(colors);

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            indicatorColor,
            indicatorColor.withValues(alpha: 0.8),
          ],
        ),
        border: Border.all(
          color: colors.background,
          width: 4,
        ),
        boxShadow: [
          BoxShadow(
            color: indicatorColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: colors.foreground.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景脉冲效果
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.2),
            ),
          ),
          // 主图标
          Icon(
            _getCategoryIcon(),
            size: 22,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  /// 获取指示器颜色
  Color _getIndicatorColor(FColors colors) {
    switch (transaction.category.toLowerCase()) {
      case '餐饮':
      case 'food':
        return const Color(0xFFFF6B6B); // 温暖的红色
      case '交通':
      case 'transport':
        return const Color(0xFF4ECDC4); // 青色
      case '购物':
      case 'shopping':
        return const Color(0xFFFFE66D); // 金黄色
      case '娱乐':
      case 'entertainment':
        return const Color(0xFFFF8E53); // 橙色
      case '工资':
      case 'salary':
        return const Color(0xFF95E1D3); // 薄荷绿
      case '医疗':
      case 'medical':
        return const Color(0xFFF38BA8); // 粉色
      case '教育':
      case 'education':
        return const Color(0xFF6C5CE7); // 紫色
      default:
        return colors.primary;
    }
  }

  /// 构建内容区域 - 升级版故事卡片
  Widget _buildContent(BuildContext context, ShadThemeData theme, FColors colors) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: InkWell(
        onTap: onTap,
        onLongPress: () {
          HapticFeedback.mediumImpact();
          onLongPress?.call();
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colors.primary,
                colors.primary.withValues(alpha: 0.8),
              ],
            ),
            border: Border.all(
              color: _getIndicatorColor(colors).withValues(alpha: 0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: _getIndicatorColor(colors).withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: colors.foreground.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // 背景装饰图案
                Positioned(
                  top: -20,
                  right: -20,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getIndicatorColor(colors).withValues(alpha: 0.05),
                    ),
                  ),
                ),
                // 主要内容
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: transaction.hasStoryContent
                      ? _buildPhotoStoryContent(theme, colors)
                      : _buildRegularStoryContent(theme, colors),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建带照片的故事内容
  Widget _buildPhotoStoryContent(ShadThemeData theme, FColors colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 照片区域
        if (transaction.photoPath != null)
          Container(
            width: double.infinity,
            height: 160,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: AssetImage(transaction.photoPath!),
                fit: BoxFit.cover,
              ),
            ),
          ),
        
        _buildStoryDetails(theme, colors),
      ],
    );
  }

  /// 构建常规故事内容
  Widget _buildRegularStoryContent(ShadThemeData theme, Fcolors colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStoryDetails(theme, colors),
        
        // 添加内容按钮
        const SizedBox(height: 12),
        _buildAddContentButton(theme, colors),
      ],
    );
  }

  /// 构建故事详情
  Widget _buildStoryDetails(ShadThemeData theme, FColors colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 金额行
        Row(
          children: [
            Expanded(
              child: Text(
                transaction.aiNarrativeTitle ?? _generateDefaultNarrative(),
                style: theme.textTheme.p.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colors.foreground,
                  height: 1.4,
                ),
              ),
            ),
            Text(
              '${transaction.type == TransactionType.expense ? '-' : '+'}¥${transaction.amount.toStringAsFixed(2)}',
              style: theme.textTheme.h4.copyWith(
                color: transaction.type == TransactionType.expense
                    ? colors.destructive
                    : Colors.green,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        
        // 商家和地点信息
        if (transaction.merchantName != null || transaction.geoLocation != null)
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 14,
                color: colors.mutedForeground,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  [transaction.merchantName, transaction.geoLocation]
                      .where((e) => e != null)
                      .join(' · '),
                  style: theme.textTheme.small.copyWith(
                    color: colors.mutedForeground,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        
        // 参与人员和心情
        if (transaction.participants.isNotEmpty || transaction.moodEmoji != null) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              // 参与人员标签
              if (transaction.participants.isNotEmpty)
                Expanded(
                  child: Wrap(
                    spacing: 6,
                    runSpacing: 4,
                    children: transaction.participants.map((participant) => 
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: colors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colors.primary.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          participant,
                          style: theme.textTheme.small.copyWith(
                            color: colors.primary,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ).toList(),
                  ),
                ),
              
              // 心情表情
              if (transaction.moodEmoji != null) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colors.secondary.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    transaction.moodEmoji!,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  /// 构建添加内容按钮
  Widget _buildAddContentButton(FThemeData theme, FColorScheme colors) {
    return FButton(
      style: FButtonStyle.ghost(),
      onPress: onAddContent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.add,
            size: 14,
            color: colors.mutedForeground,
          ),
          const SizedBox(width: 4),
          Text(
            '添加照片或心情',
            style: theme.typography.sm.copyWith(
              color: colors.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  /// 根据类别获取图标
  IconData _getCategoryIcon() {
    switch (transaction.category.toLowerCase()) {
      case '餐饮':
      case 'food':
        return Icons.restaurant;
      case '交通':
      case 'transport':
        return Icons.directions_car;
      case '购物':
      case 'shopping':
        return Icons.shopping_bag;
      case '娱乐':
      case 'entertainment':
        return Icons.games;
      case '医疗':
      case 'medical':
        return Icons.favorite;
      case '教育':
      case 'education':
        return Icons.school;
      case '旅行':
      case 'travel':
        return Icons.flight;
      case '工资':
      case 'salary':
        return Icons.attach_money;
      default:
        return Icons.account_balance_wallet;
    }
  }

  /// 生成默认叙事标题 - 增强版AI叙事
  String _generateDefaultNarrative() {
    final category = transaction.category;
    final amount = transaction.amount;
    final participants = transaction.participants;
    final hour = transaction.timestamp.hour;
    final isWeekend = transaction.timestamp.weekday >= 6;
    final merchantName = transaction.merchantName;

    // 基于参与者的叙事
    if (participants.isNotEmpty) {
      final participantText = participants.length == 1
          ? participants.first
          : '${participants.take(participants.length - 1).join('、')}和${participants.last}';

      switch (category.toLowerCase()) {
        case '餐饮':
          if (hour < 10) return '与$participantText的温馨早餐时光';
          if (hour < 14) return '与$participantText共享午后美食';
          if (hour < 18) return '与$participantText的下午茶约会';
          return '与$participantText的晚餐聚会';
        case '娱乐':
          return isWeekend ? '与$participantText的周末狂欢' : '与$participantText的快乐时光';
        case '交通':
          return '与$participantText的同行之旅';
        default:
          return '与$participantText一起的$category时光';
      }
    }

    // 基于时间和场景的叙事
    switch (category.toLowerCase()) {
      case '餐饮':
        if (merchantName != null) {
          if (hour < 10) return '在$merchantName开启美好的一天';
          if (hour < 14) return '在$merchantName的午餐时光';
          if (hour < 18) return '在$merchantName的悠闲下午';
          return '在$merchantName的晚餐时刻';
        }
        if (amount > 200) return '享受了一顿精致的美食盛宴';
        if (amount > 100) return '品味了一份温暖的美食';
        return '简单而美味的一餐';

      case '交通':
        if (amount > 100) return isWeekend ? '开启了一段周末旅程' : '踏上了远行的路';
        return '日常通勤的小确幸';

      case '购物':
        if (amount > 500) return '进行了一次愉快的购物之旅';
        if (amount > 200) return '发现了心仪的好物';
        return '买到了生活中的小惊喜';

      case '娱乐':
        if (isWeekend) return '周末的快乐时光';
        if (hour > 18) return '夜晚的放松时刻';
        return '忙碌中的小憩';

      case '工资':
        return '收获了努力工作的回报';

      case '医疗':
        return '为健康投资的明智选择';

      case '教育':
        return '为成长充电的投资';

      case '旅行':
        return amount > 1000 ? '开启了一段难忘的旅程' : '来了一场说走就走的小旅行';

      default:
        return '记录了生活中的一个${transaction.type == TransactionType.expense ? '支出' : '收入'}瞬间';
    }
  }
}
