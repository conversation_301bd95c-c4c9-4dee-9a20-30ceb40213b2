// features/footprint/widgets/simple_story_card.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../../home/<USER>/transaction_model.dart';
import '../theme/footprint_colors.dart';

/// 简洁的故事卡片组件
class SimpleStoryCard extends StatelessWidget {
  final TransactionModel transaction;
  final VoidCallback? onTap;
  final bool showDate;

  const SimpleStoryCard({
    super.key,
    required this.transaction,
    this.onTap,
    this.showDate = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: FCard.raw(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    // 类别图标
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: FootprintColors.getCategoryColor(transaction.category)
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        _getCategoryIcon(),
                        size: 20,
                        color: FootprintColors.getCategoryColor(transaction.category),
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // 标题和商家
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _generateStoryTitle(),
                            style: theme.typography.base.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colors.foreground,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (transaction.merchantName != null) ...[
                            const SizedBox(height: 2),
                            Text(
                              transaction.merchantName!,
                              style: theme.typography.sm.copyWith(
                                color: colors.mutedForeground,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // 金额
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${transaction.type == TransactionType.expense ? '-' : '+'}¥${transaction.amount.toStringAsFixed(0)}',
                          style: theme.typography.base.copyWith(
                            fontWeight: FontWeight.w700,
                            color: transaction.type == TransactionType.expense
                                ? colors.destructive
                                : Colors.green,
                          ),
                        ),
                        if (showDate) ...[
                          const SizedBox(height: 2),
                          Text(
                            _formatTime(),
                            style: theme.typography.sm.copyWith(
                              color: colors.mutedForeground,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
                
                // 参与者标签
                if (transaction.participants.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 6,
                    runSpacing: 4,
                    children: transaction.participants.take(3).map((participant) =>
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: FootprintColors.getSocialColor(transaction.participants.length)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: FootprintColors.getSocialColor(transaction.participants.length)
                                .withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          participant,
                          style: theme.typography.sm.copyWith(
                            color: FootprintColors.getSocialColor(transaction.participants.length),
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ).toList(),
                  ),
                ],
                
                // 描述
                if (transaction.description != null && transaction.description!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    transaction.description!,
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取类别图标
  IconData _getCategoryIcon() {
    switch (transaction.category.toLowerCase()) {
      case '餐饮':
      case 'food':
        return Icons.restaurant;
      case '交通':
      case 'transport':
        return Icons.directions_car;
      case '购物':
      case 'shopping':
        return Icons.shopping_bag;
      case '娱乐':
      case 'entertainment':
        return Icons.games;
      case '工资':
      case 'salary':
        return Icons.attach_money;
      case '医疗':
      case 'medical':
        return Icons.favorite;
      case '教育':
      case 'education':
        return Icons.school;
      case '旅行':
      case 'travel':
        return Icons.flight;
      case '住宿':
      case 'accommodation':
        return Icons.bed;
      default:
        return Icons.circle;
    }
  }

  /// 生成故事标题
  String _generateStoryTitle() {
    final hour = transaction.timestamp.hour;
    final isWeekend = transaction.timestamp.weekday >= 6;
    final participants = transaction.participants;
    
    // 基于参与者的叙事
    if (participants.isNotEmpty) {
      final participantText = participants.length == 1 
          ? participants.first 
          : '${participants.take(participants.length - 1).join('、')}和${participants.last}';
      
      switch (transaction.category.toLowerCase()) {
        case '餐饮':
        case 'food':
          if (hour < 10) return '与$participantText的早餐时光';
          if (hour < 14) return '与$participantText的午餐聚会';
          if (hour < 18) return '与$participantText的下午茶';
          return '与$participantText的晚餐时光';
        case '娱乐':
        case 'entertainment':
          return isWeekend ? '与$participantText的周末娱乐' : '与$participantText的休闲时光';
        default:
          return '与$participantText的${transaction.category}';
      }
    }
    
    // 基于时间和场景的叙事
    switch (transaction.category.toLowerCase()) {
      case '餐饮':
      case 'food':
        if (hour < 10) return '开启美好的一天';
        if (hour < 14) return '午餐时光';
        if (hour < 18) return '悠闲的下午茶';
        return '晚餐时刻';
        
      case '交通':
      case 'transport':
        return isWeekend ? '周末出行' : '日常通勤';
        
      case '购物':
      case 'shopping':
        if (transaction.amount > 500) return '购物收获满满';
        if (transaction.amount > 200) return '发现心仪好物';
        return '生活小采购';
        
      case '娱乐':
      case 'entertainment':
        return isWeekend ? '周末放松时光' : '忙里偷闲';
        
      case '工资':
      case 'salary':
        return '努力工作的回报';
        
      default:
        return transaction.category;
    }
  }

  /// 格式化时间
  String _formatTime() {
    final now = DateTime.now();
    final diff = now.difference(transaction.timestamp);
    
    if (diff.inDays == 0) {
      return '今天 ${transaction.timestamp.hour.toString().padLeft(2, '0')}:${transaction.timestamp.minute.toString().padLeft(2, '0')}';
    } else if (diff.inDays == 1) {
      return '昨天';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}天前';
    } else {
      return '${transaction.timestamp.month}月${transaction.timestamp.day}日';
    }
  }
}
