// features/footprint/theme/footprint_colors.dart
import 'package:flutter/material.dart';

/// 足迹模块专用颜色系统
class FootprintColors {
  // 主色调 - 深蓝紫渐变
  static const Color primaryBlue = Color(0xFF4F46E5);
  static const Color primaryPurple = Color(0xFF7C3AED);
  static const Color primaryViolet = Color(0xFF8B5CF6);
  
  // 类别颜色 - 情感化配色
  static const Map<String, Color> categoryColors = {
    '餐饮': Color(0xFFFF6B6B),      // 温暖的红色 - 食物的温暖
    'food': Color(0xFFFF6B6B),
    '交通': Color(0xFF4ECDC4),      // 青色 - 流动感
    'transport': Color(0xFF4ECDC4),
    '购物': Color(0xFFFFE66D),      // 金黄色 - 购物的愉悦
    'shopping': Color(0xFFFFE66D),
    '娱乐': Color(0xFFFF8E53),      // 橙色 - 活力与快乐
    'entertainment': Color(0xFFFF8E53),
    '工资': Color(0xFF95E1D3),      // 薄荷绿 - 收入的清新
    'salary': Color(0xFF95E1D3),
    '医疗': Color(0xFFF38BA8),      // 粉色 - 关爱与健康
    'medical': Color(0xFFF38BA8),
    '教育': Color(0xFF6C5CE7),      // 紫色 - 智慧与成长
    'education': Color(0xFF6C5CE7),
    '旅行': Color(0xFF74B9FF),      // 天蓝色 - 自由与探索
    'travel': Color(0xFF74B9FF),
    '住宿': Color(0xFFA29BFE),      // 淡紫色 - 舒适与休息
    'accommodation': Color(0xFFA29BFE),
  };
  
  // 情感色彩
  static const Map<String, Color> emotionColors = {
    'happy': Color(0xFFFFD93D),     // 明黄色 - 快乐
    'excited': Color(0xFFFF6B35),   // 橙红色 - 兴奋
    'calm': Color(0xFF6BCF7F),      // 绿色 - 平静
    'romantic': Color(0xFFFF8A95),  // 粉色 - 浪漫
    'nostalgic': Color(0xFFB19CD9), // 淡紫色 - 怀念
    'grateful': Color(0xFF95E1D3),  // 薄荷绿 - 感恩
    'surprised': Color(0xFFFFB347), // 橙色 - 惊喜
    'content': Color(0xFF87CEEB),   // 天蓝色 - 满足
  };
  
  // 时间段颜色
  static const Map<String, Color> timeColors = {
    'morning': Color(0xFFFFE5B4),   // 晨光黄
    'afternoon': Color(0xFFFFB347), // 午后橙
    'evening': Color(0xFF9B59B6),   // 黄昏紫
    'night': Color(0xFF2C3E50),     // 夜色蓝
  };
  
  // 季节颜色
  static const Map<String, Color> seasonColors = {
    'spring': Color(0xFF98FB98),    // 春绿
    'summer': Color(0xFFFFD700),    // 夏金
    'autumn': Color(0xFFFF8C00),    // 秋橙
    'winter': Color(0xFF87CEEB),    // 冬蓝
  };
  
  // 故事章节类型颜色
  static const Map<String, Color> chapterTypeColors = {
    'travel': Color(0xFF74B9FF),    // 旅行 - 天蓝色
    'project': Color(0xFF6C5CE7),   // 项目 - 紫色
    'social': Color(0xFFFF8E53),    // 社交 - 橙色
    'habit': Color(0xFF95E1D3),     // 习惯 - 薄荷绿
    'special': Color(0xFFFF6B6B),   // 特殊 - 红色
  };
  
  /// 根据类别获取颜色
  static Color getCategoryColor(String category) {
    return categoryColors[category.toLowerCase()] ?? primaryBlue;
  }
  
  /// 根据情感获取颜色
  static Color getEmotionColor(String emotion) {
    return emotionColors[emotion.toLowerCase()] ?? Colors.grey;
  }
  
  /// 根据时间获取颜色
  static Color getTimeColor(DateTime dateTime) {
    final hour = dateTime.hour;
    if (hour >= 6 && hour < 12) return timeColors['morning']!;
    if (hour >= 12 && hour < 18) return timeColors['afternoon']!;
    if (hour >= 18 && hour < 22) return timeColors['evening']!;
    return timeColors['night']!;
  }
  
  /// 根据季节获取颜色
  static Color getSeasonColor(DateTime dateTime) {
    final month = dateTime.month;
    if (month >= 3 && month <= 5) return seasonColors['spring']!;
    if (month >= 6 && month <= 8) return seasonColors['summer']!;
    if (month >= 9 && month <= 11) return seasonColors['autumn']!;
    return seasonColors['winter']!;
  }
  
  /// 根据章节类型获取颜色
  static Color getChapterTypeColor(String type) {
    return chapterTypeColors[type.toLowerCase()] ?? primaryBlue;
  }
  
  /// 生成渐变色
  static LinearGradient createGradient(Color startColor, {Color? endColor}) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        startColor,
        endColor ?? startColor.withValues(alpha: 0.7),
      ],
    );
  }
  
  /// 创建主题渐变
  static LinearGradient get primaryGradient => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryPurple, primaryViolet],
  );
  
  /// 创建卡片阴影
  static List<BoxShadow> createCardShadow(Color color, {double opacity = 0.1}) {
    return [
      BoxShadow(
        color: color.withValues(alpha: opacity),
        blurRadius: 12,
        offset: const Offset(0, 4),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.05),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }
  
  /// 创建发光效果
  static List<BoxShadow> createGlowEffect(Color color, {double intensity = 0.3}) {
    return [
      BoxShadow(
        color: color.withValues(alpha: intensity),
        blurRadius: 20,
        offset: const Offset(0, 0),
        spreadRadius: 5,
      ),
    ];
  }
  
  /// 根据金额大小获取颜色强度
  static Color getAmountIntensityColor(double amount, Color baseColor) {
    // 根据金额大小调整颜色强度
    if (amount < 50) return baseColor.withValues(alpha: 0.6);
    if (amount < 200) return baseColor.withValues(alpha: 0.8);
    if (amount < 500) return baseColor;
    return Color.lerp(baseColor, Colors.white, 0.2) ?? baseColor;
  }
  
  /// 获取对比色（用于文字）
  static Color getContrastColor(Color backgroundColor) {
    // 计算亮度
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
  
  /// 创建脉冲动画颜色序列
  static List<Color> createPulseColors(Color baseColor) {
    return [
      baseColor.withValues(alpha: 0.3),
      baseColor.withValues(alpha: 0.6),
      baseColor,
      baseColor.withValues(alpha: 0.6),
      baseColor.withValues(alpha: 0.3),
    ];
  }
  
  /// 根据参与者数量获取社交颜色
  static Color getSocialColor(int participantCount) {
    if (participantCount == 1) return const Color(0xFF95E1D3); // 薄荷绿 - 独处
    if (participantCount == 2) return const Color(0xFFFF8A95); // 粉色 - 二人世界
    if (participantCount <= 5) return const Color(0xFFFF8E53); // 橙色 - 小聚
    return const Color(0xFF74B9FF); // 蓝色 - 大聚会
  }
  
  /// 根据消费频率获取习惯颜色
  static Color getHabitColor(int frequency) {
    if (frequency < 3) return Colors.grey.withValues(alpha: 0.6);
    if (frequency < 10) return const Color(0xFF95E1D3);
    if (frequency < 20) return const Color(0xFF74B9FF);
    return const Color(0xFF6C5CE7);
  }
}

/// 足迹主题扩展
extension FootprintThemeExtension on ThemeData {
  /// 获取足迹主题颜色
  FootprintThemeColors get footprintColors => FootprintThemeColors(this);
}

/// 足迹主题颜色类
class FootprintThemeColors {
  final ThemeData theme;
  
  FootprintThemeColors(this.theme);
  
  /// 主要颜色
  Color get primary => theme.brightness == Brightness.dark 
      ? FootprintColors.primaryViolet 
      : FootprintColors.primaryBlue;
  
  /// 背景颜色
  Color get background => theme.brightness == Brightness.dark 
      ? const Color(0xFF1A1A1A) 
      : Colors.white;
  
  /// 卡片颜色
  Color get card => theme.brightness == Brightness.dark 
      ? const Color(0xFF2A2A2A) 
      : Colors.white;
  
  /// 边框颜色
  Color get border => theme.brightness == Brightness.dark 
      ? Colors.white.withValues(alpha: 0.1)
      : Colors.black.withValues(alpha: 0.1);
  
  /// 文字颜色
  Color get text => theme.brightness == Brightness.dark 
      ? Colors.white 
      : Colors.black87;
  
  /// 次要文字颜色
  Color get textSecondary => theme.brightness == Brightness.dark 
      ? Colors.white.withValues(alpha: 0.7)
      : Colors.black54;
}
