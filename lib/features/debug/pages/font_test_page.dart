import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../../../app/theme/china_font_config.dart';
import '/shared/l10n/app_strings.dart';

class FontTestPage extends StatelessWidget {
  const FontTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.get('fontTestPage')),
        backgroundColor: theme.colors.background,
        foregroundColor: theme.colors.foreground,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题部分
            Text(
              AppStrings.get('fontDisplayTest'),
              style: theme.typography.xl,
            ),
            const SizedBox(height: 16),

            // 中文文本测试
            _buildFontSection(
              context,
              title: AppStrings.get('chineseTextTest'),
              samples: [
                AppStrings.get('fontTestSample1'),
                AppStrings.get('fontTestSample2'),
                AppStrings.get('fontTestSample3'),
                AppStrings.get('fontTestSample4'),
                AppStrings.get('fontTestSample5'),
              ],
            ),

            const SizedBox(height: 24),

            // 英文文本测试
            _buildFontSection(
              context,
              title: AppStrings.get('englishTextTest'),
              samples: [
                'This is English text for font testing.',
                'AI Expense Tracker Application',
                'Data Visualization and Analytics',
                'User Interface Components',
                'Flutter & Dart Programming',
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 混合文本测试
            _buildFontSection(
              context,
              title: '中英文混合测试',
              samples: [
                'AI智能助手 - Artificial Intelligence Assistant',
                '支出 Expense: ¥1,234.56',
                'Flutter应用开发 Development',
                'UI/UX设计 Design & Experience',
                'API接口 Interface & Backend',
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 数字和符号测试
            _buildFontSection(
              context,
              title: '数字和符号测试',
              samples: [
                '金额：¥12,345.67',
                '日期：2024年12月30日',
                '时间：14:30:25',
                '百分比：85.5%',
                '特殊符号：@#\$%^&*()_+-=[]{}|;:,.<>?',
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 不同字体大小测试
            _buildFontSizeSection(context),
            
            const SizedBox(height: 24),
            
            // 系统字体信息
            _buildSystemFontInfo(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSection(BuildContext context, {
    required String title,
    required List<String> samples,
  }) {
    final theme =context.theme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.typography.xl,
        ),
        const SizedBox(height: 8),
        ...samples.map((sample) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Text(
            sample,
            style: theme.typography.base,
          ),
        )),
      ],
    );
  }

  Widget _buildFontSizeSection(BuildContext context) {
    final theme = context.theme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '字体大小测试',
          style: theme.typography.xl,
        ),
        const SizedBox(height: 8),
        Text('超大标题 - 这是H1标题样式', style: theme.typography.xl),
        const SizedBox(height: 4),
        Text('大标题 - 这是H2标题样式', style: theme.typography.xl2),
        const SizedBox(height: 4),
        Text('中标题 - 这是H3标题样式', style: theme.typography.xl3),
        const SizedBox(height: 4),
        Text('小标题 - 这是H4标题样式', style: theme.typography.xl4),
        const SizedBox(height: 4),
        Text('正文大 - 这是Large正文样式', style: theme.typography.lg),
        const SizedBox(height: 4),
        Text('正文 - 这是普通正文样式', style: theme.typography.base),
        const SizedBox(height: 4),
        Text('正文小 - 这是Small正文样式', style: theme.typography.sm),
        const SizedBox(height: 4),
        Text('微小文字 - 这是Muted样式', style: theme.typography.xs),
      ],
    );
  }

  Widget _buildSystemFontInfo(BuildContext context) {
    final theme = context.theme;
    final fontInfo = ChinaFontConfig.getFontInfo();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '字体配置信息',
          style: theme.typography.xl3,
        ),
        const SizedBox(height: 8),
        Text(
          '主要字体：${fontInfo['primaryFont']}',
          style: theme.typography.base,
        ),
        const SizedBox(height: 8),
        Text(
          '字体来源：${fontInfo['description']}',
          style: theme.typography.base,
        ),
        const SizedBox(height: 8),
        Text(
          '字体特点：专为中国大陆用户优化，无需网络下载',
          style: theme.typography.sm,
        ),
        const SizedBox(height: 8),
        Text(
          '字体回退列表：',
          style: theme.typography.base,
        ),
        ...(fontInfo['fallbackFonts'] as List<String>).map((font) => Padding(
          padding: const EdgeInsets.only(left: 16.0, top: 2.0),
          child: Text(
            '• $font',
            style: theme.typography.sm,
          ),
        )),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colors.muted,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '💡 提示',
                style: theme.typography.base.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '如果字体显示不正常，请确保已下载字体文件到 assets/fonts/ 目录',
                style: theme.typography.sm,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
