// features/forecast/providers/forecast_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/forecast_models.dart';
import '../services/forecast_service.dart';

/// 预测天数选项
enum ForecastDaysOption {
  days60(60, '60天'),
  days180(180, '180天'),
  days365(365, '365天');

  const ForecastDaysOption(this.days, this.label);
  final int days;
  final String label;
}

/// 当前选择的预测天数Provider
final selectedForecastDaysProvider = StateProvider<ForecastDaysOption>((ref) => ForecastDaysOption.days60);

/// What-If场景Provider
final whatIfScenariosProvider = StateProvider<List<WhatIfScenario>>((ref) => []);

/// 财务预测Provider
final forecastProvider = FutureProvider<FinancialForecast>((ref) async {
  final forecastService = ref.watch(forecastServiceProvider);
  final selectedDays = ref.watch(selectedForecastDaysProvider);
  final scenarios = ref.watch(whatIfScenariosProvider);

  // 🔥 根据天数确定granularity
  String granularity;
  switch (selectedDays.days) {
    case 60:
      granularity = 'daily';
      break;
    case 180:
      granularity = 'weekly';
      break;
    case 365:
      granularity = 'monthly';
      break;
    default:
      granularity = 'daily';
  }

  final request = ForecastRequest(
    forecastDays: selectedDays.days,
    scenarios: scenarios,
    granularity: granularity, // 🔥 传入granularity参数
  );

  print('Forecast API调用: 天数=${selectedDays.days}, 粒度=$granularity');
  return await forecastService.getForecast(request);
});
