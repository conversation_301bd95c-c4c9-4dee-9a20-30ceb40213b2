// features/forecast/providers/onboarding_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:decimal/decimal.dart';
import '../models/forecast_models.dart';
import '../services/forecast_service.dart';

/// 引导流程状态管理
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
  return OnboardingNotifier(ref);
});

class OnboardingNotifier extends StateNotifier<OnboardingState> {
  final Ref _ref;

  OnboardingNotifier(this._ref) : super(const OnboardingState());

  /// 检查引导状态
  Future<bool> checkOnboardingStatus() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final forecastService = _ref.read(forecastServiceProvider);
      final response = await forecastService.getOnboardingStatus();

      state = state.copyWith(
        isCompleted: response.isCompleted,
        isLoading: false,
      );

      return response.isCompleted;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );

      return false;
    }
  }

  /// 前往下一步
  void nextStep() {
    switch (state.currentStep) {
      case OnboardingStep.welcome:
        state = state.copyWith(
          currentStep: OnboardingStep.currentBalance,
          currentPageIndex: 1,
        );
        break;
      case OnboardingStep.currentBalance:
        if (state.currentBalance != null && state.currentBalance! > Decimal.zero) {
          state = state.copyWith(
            currentStep: OnboardingStep.recurringEvents,
            currentPageIndex: 2,
          );
        }
        break;
      case OnboardingStep.recurringEvents:
        if (_hasValidRecurringEvents()) {
          state = state.copyWith(
            currentStep: OnboardingStep.dailySpending,
            currentPageIndex: 3,
          );
        }
        break;
      case OnboardingStep.dailySpending:
        state = state.copyWith(
          currentStep: OnboardingStep.safetyThreshold,
          currentPageIndex: 4,
        );
        break;
      case OnboardingStep.safetyThreshold:
        state = state.copyWith(
          currentStep: OnboardingStep.generating,
          currentPageIndex: 5,
        );
        _generateForecast();
        break;
      case OnboardingStep.generating:
        // 完成引导，由外部处理导航
        break;
    }
  }

  /// 返回上一步
  void previousStep() {
    switch (state.currentStep) {
      case OnboardingStep.currentBalance:
        state = state.copyWith(
          currentStep: OnboardingStep.welcome,
          currentPageIndex: 0,
        );
        break;
      case OnboardingStep.recurringEvents:
        state = state.copyWith(
          currentStep: OnboardingStep.currentBalance,
          currentPageIndex: 1,
        );
        break;
      case OnboardingStep.dailySpending:
        state = state.copyWith(
          currentStep: OnboardingStep.recurringEvents,
          currentPageIndex: 2,
        );
        break;
      case OnboardingStep.safetyThreshold:
        state = state.copyWith(
          currentStep: OnboardingStep.dailySpending,
          currentPageIndex: 3,
        );
        break;
      case OnboardingStep.generating:
        state = state.copyWith(
          currentStep: OnboardingStep.safetyThreshold,
          currentPageIndex: 4,
        );
        break;
      case OnboardingStep.welcome:
        // 已经是第一步
        break;
    }
  }

  /// 设置当前余额（Step 2）
  Future<bool> setCurrentBalance(Decimal balance) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final forecastService = _ref.read(forecastServiceProvider);
      await forecastService.createInitialCashSource(balance.toDouble());

      state = state.copyWith(
        currentBalance: balance,
        isLoading: false,
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );

      return false;
    }
  }

  /// 添加周期性交易（Step 3）
  Future<bool> addRecurringTransaction({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final forecastService = _ref.read(forecastServiceProvider);

      final response = await forecastService.createRecurringTransaction(
        amount: amount,
        categoryId: categoryId,
        description: description,
        recurrenceRule: recurrenceRule,
        startDate: startDate,
        endDate: endDate,
      );

      // 转换为RecurringEvent并添加到状态
      final recurringEvent = RecurringEvent(
        id: response.id,
        type: amount > 0 ? RecurringEventType.income : RecurringEventType.expense,
        description: response.description ?? '',
        amount: response.amount,
        frequency: RecurringFrequency.monthly, // 默认月度
        startDate: DateTime.parse(response.startDate),
        endDate: response.endDate != null ? DateTime.parse(response.endDate!) : null,
        recurrenceRule: response.recurrenceRule,
      );

      final updatedEvents = [...state.recurringEvents, recurringEvent];
      state = state.copyWith(
        recurringEvents: updatedEvents,
        isLoading: false,
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );

      return false;
    }
  }

  

  /// 移除周期性事件
  void removeRecurringEvent(int index) {
    if (index >= 0 && index < state.recurringEvents.length) {
      final updatedEvents = [...state.recurringEvents];
      updatedEvents.removeAt(index);
      state = state.copyWith(recurringEvents: updatedEvents);
    }
  }

  /// 设置预估日常支出
  void setEstimatedDailySpending(double amount) {
    state = state.copyWith(estimatedDailySpending: amount);
  }

  /// 设置安心线
  void setSafetyThreshold(double threshold) {
    state = state.copyWith(safetyThreshold: threshold);
  }

  /// 完成引导流程（Step 4 & 5）
  Future<bool> completeOnboarding() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final forecastService = _ref.read(forecastServiceProvider);

      await forecastService.saveUserSettings(
        estimatedAvgDailySpending: state.estimatedDailySpending,
        safetyBalanceThreshold: state.safetyThreshold,
      );

      state = state.copyWith(
        isCompleted: true,
        isLoading: false,
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );

      return false;
    }
  }

  /// 生成财务预测
  Future<void> _generateForecast() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final forecastService = _ref.read(forecastServiceProvider);
      
      final request = ForecastRequest(
        forecastDays: 60,
        scenarios: [],
      );

      await forecastService.getForecast(request);
      
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 检查是否有有效的周期性事件
  bool _hasValidRecurringEvents() {
    final hasIncome = state.recurringEvents.any((e) => e.type == RecurringEventType.income);
    final hasExpense = state.recurringEvents.any((e) => e.type == RecurringEventType.expense);
    return hasIncome && hasExpense;
  }

  /// 检查当前步骤是否可以继续
  bool canProceed() {
    switch (state.currentStep) {
      case OnboardingStep.welcome:
        return true;
      case OnboardingStep.currentBalance:
        return state.currentBalance != null && state.currentBalance! > Decimal.zero;
      case OnboardingStep.recurringEvents:
        return _hasValidRecurringEvents();
      case OnboardingStep.dailySpending:
        return true;
      case OnboardingStep.safetyThreshold:
        return true;
      case OnboardingStep.generating:
        return !state.isLoading;
    }
  }

  /// 重置状态
  void reset() {
    state = const OnboardingState();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}
