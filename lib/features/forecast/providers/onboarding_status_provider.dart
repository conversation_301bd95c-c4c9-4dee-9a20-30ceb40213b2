// features/forecast/providers/onboarding_status_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/forecast_service.dart';
import '../../auth/providers/auth_provider.dart';

/// 引导状态枚举
enum OnboardingStatus {
  unknown,    // 未知状态，需要检查
  completed,  // 已完成
  incomplete, // 未完成
}

/// 引导状态管理Provider
class OnboardingStatusNotifier extends StateNotifier<OnboardingStatus> {
  final Ref _ref;
  static const String _keyPrefix = 'onboarding_status_for_user_';

  OnboardingStatusNotifier(this._ref) : super(OnboardingStatus.unknown);

  /// 获取当前用户的本地缓存键
  String _getCacheKey() {
    final authState = _ref.read(authProvider);
    final userId = authState.user?.id ?? 'unknown';
    return '$_keyPrefix$userId';
  }

  /// 从本地缓存读取引导状态
  Future<OnboardingStatus> _getLocalStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey();
      final statusString = prefs.getString(cacheKey);
      
      switch (statusString) {
        case 'completed':
          return OnboardingStatus.completed;
        case 'incomplete':
          return OnboardingStatus.incomplete;
        default:
          return OnboardingStatus.unknown;
      }
    } catch (e) {
      print('读取本地引导状态失败: $e');
      return OnboardingStatus.unknown;
    }
  }

  /// 保存引导状态到本地缓存
  Future<void> _saveLocalStatus(OnboardingStatus status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey();
      
      String statusString;
      switch (status) {
        case OnboardingStatus.completed:
          statusString = 'completed';
          break;
        case OnboardingStatus.incomplete:
          statusString = 'incomplete';
          break;
        case OnboardingStatus.unknown:
          // 不保存unknown状态
          return;
      }
      
      await prefs.setString(cacheKey, statusString);
      print('本地引导状态已保存: $statusString');
    } catch (e) {
      print('保存本地引导状态失败: $e');
    }
  }

  /// 智能检查引导状态
  /// 这个方法只在关键生命周期节点被调用
  Future<OnboardingStatus> checkOnboardingStatus() async {
    print('开始智能检查引导状态...');
    
    // 第一步：检查本地缓存
    final localStatus = await _getLocalStatus();
    print('本地缓存状态: $localStatus');
    
    switch (localStatus) {
      case OnboardingStatus.completed:
        // Case A: 已完成 - 直接信任本地缓存
        print('本地缓存显示已完成，直接使用');
        state = OnboardingStatus.completed;
        return OnboardingStatus.completed;
        
      case OnboardingStatus.incomplete:
        // Case B: 未完成 - 直接信任本地缓存
        print('本地缓存显示未完成，直接使用');
        state = OnboardingStatus.incomplete;
        return OnboardingStatus.incomplete;
        
      case OnboardingStatus.unknown:
        // Case C: 未知状态 - 需要发起网络请求
        print('本地缓存未知，发起网络请求检查');
        return await _checkFromServer();
    }
  }

  /// 从服务器检查引导状态
  Future<OnboardingStatus> _checkFromServer() async {
    try {
      final forecastService = _ref.read(forecastServiceProvider);
      final response = await forecastService.getOnboardingStatus();
      
      final serverStatus = response.isCompleted 
          ? OnboardingStatus.completed 
          : OnboardingStatus.incomplete;
      
      print('服务器返回状态: $serverStatus');
      
      // 更新本地缓存
      await _saveLocalStatus(serverStatus);
      
      // 更新内存状态
      state = serverStatus;
      
      return serverStatus;
    } catch (e) {
      print('从服务器检查引导状态失败: $e');
      // 网络错误时，假设需要引导
      state = OnboardingStatus.incomplete;
      return OnboardingStatus.incomplete;
    }
  }

  /// 标记引导流程已完成
  /// 在用户成功完成整个引导流程后调用
  Future<void> markAsCompleted() async {
    print('标记引导流程为已完成');
    
    // 更新本地缓存
    await _saveLocalStatus(OnboardingStatus.completed);
    
    // 更新内存状态
    state = OnboardingStatus.completed;
  }

  /// 重置引导状态（用于测试或重新引导）
  Future<void> reset() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey();
      await prefs.remove(cacheKey);
      state = OnboardingStatus.unknown;
      print('引导状态已重置');
    } catch (e) {
      print('重置引导状态失败: $e');
    }
  }

  /// 在用户登录成功后调用此方法
  Future<void> onUserLoggedIn() async {
    print('用户登录成功，检查引导状态');
    await checkOnboardingStatus();
  }

  /// 在用户注册成功后调用此方法
  Future<void> onUserRegistered() async {
    print('用户注册成功，设置为需要引导');
    // 新注册用户默认需要引导
    await _saveLocalStatus(OnboardingStatus.incomplete);
    state = OnboardingStatus.incomplete;
  }
}

/// Provider实例
final onboardingStatusProvider = StateNotifierProvider<OnboardingStatusNotifier, OnboardingStatus>((ref) {
  return OnboardingStatusNotifier(ref);
});
