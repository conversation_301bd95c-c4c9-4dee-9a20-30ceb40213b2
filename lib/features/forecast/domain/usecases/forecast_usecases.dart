// features/forecast/domain/usecases/forecast_usecases.dart
import 'package:decimal/decimal.dart';
import '../entities/forecast_entities.dart';
import '../repositories/forecast_repository.dart';

/// 获取预测用例
class GetForecastUseCase {
  final ForecastRepository _repository;

  GetForecastUseCase(this._repository);

  Future<ForecastData> execute(ForecastConfig config) async {
    // 验证配置
    if (!config.isValid) {
      throw const ForecastError(
        message: '预测配置不完整',
        type: ForecastErrorType.validation,
      );
    }

    // 尝试获取缓存数据
    final cachedData = _repository.getCachedForecast(config);
    if (cachedData != null && !cachedData.isExpired) {
      return cachedData;
    }

    try {
      // 获取新数据
      final data = await _repository.getForecast(config);

      // 缓存数据
      await _repository.cacheForecast(config, data);

      return data;
    } catch (e) {
      // 如果有缓存数据，即使过期也返回
      if (cachedData != null) {
        return cachedData;
      }

      // 否则抛出错误
      if (e is ForecastError) {
        rethrow;
      } else {
        throw ForecastError(
          message: '获取预测数据失败',
          type: ForecastErrorType.network,
          details: e.toString(),
        );
      }
    }
  }
}

/// 保存用户设置用例
class SaveUserSettingsUseCase {
  final ForecastRepository _repository;

  SaveUserSettingsUseCase(this._repository);

  Future<void> execute({
    required double estimatedDailySpending,
    required double safetyThreshold,
  }) async {
    // 验证输入
    if (estimatedDailySpending <= 0) {
      throw const ForecastError(
        message: '日常支出必须大于0',
        type: ForecastErrorType.validation,
      );
    }

    if (safetyThreshold <= 0) {
      throw const ForecastError(
        message: '安全线必须大于0',
        type: ForecastErrorType.validation,
      );
    }

    try {
      await _repository.saveUserSettings(
        estimatedDailySpending: estimatedDailySpending,
        safetyThreshold: safetyThreshold,
      );
    } catch (e) {
      throw ForecastError(
        message: '保存设置失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }
}

/// 管理周期性事件用例
class ManageRecurringEventsUseCase {
  final ForecastRepository _repository;

  ManageRecurringEventsUseCase(this._repository);

  Future<String> createEvent(RecurringEvent event) async {
    // 验证事件
    if (event.amount == Decimal.zero) {
      throw const ForecastError(
        message: '金额不能为0',
        type: ForecastErrorType.validation,
      );
    }

    if (event.description.isEmpty) {
      throw const ForecastError(
        message: '描述不能为空',
        type: ForecastErrorType.validation,
      );
    }

    try {
      return await _repository.createRecurringEvent(event);
    } catch (e) {
      throw ForecastError(
        message: '创建周期性事件失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }

  Future<void> deleteEvent(String eventId) async {
    if (eventId.isEmpty) {
      throw const ForecastError(
        message: '事件ID不能为空',
        type: ForecastErrorType.validation,
      );
    }

    try {
      await _repository.deleteRecurringEvent(eventId);
    } catch (e) {
      throw ForecastError(
        message: '删除周期性事件失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }
}

/// 引导流程用例
class OnboardingUseCase {
  final ForecastRepository _repository;

  OnboardingUseCase(this._repository);

  Future<bool> checkStatus() async {
    try {
      return await _repository.checkOnboardingStatus();
    } catch (e) {
      // 检查失败时默认认为未完成
      return false;
    }
  }

  Future<void> markCompleted() async {
    try {
      await _repository.markOnboardingCompleted();
    } catch (e) {
      throw ForecastError(
        message: '标记引导完成失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }
}

/// 离线预测用例
class OfflineForecastUseCase {
  /// 生成基础的离线预测
  ForecastData generateBasicForecast(ForecastConfig config) {
    if (!config.isValid) {
      throw const ForecastError(
        message: '配置不完整，无法生成离线预测',
        type: ForecastErrorType.validation,
      );
    }

    final startDate = DateTime.now();
    final dailyBreakdown = <DailyBreakdown>[];
    var currentBalance = config.currentBalance!;

    for (int i = 0; i < config.forecastDays; i++) {
      final date = startDate.add(Duration(days: i));
      final events = <DailyEvent>[];

      // 添加日常支出
      final dailyExpense = Decimal.parse(
        config.estimatedDailySpending.toString(),
      );
      events.add(
        DailyEvent(
          description: '预测日常支出',
          amount: -dailyExpense,
          type: DailyEventType.predicted,
        ),
      );
      currentBalance -= dailyExpense;

      // 添加周期性事件
      for (final recurringEvent in config.recurringEvents) {
        if (_shouldAddRecurringEvent(recurringEvent, date)) {
          events.add(
            DailyEvent(
              description: recurringEvent.description,
              amount: recurringEvent.amount,
              type: DailyEventType.recurring,
            ),
          );
          currentBalance += recurringEvent.amount;
        }
      }

      dailyBreakdown.add(
        DailyBreakdown(
          date: date,
          closingBalance: currentBalance,
          events: events,
        ),
      );
    }

    // 生成警告
    final warnings = _generateWarnings(dailyBreakdown, config.safetyThreshold);

    return ForecastData(
      dailyBreakdown: dailyBreakdown,
      warnings: warnings,
      summary: ForecastSummary(
        startBalance: config.currentBalance!,
        endBalance: currentBalance,
        totalIncome: _calculateTotalIncome(dailyBreakdown),
        totalExpense: _calculateTotalExpense(dailyBreakdown),
      ),
      lastUpdated: DateTime.now(),
    );
  }

  bool _shouldAddRecurringEvent(RecurringEvent event, DateTime date) {
    // 简化的周期性事件判断逻辑
    if (date.isBefore(event.startDate)) return false;
    if (event.endDate != null && date.isAfter(event.endDate!)) return false;

    switch (event.frequency) {
      case RecurringFrequency.monthly:
        return date.day == event.startDate.day;
      case RecurringFrequency.weekly:
        return date.weekday == event.startDate.weekday;
    }
  }

  List<ForecastWarning> _generateWarnings(
    List<DailyBreakdown> breakdown,
    double safetyThreshold,
  ) {
    final warnings = <ForecastWarning>[];
    final threshold = Decimal.parse(safetyThreshold.toString());

    for (final day in breakdown) {
      if (day.closingBalance < threshold) {
        warnings.add(
          ForecastWarning(
            date: day.date,
            message: '余额将低于安全线',
            level: day.closingBalance < Decimal.zero
                ? WarningLevel.critical
                : WarningLevel.warning,
          ),
        );
      }
    }

    return warnings;
  }

  Decimal _calculateTotalIncome(List<DailyBreakdown> breakdown) {
    return breakdown.fold(Decimal.zero, (total, day) {
      return total +
          day.events
              .where((e) => e.amount > Decimal.zero)
              .fold(Decimal.zero, (sum, event) => sum + event.amount);
    });
  }

  Decimal _calculateTotalExpense(List<DailyBreakdown> breakdown) {
    return breakdown.fold(Decimal.zero, (total, day) {
      return total +
          day.events
              .where((e) => e.amount < Decimal.zero)
              .fold(Decimal.zero, (sum, event) => sum + event.amount.abs());
    });
  }
}
