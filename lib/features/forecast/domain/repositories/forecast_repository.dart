// features/forecast/domain/repositories/forecast_repository.dart
import '../entities/forecast_entities.dart';

/// 预测数据仓库接口
abstract class ForecastRepository {
  /// 获取财务预测
  Future<ForecastData> getForecast(ForecastConfig config);

  /// 保存用户设置
  Future<void> saveUserSettings({
    required double estimatedDailySpending,
    required double safetyThreshold,
  });

  /// 创建周期性事件
  Future<String> createRecurringEvent(RecurringEvent event);

  /// 删除周期性事件
  Future<void> deleteRecurringEvent(String eventId);

  /// 检查引导状态
  Future<bool> checkOnboardingStatus();

  /// 标记引导完成
  Future<void> markOnboardingCompleted();

  /// 获取缓存的预测数据
  ForecastData? getCachedForecast(ForecastConfig config);

  /// 缓存预测数据
  Future<void> cacheForecast(ForecastConfig config, ForecastData data);

  /// 清除缓存
  Future<void> clearCache();
}
