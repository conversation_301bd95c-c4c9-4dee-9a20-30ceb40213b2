// features/forecast/domain/entities/forecast_entities.dart
import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'forecast_entities.freezed.dart';

/// 预测配置实体
@freezed
class ForecastConfig with _$ForecastConfig {
  const factory ForecastConfig({
    @Default(60) int forecastDays,
    @Default('daily') String granularity,
    @Default([]) List<WhatIfScenario> scenarios,
    Decimal? currentBalance,
    @Default(150.0) double estimatedDailySpending,
    @Default(500.0) double safetyThreshold,
    @Default([]) List<RecurringEvent> recurringEvents,
  }) = _ForecastConfig;

  const ForecastConfig._();

  /// 验证配置是否完整
  bool get isValid {
    return currentBalance != null && 
           currentBalance! > Decimal.zero &&
           estimatedDailySpending > 0 &&
           safetyThreshold > 0;
  }

  /// 获取预测天数选项
  ForecastDaysOption get daysOption {
    switch (forecastDays) {
      case 60:
        return ForecastDaysOption.days60;
      case 180:
        return ForecastDaysOption.days180;
      case 365:
        return ForecastDaysOption.days365;
      default:
        return ForecastDaysOption.days60;
    }
  }
}

/// 预测数据实体
@freezed
class ForecastData with _$ForecastData {
  const factory ForecastData({
    required List<DailyBreakdown> dailyBreakdown,
    @Default([]) List<ForecastWarning> warnings,
    ForecastSummary? summary,
    List<DailyBreakdown>? simulatedDailyBreakdown,
    DateTime? lastUpdated,
  }) = _ForecastData;

  const ForecastData._();

  /// 获取指定日期的数据
  DailyBreakdown? getBreakdownForDate(DateTime date) {
    try {
      return dailyBreakdown.firstWhere(
        (breakdown) => _isSameDay(breakdown.date, date),
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取今天的数据
  DailyBreakdown? get todayBreakdown {
    return getBreakdownForDate(DateTime.now());
  }

  /// 检查数据是否过期（超过1小时）
  bool get isExpired {
    if (lastUpdated == null) return true;
    return DateTime.now().difference(lastUpdated!).inHours > 1;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}

/// 预测错误实体
@freezed
class ForecastError with _$ForecastError {
  const factory ForecastError({
    required String message,
    required ForecastErrorType type,
    DateTime? timestamp,
    String? details,
  }) = _ForecastError;

  const ForecastError._();

  /// 是否为网络错误
  bool get isNetworkError => type == ForecastErrorType.network;

  /// 是否为验证错误
  bool get isValidationError => type == ForecastErrorType.validation;

  /// 获取用户友好的错误消息
  String get userFriendlyMessage {
    switch (type) {
      case ForecastErrorType.network:
        return '网络连接失败，请检查网络设置';
      case ForecastErrorType.validation:
        return '输入数据有误，请检查后重试';
      case ForecastErrorType.calculation:
        return '预测计算失败，请稍后重试';
      case ForecastErrorType.unknown:
        return '发生未知错误，请联系客服';
    }
  }
}

/// 错误类型枚举
enum ForecastErrorType {
  network,
  validation,
  calculation,
  unknown,
}

/// 预测天数选项
enum ForecastDaysOption {
  days60(60, '60天'),
  days180(180, '180天'),
  days365(365, '365天');

  const ForecastDaysOption(this.days, this.label);
  final int days;
  final String label;
}

// 重新导出原有模型，保持兼容性
export '../models/forecast_models.dart' show 
  DailyBreakdown,
  ForecastWarning,
  ForecastSummary,
  WhatIfScenario,
  RecurringEvent,
  RecurringEventType,
  RecurringFrequency,
  DailyEvent,
  DailyEventType,
  WarningLevel;