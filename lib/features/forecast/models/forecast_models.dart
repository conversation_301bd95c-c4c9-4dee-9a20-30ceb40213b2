// features/forecast/models/forecast_models.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:decimal/decimal.dart';


part 'forecast_models.freezed.dart';
part 'forecast_models.g.dart';

/// 引导状态检查响应
@freezed
abstract class OnboardingStatusResponse with _$OnboardingStatusResponse {
  const factory OnboardingStatusResponse({
    required bool isCompleted,
  }) = _OnboardingStatusResponse;

  factory OnboardingStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$OnboardingStatusResponseFromJson(json);
}

/// 引导流程状态
@freezed
abstract class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(OnboardingStep.welcome) OnboardingStep currentStep,
    @Default(0) int currentPageIndex,
    Decimal? currentBalance,
    @Default([]) List<RecurringEvent> recurringEvents,
    @Default(150.0) double estimatedDailySpending,
    @Default(500.0) double safetyThreshold, // 新增安心线
    @Default(false) bool isLoading,
    String? error,
    @Default(false) bool isCompleted, // 新增完成状态
  }) = _OnboardingState;

  factory OnboardingState.fromJson(Map<String, dynamic> json) =>
      _$OnboardingStateFromJson(json);
}

/// 引导步骤
enum OnboardingStep {
  @JsonValue('welcome')
  welcome,

  @JsonValue('current_balance')
  currentBalance,

  @JsonValue('recurring_events')
  recurringEvents,

  @JsonValue('daily_spending')
  dailySpending,

  @JsonValue('safety_threshold')
  safetyThreshold, // 新增安心线步骤

  @JsonValue('generating')
  generating,
}

/// 周期性事件
@freezed
abstract class RecurringEvent with _$RecurringEvent {
  const factory RecurringEvent({
    String? id,
    required RecurringEventType type,
    required String description,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal amount,
    required RecurringFrequency frequency,
    required DateTime startDate,
    DateTime? endDate,
    required String recurrenceRule, // RRULE format
  }) = _RecurringEvent;

  factory RecurringEvent.fromJson(Map<String, dynamic> json) =>
      _$RecurringEventFromJson(json);
}

/// 周期性事件类型
enum RecurringEventType {
  @JsonValue('income')
  income,
  
  @JsonValue('expense')
  expense,
}

/// 周期频率
enum RecurringFrequency {
  @JsonValue('monthly')
  monthly,
  
  @JsonValue('weekly')
  weekly,
}

/// 财务预测数据
@freezed
abstract class FinancialForecast with _$FinancialForecast {
  const factory FinancialForecast({
    required List<DailyBreakdown> dailyBreakdown,
    @Default([]) List<ForecastWarning> warnings,
    ForecastSummary? summary,
    List<DailyBreakdown>? simulatedDailyBreakdown, // What-If 模拟结果
  }) = _FinancialForecast;

  factory FinancialForecast.fromJson(Map<String, dynamic> json) =>
      _$FinancialForecastFromJson(json);
}

/// 每日财务明细
@freezed
abstract class DailyBreakdown with _$DailyBreakdown {
  const factory DailyBreakdown({
    required DateTime date,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal closingBalance,
    @Default([]) List<DailyEvent> events,
  }) = _DailyBreakdown;

  factory DailyBreakdown.fromJson(Map<String, dynamic> json) =>
      _$DailyBreakdownFromJson(json);
}

/// 预测摘要
@freezed
abstract class ForecastSummary with _$ForecastSummary {
  const factory ForecastSummary({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal startBalance,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal endBalance,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal totalIncome,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal totalExpense,
  }) = _ForecastSummary;

  factory ForecastSummary.fromJson(Map<String, dynamic> json) =>
      _$ForecastSummaryFromJson(json);
}

/// 每日事件
@freezed
abstract class DailyEvent with _$DailyEvent {
  const factory DailyEvent({
    required String description,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal amount,
    DailyEventType? type,
    String? category,
  }) = _DailyEvent;

  factory DailyEvent.fromJson(Map<String, dynamic> json) =>
      _$DailyEventFromJson(json);
}

/// 每日事件类型
enum DailyEventType {
  @JsonValue('actual')
  actual, // 实际记录的交易
  
  @JsonValue('predicted')
  predicted, // 预测的支出
  
  @JsonValue('recurring')
  recurring, // 周期性事件
}

/// 预测警告
@freezed
abstract class ForecastWarning with _$ForecastWarning {
  const factory ForecastWarning({
    required DateTime date,
    required String message,
    @Default(WarningLevel.warning) WarningLevel level,
  }) = _ForecastWarning;

  factory ForecastWarning.fromJson(Map<String, dynamic> json) =>
      _$ForecastWarningFromJson(json);
}

/// 警告级别
enum WarningLevel {
  @JsonValue('info')
  info,
  
  @JsonValue('warning')
  warning,
  
  @JsonValue('critical')
  critical,
}

/// What-If 场景
@freezed
abstract class WhatIfScenario with _$WhatIfScenario {
  const factory WhatIfScenario({
    required String amount,
    required String date, // ISO 8601 格式
    required String description,
  }) = _WhatIfScenario;

  factory WhatIfScenario.fromJson(Map<String, dynamic> json) =>
      _$WhatIfScenarioFromJson(json);
}

/// 预测请求
@freezed
abstract class ForecastRequest with _$ForecastRequest {
  const factory ForecastRequest({
    @Default(60) int forecastDays,
    @Default([]) List<WhatIfScenario> scenarios,
    @Default('daily') String granularity, // 🔥 添加granularity参数
  }) = _ForecastRequest;

  factory ForecastRequest.fromJson(Map<String, dynamic> json) =>
      _$ForecastRequestFromJson(json);
}



/// 周期性交易响应
@freezed
abstract class RecurringTransactionResponse with _$RecurringTransactionResponse {
  const factory RecurringTransactionResponse({
    required String id,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required String startDate,
    String? endDate,
    @Default([]) List<String> exceptionDates,
    bool? isActive,
    required String createdAt,
    required String updatedAt,
  }) = _RecurringTransactionResponse;

  factory RecurringTransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$RecurringTransactionResponseFromJson(json);
}

/// 创建周期性事件请求
@freezed
abstract class CreateRecurringEventRequest with _$CreateRecurringEventRequest {
  const factory CreateRecurringEventRequest({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal amount,
    String? categoryId, // 修改为可选的categoryId
    required String description,
    required String recurrenceRule,
    required String startDate, // 修改为String格式
    String? endDate, // 修改为String格式
  }) = _CreateRecurringEventRequest;

  factory CreateRecurringEventRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateRecurringEventRequestFromJson(json);
}

/// 用户设置请求
@freezed
abstract class UserSettingsRequest with _$UserSettingsRequest {
  const factory UserSettingsRequest({
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal estimatedAvgDailySpending,
    @JsonKey(fromJson: _decimalFromJson, toJson: _decimalToJson) required Decimal safetyBalanceThreshold,
  }) = _UserSettingsRequest;

  factory UserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsRequestFromJson(json);
}

/// 交易分类枚举
enum TransactionCategory {
  @JsonValue('1')
  dailyConsumption('1', '日常消费'),
  @JsonValue('2')
  transportation('2', '交通出行'),
  @JsonValue('3')
  healthcare('3', '医疗健康'),
  @JsonValue('4')
  housing('4', '住房物业'),
  @JsonValue('5')
  education('5', '教育培训'),
  @JsonValue('6')
  income('6', '收入进账'),
  @JsonValue('7')
  social('7', '社交馈赠'),
  @JsonValue('8')
  financial('8', '资金周转');

  const TransactionCategory(this.id, this.displayName);

  final String id;
  final String displayName;

  static TransactionCategory? fromId(String id) {
    for (final category in TransactionCategory.values) {
      if (category.id == id) return category;
    }
    return null;
  }
}

// 辅助函数：Decimal序列化/反序列化
Decimal _decimalFromJson(String value) => Decimal.parse(value);
String _decimalToJson(Decimal value) => value.toString();

// 可空Decimal序列化/反序列化
Decimal? _nullableDecimalFromJson(String? value) => value != null ? Decimal.parse(value) : null;
String? _nullableDecimalToJson(Decimal? value) => value?.toString();
