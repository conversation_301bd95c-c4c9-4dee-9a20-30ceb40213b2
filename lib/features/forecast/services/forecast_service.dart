// features/forecast/services/forecast_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:decimal/decimal.dart';
import '../../../core/network/network_client.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/forecast_models.dart';
import '../../profile/models/cash_pocket.dart';

class ForecastService {
  final NetworkClient _networkClient;

  ForecastService(this._networkClient);

  /// 检查引导状态
  Future<OnboardingStatusResponse> getOnboardingStatus() async {
    // 暂时使用模拟数据进行测试
    return await _mockGetOnboardingStatus();

    // 真实API调用代码（暂时注释）
    /*
    return await _networkClient.request<OnboardingStatusResponse>(
      '/onboarding/status',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return OnboardingStatusResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析引导状态响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /onboarding/status 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
    */
  }

  /// 创建初始现金来源（Step 2）
  Future<CashSourceSummary> createInitialCashSource(double initialBalance) async {
    final request = CashSourceRequest(sources: [
      CashSource(
        balance: Decimal.parse(initialBalance.toString()),
        sourceType: CashSourceType.preset,
        presetKey: 'initial_balance',
      ),
    ]);

    return await _networkClient.request<CashSourceSummary>(
      '/user/cash-sources',
      method: HttpMethod.post,
      data: request.toJson(),
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return CashSourceSummary.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析现金来源响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /user/cash-sources 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 保存用户设置（Step 4 & 5）
  Future<void> saveUserSettings({
    required double estimatedAvgDailySpending,
    required double safetyBalanceThreshold,
  }) async {
    final request = UserSettingsRequest(
      estimatedAvgDailySpending: Decimal.parse(estimatedAvgDailySpending.toString()),
      safetyBalanceThreshold: Decimal.parse(safetyBalanceThreshold.toString()),
    );

    await _networkClient.request<void>(
      '/user/settings',
      method: HttpMethod.put,
      data: request.toJson(),
      fromJsonT: (json) {}, // PUT请求通常不返回数据
    );
  }

  /// 创建周期性事件（Step 3）
  Future<RecurringTransactionResponse> createRecurringTransaction({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  }) async {
    final request = CreateRecurringEventRequest(
      amount: Decimal.parse(amount.toString()),
      categoryId: categoryId,
      description: description ?? '',
      recurrenceRule: recurrenceRule,
      startDate: startDate.toIso8601String().split('T')[0], // 只取日期部分
      endDate: endDate?.toIso8601String().split('T')[0],
    );

    return await _networkClient.request<RecurringTransactionResponse>(
      '/transactions/recurring',
      method: HttpMethod.post,
      data: request.toJson(),
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return RecurringTransactionResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析周期性交易响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /transactions/recurring 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  

  /// 获取财务预测
  Future<FinancialForecast> getForecast(ForecastRequest request) async {
    // 🔥 启用真实API调用
    print('🔥 调用真实API: /transactions/forecast');
    print('🔥 请求参数: ${request.toJson()}');

    return await _networkClient.request<FinancialForecast>(
      '/transactions/forecast',
      method: HttpMethod.post,
      data: request.toJson(),
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            print('🔥 API响应成功，解析数据...');
            return FinancialForecast.fromJson(json);
          } catch (e) {
            print('🔥 API响应解析失败: $e');
            throw DataParsingException("解析财务预测响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /transactions/forecast 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );

    // 模拟数据备用（如果API失败可以临时启用）
    // return await _mockGetForecast(request);
  }

  

  /// 获取用户财务配置（用于检查是否需要引导）
  Future<bool> hasCompletedOnboarding() async {
    // 暂时返回false以便测试引导流程
    return false;

    // 真实API调用代码（暂时注释）
    /*
    try {
      await _networkClient.requestMap(
        '/user/financial-profile',
        method: HttpMethod.get,
      );
      return true;
    } catch (e) {
      // 如果获取失败，认为用户未完成引导
      return false;
    }
    */
  }

  // === 开发阶段模拟数据 ===

  /// 模拟检查引导状态
  Future<OnboardingStatusResponse> _mockGetOnboardingStatus() async {
    await Future.delayed(const Duration(milliseconds: 500));

    // 暂时返回false以便测试引导流程
    return const OnboardingStatusResponse(isCompleted: false);
  }

  

  

  
}

// Provider
final forecastServiceProvider = Provider<ForecastService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return ForecastService(networkClient);
});
