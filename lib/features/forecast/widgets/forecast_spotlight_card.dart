// features/forecast/widgets/forecast_spotlight_card.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';

import 'package:decimal/decimal.dart';
import '../models/forecast_models.dart';
import 'forecast_chart.dart';
import '/shared/l10n/app_strings.dart';

class ForecastSpotlightCard extends ConsumerStatefulWidget {
  final FinancialForecast forecast;

  const ForecastSpotlightCard({
    super.key,
    required this.forecast,
  });

  @override
  ConsumerState<ForecastSpotlightCard> createState() => _ForecastSpotlightCardState();
}

class _ForecastSpotlightCardState extends ConsumerState<ForecastSpotlightCard>
    with TickerProviderStateMixin {
  late AnimationController _colorAnimationController;
  late AnimationController _scaleAnimationController;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _scaleAnimation;

  DailyBreakdown? _currentDayData;
  bool _isInteracting = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _colorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.easeInOut,
    ));

    // 初始化颜色动画为默认值
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.transparent,
    ).animate(CurvedAnimation(
      parent: _colorAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 在这里设置默认显示今天的数据，因为此时context已经可用
    _setTodayAsDefault();

    // 监听选中日期变化
    ref.listenManual<DateTime?>(selectedDateProvider, (previous, next) {
      if (next != null) {
        _updateSpotlightCard(next);
      } else if (!_isInteracting) {
        _setTodayAsDefault();
      }
    });
  }

  @override
  void dispose() {
    _colorAnimationController.dispose();
    _scaleAnimationController.dispose();
    super.dispose();
  }

  void _setTodayAsDefault() {
    final today = DateTime.now();
    final todayData = widget.forecast.dailyBreakdown.firstWhere(
      (breakdown) => _isSameDay(breakdown.date, today),
      orElse: () => widget.forecast.dailyBreakdown.first,
    );

    setState(() {
      _currentDayData = todayData;
      _isInteracting = false;
    });

    // 延迟调用动画更新，确保context可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateAnimations(todayData);
      }
    });
  }

  void _updateSpotlightCard(DateTime selectedDate) {
    final dayData = widget.forecast.dailyBreakdown.firstWhere(
      (breakdown) => _isSameDay(breakdown.date, selectedDate),
      orElse: () => widget.forecast.dailyBreakdown.first,
    );
    
    setState(() {
      _currentDayData = dayData;
      _isInteracting = true;
    });
    
    _updateAnimations(dayData);
  }

  void _updateAnimations(DailyBreakdown dayData) {
    // 检查context是否可用
    if (!mounted) return;

    final theme = context.theme;

    // 根据事件类型决定颜色
    Color targetColor = theme.colors.background;
    bool shouldScale = false;

    if (dayData.events.isNotEmpty) {
      final hasIncome = dayData.events.any((e) => e.amount > Decimal.zero);
      final hasLargeExpense = dayData.events.any((e) =>
        e.amount < Decimal.zero && e.amount.abs() > Decimal.fromInt(1000)
      );

      if (hasIncome) {
        targetColor = Colors.green.withValues(alpha: 0.1);
        shouldScale = true;
      } else if (hasLargeExpense) {
        targetColor = Colors.orange.withValues(alpha: 0.1);
        shouldScale = true;
      }
    }

    _colorAnimation = ColorTween(
      begin: theme.colors.background,
      end: targetColor,
    ).animate(CurvedAnimation(
      parent: _colorAnimationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimationController.forward();

    if (shouldScale) {
      _scaleAnimationController.forward();
    } else {
      _scaleAnimationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    if (_currentDayData == null) {
      return _buildLoadingCard(theme);
    }
    
    return AnimatedBuilder(
      animation: Listenable.merge([_colorAnimationController, _scaleAnimationController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              color: _colorAnimation.value ?? theme.colors.background,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: theme.colors.border),
              boxShadow: [
                BoxShadow(
                  color: theme.colors.foreground.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: _buildCardContent(theme),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingCard(FThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colors.background,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: theme.colors.border),
      ),
      child: Center(
        child: CircularProgressIndicator(
          color: theme.colors.primary,
        ),
      ),
    );
  }

  Widget _buildCardContent(FThemeData theme) {
    final dayData = _currentDayData!;
    final isToday = _isSameDay(dayData.date, DateTime.now());
    final isTomorrow = _isSameDay(dayData.date, DateTime.now().add(const Duration(days: 1)));

    String dateLabel;
    if (isToday) {
      dateLabel = AppStrings.get('todayLabel');
    } else if (isTomorrow) {
      dateLabel = AppStrings.get('tomorrowLabel');
    } else {
      // 根据语言格式化日期
      final currentLocale = AppStrings.getCurrentLocale();
      if (currentLocale == 'zh_CN') {
        dateLabel = '${dayData.date.month}月${dayData.date.day}日';
      } else {
        dateLabel = '${dayData.date.month}/${dayData.date.day}';
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 日期和余额标题
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isToday
                  ? theme.colors.primary
                  : theme.colors.muted.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                dateLabel,
                style: theme.typography.sm.copyWith(
                  color: isToday
                    ? theme.colors.primaryForeground
                    : theme.colors.foreground,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const Spacer(),

            Text(
              '${AppStrings.get('balanceLabel')}: ¥${dayData.closingBalance.toString()}',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 事件内容 - 使用Flexible而不是Expanded，允许内容自适应高度
        Flexible(
          child: dayData.events.isEmpty
            ? _buildNoEventsContent(theme, dateLabel)
            : _buildEventsContent(theme, dayData.events),
        ),
      ],
    );
  }

  Widget _buildNoEventsContent(FThemeData theme, String dateLabel) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FIcons.calendar,
            size: 32,
            color: theme.colors.mutedForeground.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 8),
          Text(
            '$dateLabel${AppStrings.get('noSpecialEvents')}',
            style: theme.typography.lg.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsContent(FThemeData theme, List<DailyEvent> events) {
    if (events.length == 1) {
      return _buildSingleEventContent(theme, events.first);
    } else {
      return _buildMultipleEventsContent(theme, events);
    }
  }

  Widget _buildSingleEventContent(FThemeData theme, DailyEvent event) {
    final isIncome = event.amount > Decimal.zero;
    final isLargeAmount = event.amount.abs() > Decimal.fromInt(1000);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 事件图标
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isIncome
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              _getEventIcon(event),
              size: 24,
              color: isIncome ? Colors.green : Colors.red,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // 事件描述
          Text(
            event.description,
            style: theme.typography.lg.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // 金额 - 大额时放大显示
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 200),
            style: theme.typography.xl2.copyWith(
              color: isIncome ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: isLargeAmount ? 32 : 24,
            ),
            child: Text(
              '${isIncome ? '+' : ''}¥${event.amount.abs().toString()}',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultipleEventsContent(FThemeData theme, List<DailyEvent> events) {
    // 显示最重要的2-3个事件
    final importantEvents = events.take(3).toList();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: importantEvents.map((event) {
        final isIncome = event.amount > Decimal.zero;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colors.muted.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                _getEventIcon(event),
                size: 16,
                color: isIncome ? Colors.green : Colors.red,
              ),

              const SizedBox(width: 12),

              Expanded(
                child: Text(
                  event.description,
                  style: theme.typography.sm.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              Text(
                '${isIncome ? '+' : ''}¥${event.amount.abs().toString()}',
                style: theme.typography.sm.copyWith(
                  color: isIncome ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  IconData _getEventIcon(DailyEvent event) {
    if (event.description.contains('工资') || event.description.contains('收入')) {
      return FIcons.banknote;
    } else if (event.description.contains('房租') || event.description.contains('住房')) {
      return FIcons.house;
    } else if (event.description.contains('预测') || event.description.contains('日常')) {
      return FIcons.trendingDown;
    }
    return FIcons.circle;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
