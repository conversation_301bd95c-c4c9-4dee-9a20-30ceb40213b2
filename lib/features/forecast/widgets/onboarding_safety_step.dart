// features/forecast/widgets/onboarding_safety_step.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import '/shared/l10n/app_strings.dart';

class OnboardingSafetyStep extends StatelessWidget {
  final double safetyThreshold;
  final Function(double) onThresholdChanged;
  final VoidCallback onNext;
  final VoidCallback onBack;

  const OnboardingSafetyStep({
    super.key,
    required this.safetyThreshold,
    required this.onThresholdChanged,
    required this.onNext,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight - 48,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      const Spacer(),

                      // 图标
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: colors.primary,
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: Icon(
                            FIcons.shield,
                            size: 40,
                            color: colors.primaryForeground,
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 标题
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 200),
                        child: Text(
                          AppStrings.get('tellMeYourSafetyLine'),
                          style: theme.typography.xl.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colors.foreground,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 副标题
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 400),
                        child: Text(
                          AppStrings.get('safetyLineDescription'),
                          style: theme.typography.base.copyWith(
                            color: colors.mutedForeground,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 48),

                      // 当前金额显示
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 600),
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: colors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: colors.primary.withValues(alpha: 0.2),
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(
                                '安心线',
                                style: theme.typography.sm.copyWith(
                                  color: colors.mutedForeground,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '¥ ${safetyThreshold.toStringAsFixed(0)}',
                                style: theme.typography.xl.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colors.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 滑块
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 800),
                        child: Column(
                          children: [
                            Slider(
                              value: safetyThreshold,
                              min: 0,
                              max: 20000,
                              onChanged: onThresholdChanged,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '¥0',
                                  style: theme.typography.sm.copyWith(
                                    color: colors.mutedForeground,
                                  ),
                                ),
                                Text(
                                  '¥20,000',
                                  style: theme.typography.sm.copyWith(
                                    color: colors.mutedForeground,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 快捷值按钮
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 1000),
                        child: Wrap(
                          spacing: 12,
                          runSpacing: 12,
                          children: [
                            _buildQuickValueButton(context, 500, '¥500'),
                            _buildQuickValueButton(context, 1000, '¥1,000'),
                            _buildQuickValueButton(context, 3000, '¥3,000'),
                            _buildQuickValueButton(context, 5000, '¥5,000'),
                            _buildQuickValueButton(context, 10000, '¥10,000'),
                            _buildQuickValueButton(context, 20000, '¥20,000'),
                          ],
                        ),
                      ),

                      const Spacer(),

                      // 按钮区域
                      FadeInUp(
                        duration: const Duration(milliseconds: 600),
                        delay: const Duration(milliseconds: 1200),
                        child: Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              child: FButton(
                                onPress: onNext,
                                child: const Text('完成设置'),
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: FButton(
                                style: FButtonStyle.outline(),
                                onPress: onBack,
                                child: const Text('返回'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildQuickValueButton(BuildContext context, double value, String label) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final isSelected = (safetyThreshold - value).abs() < 1;

    return FButton(
      style: FButtonStyle.outline(),
      onPress: () => onThresholdChanged(value),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.secondary : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? colorScheme.secondaryForeground : colorScheme.foreground,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
