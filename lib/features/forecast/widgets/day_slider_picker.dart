// features/forecast/widgets/day_slider_picker.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class DaySliderPicker extends StatefulWidget {
  final int initialDay;
  final ValueChanged<int> onChanged;
  final int minDay;
  final int maxDay;

  const DaySliderPicker({
    super.key,
    required this.initialDay,
    required this.onChanged,
    this.minDay = 1,
    this.maxDay = 28,
  });

  @override
  State<DaySliderPicker> createState() => _DaySliderPickerState();
}

class _DaySliderPickerState extends State<DaySliderPicker> {
  late PageController _pageController;
  late int _currentDay;

  @override
  void initState() {
    super.initState();
    _currentDay = widget.initialDay;
    _pageController = PageController(
      initialPage: _currentDay - widget.minDay,
      viewportFraction: 0.3,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _updateDay(int newDay) {
    if (newDay != _currentDay && newDay >= widget.minDay && newDay <= widget.maxDay) {
      setState(() {
        _currentDay = newDay;
      });
      widget.onChanged(newDay);
    }
  }

  void _goToPreviousDay() {
    if (_currentDay > widget.minDay) {
      final newDay = _currentDay - 1;
      _pageController.animateToPage(
        newDay - widget.minDay,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _updateDay(newDay);
    }
  }

  void _goToNextDay() {
    if (_currentDay < widget.maxDay) {
      final newDay = _currentDay + 1;
      _pageController.animateToPage(
        newDay - widget.minDay,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _updateDay(newDay);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Container(
      height: 120,
      decoration: BoxDecoration(
        borderRadius: theme.style.borderRadius,
        border: Border.all(color: theme.colors.border),
        color: theme.colors.background,
      ),
      child: Column(
        children: [
          // 标题和控制按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                FButton(
                  style: FButtonStyle.ghost(),
                  onPress: _currentDay > widget.minDay ? _goToPreviousDay : null,
                  child: const Icon(Icons.chevron_left, size: 20),
                ),
                Text(
                  '每月 $_currentDay 号',
                  style: theme.typography.lg.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                FButton(
                  style: FButtonStyle.ghost(),
                  onPress: _currentDay < widget.maxDay ? _goToNextDay : null,
                  child: const Icon(Icons.chevron_right, size: 20),
                ),
              ],
            ),
          ),
          
          // 滑动选择器
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                final newDay = index + widget.minDay;
                _updateDay(newDay);
              },
              itemCount: widget.maxDay - widget.minDay + 1,
              itemBuilder: (context, index) {
                final day = index + widget.minDay;
                final isSelected = day == _currentDay;
                
                return Center(
                  child: GestureDetector(
                    onTap: () {
                      _pageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: isSelected ? 60 : 50,
                      height: isSelected ? 60 : 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected
                          ? theme.colors.primary
                          : theme.colors.muted.withValues(alpha: 0.3),
                        border: Border.all(
                          color: isSelected
                            ? theme.colors.primary
                            : theme.colors.border,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '$day',
                          style: theme.typography.lg.copyWith(
                            color: isSelected
                              ? theme.colors.primaryForeground
                              : theme.colors.foreground,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}