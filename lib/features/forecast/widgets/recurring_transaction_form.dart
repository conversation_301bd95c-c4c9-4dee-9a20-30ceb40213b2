// features/forecast/widgets/recurring_transaction_form.dart
import 'package:flutter/material.dart';

import 'package:forui/forui.dart';

import '../models/forecast_models.dart';
import 'day_slider_picker.dart';


class RecurringTransactionForm extends StatefulWidget {
  final bool isIncome;
  final Function({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  }) onSubmit;

  const RecurringTransactionForm({
    super.key,
    required this.isIncome,
    required this.onSubmit,
  });

  @override
  State<RecurringTransactionForm> createState() => _RecurringTransactionFormState();
}

class _RecurringTransactionFormState extends State<RecurringTransactionForm> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  
  TransactionCategory? _selectedCategory;
  final DateTime _startDate = DateTime.now();
  int _dayOfMonth = DateTime.now().day;

  @override
  void initState() {
    super.initState();
    // 设置默认值
    if (widget.isIncome) {
      _descriptionController.text = '我的工资收入';
      _selectedCategory = TransactionCategory.income;
    } else {
      _descriptionController.text = '我的房租';
      _selectedCategory = TransactionCategory.housing;
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return AlertDialog(
      title: Text(widget.isIncome ? '添加固定收入' : '添加固定支出'),
      actions: [
        FButton(
          style: FButtonStyle.outline(),
          onPress: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        FButton(
          onPress: _handleSubmit,
          child: const Text('保存'),
        ),
      ],
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 描述输入
            Text(
              '描述',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            FTextField(
              controller: _descriptionController,
              hint: widget.isIncome ? '例如：工资收入' : '例如：房租支出',
            ),

            const SizedBox(height: 16),

            // 金额输入
            Text(
              '金额',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            FTextField(
              controller: _amountController,
              hint: '¥0.00',
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),

            const SizedBox(height: 16),

            // 分类选择（仅支出需要）
            if (!widget.isIncome) ...[
              Text(
                '分类',
                style: theme.typography.sm.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<TransactionCategory>(
                decoration: const InputDecoration(
                  hintText: '选择分类',
                ),
                items: TransactionCategory.values
                    .where((cat) => cat != TransactionCategory.income)
                    .map((category) => DropdownMenuItem(
                          value: category,
                          child: Text(category.displayName),
                        ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                },
                value: _selectedCategory,
              ),
              const SizedBox(height: 16),
            ],

            // 日期选择
            Text(
              '每月日期',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            DaySliderPicker(
              initialDay: _dayOfMonth,
              onChanged: (day) {
                setState(() {
                  _dayOfMonth = day;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!widget.isIncome && _selectedCategory == null) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择分类')),
      );
      return;
    }

    final amount = double.parse(_amountController.text.trim());
    final finalAmount = widget.isIncome ? amount : -amount; // 支出为负数

    // 生成RRULE
    final recurrenceRule = 'FREQ=MONTHLY;BYMONTHDAY=$_dayOfMonth';

    widget.onSubmit(
      amount: finalAmount,
      categoryId: widget.isIncome ? TransactionCategory.income.id : _selectedCategory?.id,
      description: _descriptionController.text.trim(),
      recurrenceRule: recurrenceRule,
      startDate: _startDate,
    );

    Navigator.of(context).pop();
  }
}
