// features/forecast/widgets/onboarding_events_step.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import 'package:decimal/decimal.dart';
import '../models/forecast_models.dart';
import 'recurring_transaction_form.dart';

class OnboardingEventsStep extends StatelessWidget {
  final List<RecurringEvent> recurringEvents;
  final bool isLoading;
  final String? error;
  final Function({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  }) onAddTransaction; // 新增API调用方法
  final Function(int) onRemoveEvent;
  final VoidCallback onNext;
  final VoidCallback onBack;
  final bool canProceed;
  final VoidCallback onClearError;

  const OnboardingEventsStep({
    super.key,
    required this.recurringEvents,
    required this.isLoading,
    required this.error,
    required this.onAddTransaction,
    required this.onRemoveEvent,
    required this.onNext,
    required this.onBack,
    required this.canProceed,
    required this.onClearError,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // 标题区域
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Column(
                  children: [
                    Icon(
                      FIcons.repeat,
                      size: 32,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '添加固定收支',
                      style: theme.typography.lg.copyWith(
                        fontWeight: FontWeight.w700,
                        color: colorScheme.foreground,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '添加固定收入和支出',
                      style: theme.typography.sm.copyWith(
                        color: colorScheme.mutedForeground,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // 错误提示
              if (error != null)
                FadeInUp(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: FAlert(
                      style: FAlertStyle.destructive(),
                      title: const Text('添加失败'),
                      subtitle: Text(error!),
                    ),
                  ),
                ),

              // 添加按钮
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 100),
                child: Row(
                  children: [
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: isLoading ? null : () => _showAddTransactionForm(context, true),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(FIcons.plus, size: 16, color: Colors.green),
                            const SizedBox(width: 6),
                            const Text('收入', style: TextStyle(fontSize: 14)),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: isLoading ? null : () => _showAddTransactionForm(context, false),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(FIcons.minus, size: 16, color: Colors.red),
                            const SizedBox(width: 6),
                            const Text('支出', style: TextStyle(fontSize: 14)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // 事件列表
              Expanded(
                child: FadeInUp(
                  duration: const Duration(milliseconds: 600),
                  delay: const Duration(milliseconds: 200),
                  child: recurringEvents.isEmpty
                      ? _buildEmptyState(theme)
                      : _buildEventsList(theme),
                ),
              ),

              // 进度提示
              if (recurringEvents.isNotEmpty) ...[
                const SizedBox(height: 8),
                FadeInUp(
                  duration: const Duration(milliseconds: 600),
                  delay: const Duration(milliseconds: 300),
                  child: _buildProgressHint(theme),
                ),
              ],

              const SizedBox(height: 16),

              // 按钮组
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Row(
                  children: [
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: onBack,
                        child: const Text('上一步'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FButton(
                        onPress: canProceed ? onNext : null,
                        child: const Text('下一步'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(FThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FIcons.calendar,
            size: 64,
            color: theme.colors.mutedForeground.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有添加任何事件',
            style: theme.typography.base.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击上方按钮添加你的第一个固定收入或支出',
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEventsList(FThemeData theme) {
    return ListView.builder(
      itemCount: recurringEvents.length,
      itemBuilder: (context, index) {
        final event = recurringEvents[index];
        return SlideInRight(
          duration: Duration(milliseconds: 300 + (index * 100)),
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // 类型图标
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: event.type == RecurringEventType.income
                            ? Colors.green.withValues(alpha: 0.1)
                            : Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        event.type == RecurringEventType.income
                            ? FIcons.trendingUp
                            : FIcons.trendingDown,
                        size: 16,
                        color: event.type == RecurringEventType.income
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),

                    const SizedBox(width: 10),
                    
                    // 事件信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.description,
                            style: theme.typography.sm.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${event.frequency == RecurringFrequency.monthly ? "每月" : "每周"} | ${event.amount > Decimal.zero ? "+" : ""}¥${event.amount.abs()}',
                            style: theme.typography.sm.copyWith(
                              color: theme.colors.mutedForeground,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // 删除按钮
                    FButton(
                      style: FButtonStyle.ghost(),
                      onPress: () => onRemoveEvent(index),
                      child: Icon(
                        FIcons.trash2,
                        size: 16,
                        color: theme.colors.destructive,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressHint(FThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: canProceed 
            ? theme.colors.primary.withValues(alpha: 0.1)
            : theme.colors.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: canProceed 
              ? theme.colors.primary.withValues(alpha: 0.3)
              : theme.colors.muted,
        ),
      ),
      child: Row(
        children: [
          Icon(
            canProceed ? FIcons.check : FIcons.info,
            size: 16,
            color: canProceed
                ? theme.colors.secondary
                : theme.colors.mutedForeground,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              canProceed
                  ? '很好！你已经添加了收入和支出，可以继续下一步了'
                  : '请至少添加一个固定收入和一个固定支出',
              style: theme.typography.sm.copyWith(
                color: canProceed
                    ? theme.colors.secondary
                    : theme.colors.mutedForeground,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTransactionForm(BuildContext context, bool isIncome) {
    showDialog(
      context: context,
      builder: (context) => RecurringTransactionForm(
        isIncome: isIncome,
        onSubmit: onAddTransaction,
      ),
    );
  }

  
}
