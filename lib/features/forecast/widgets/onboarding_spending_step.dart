// features/forecast/widgets/onboarding_spending_step.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import '/shared/l10n/app_strings.dart';

class OnboardingSpendingStep extends StatelessWidget {
  final double estimatedDailySpending;
  final Function(double) onSpendingChanged;
  final VoidCallback onNext;
  final VoidCallback onBack;

  const OnboardingSpendingStep({
    super.key,
    required this.estimatedDailySpending,
    required this.onSpendingChanged,
    required this.onNext,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight - 48,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      const Spacer(),

              // 图标
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: colorScheme.secondary,
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Icon(
                    FIcons.shoppingCart,
                    size: 40,
                    color: colorScheme.secondaryForeground,
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // 标题
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 100),
                child: Text(
                  AppStrings.get('dailySpendingQuestion'),
                  style: theme.typography.xl2.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.foreground,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // 描述
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: Text(
                  AppStrings.get('dailySpendingDescription'),
                  style: theme.typography.base.copyWith(
                    color: colorScheme.mutedForeground,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 60),

              // 金额显示
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 300),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Column(
                    children: [
                      Text(
                        '¥${estimatedDailySpending.toStringAsFixed(0)}',
                        style: theme.typography.xl4.copyWith(
                          fontSize: 48,
                          fontWeight: FontWeight.w700,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        AppStrings.get('perDay'),
                        style: theme.typography.lg.copyWith(
                          color: colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // 滑块
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Column(
                  children: [
                    Slider(
                      value: estimatedDailySpending,
                      min: 20,
                      max: 500,
                      onChanged: onSpendingChanged,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '¥20',
                          style: theme.typography.sm.copyWith(
                            color: colorScheme.mutedForeground,
                          ),
                        ),
                        Text(
                          '¥500',
                          style: theme.typography.sm.copyWith(
                            color: colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 40),

              // 参考提示
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 500),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.muted.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colorScheme.border,
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            FIcons.lightbulb,
                            size: 16,
                            color: colorScheme.mutedForeground,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AppStrings.get('referenceStandard'),
                            style: theme.typography.sm.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.foreground,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildReferenceItem(theme, AppStrings.get('frugalType'), AppStrings.get('frugalAmount')),
                      const SizedBox(height: 8),
                      _buildReferenceItem(theme, AppStrings.get('comfortableType'), AppStrings.get('comfortableAmount')),
                      const SizedBox(height: 8),
                      _buildReferenceItem(theme, AppStrings.get('relaxedType'), AppStrings.get('relaxedAmount')),
                    ],
                  ),
                ),
              ),

              const Spacer(),

              // 按钮组
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 600),
                child: Row(
                  children: [
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: onBack,
                        child: Text(AppStrings.get('previousStep')),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FButton(
                        onPress: onNext,
                        child: Text(AppStrings.get('completeMapping')),
                      ),
                    ),
                  ],
                ),
              ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildReferenceItem(FThemeData theme, String label, String amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
          ),
        ),
        Text(
          amount,
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
