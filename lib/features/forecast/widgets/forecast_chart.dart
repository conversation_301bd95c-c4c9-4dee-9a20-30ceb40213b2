// features/forecast/widgets/forecast_chart.dart
import 'dart:math';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:forui/forui.dart';
import '../models/forecast_models.dart';
import '../providers/forecast_providers.dart';

/// 选中日期Provider
final selectedDateProvider = StateProvider<DateTime?>((ref) => null);

/// 安心线阈值Provider (从用户设置获取，这里暂时硬编码)
final safetyThresholdProvider = StateProvider<double>((ref) => 1000.0);

/// 时间跨度枚举 - 映射到原有的ForecastDaysOption
enum TimeSpan {
  twoMonths('60D', '近2月', 60),
  sixMonths('180D', '近6月', 180),
  oneYear('365D', '近1年', 365);

  const TimeSpan(this.key, this.label, this.days);
  final String key;
  final String label;
  final int days;
}

class ForecastChart extends ConsumerStatefulWidget {
  const ForecastChart({super.key});

  @override
  ConsumerState<ForecastChart> createState() => _ForecastChartState();
}

class _ForecastChartState extends ConsumerState<ForecastChart> {
  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final safetyThreshold = ref.watch(safetyThresholdProvider);

    // 🔥 使用原有的forecast provider，只是改变selectedForecastDaysProvider的值
    final forecastAsync = ref.watch(forecastProvider);

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.border),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图表标题和时间跨度选择器
            _buildChartHeader(theme, forecastAsync.valueOrNull?.summary),

            const SizedBox(height: 20), // 🔥 增加间距从16到20
            // 主图表区域
            Expanded(
              child: forecastAsync.when(
                data: (forecast) => Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ), // 增加左右边距，确保居中
                  child: LineChart(
                    _buildChartData(colorScheme, safetyThreshold, forecast),
                    // 🔥 修复缩放和平移功能
                    transformationConfig: FlTransformationConfig(
                      scaleAxis: FlScaleAxis.horizontal, // 只允许水平缩放
                      minScale: 1.0, // 最小缩放比例
                      maxScale: _getMaxScale(forecast), // 🔥 根据数据量动态调整最大缩放
                      panEnabled: true, // 🔥 启用平移功能
                      scaleEnabled: true, // 🔥 启用缩放功能
                    ),
                  ),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: colorScheme.destructive,
                      ),
                      const SizedBox(height: 16),
                      Text('加载预测数据失败', style: theme.typography.lg),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: theme.typography.sm.copyWith(
                          color: colorScheme.mutedForeground,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 图例
            const SizedBox(height: 20), // 🔥 增加间距从16到20
            forecastAsync.when(
              data: (forecast) =>
                  _buildLegend(theme, colorScheme, safetyThreshold, forecast),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartHeader(FThemeData theme, ForecastSummary? summary) {
    return Column(
      children: [
        // 标题和期末余额
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Text(
                  //   '现金流预测',
                  //   style: theme.textTheme.large.copyWith(
                  //     fontWeight: FontWeight.w600,
                  //   ),
                  // ),
                  if (summary != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '期末余额: ¥${summary.endBalance.toString()}',
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20), // 🔥 增加间距从16到20
        // 时间跨度选择器
        _buildTimeSpanSelector(theme),
      ],
    );
  }

  Widget _buildTimeSpanSelector(FThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        // 从原有的selectedForecastDaysProvider获取当前选中的天数
        final selectedForecastDays = ref.watch(selectedForecastDaysProvider);
        final currentDays = selectedForecastDays.days;

        // 找到对应的TimeSpan
        final selectedTimeSpan = TimeSpan.values.firstWhere(
          (timeSpan) => timeSpan.days == currentDays,
          orElse: () => TimeSpan.twoMonths,
        );

        return FTabs(
          initialIndex: TimeSpan.values.indexOf(selectedTimeSpan),
          onPress: (index) {
            final timeSpan = TimeSpan.values[index];
            _onTimeSpanChanged(timeSpan, ref);
          },
          children: TimeSpan.values.map((timeSpan) {
            return FTabEntry(
              label: Text(
                timeSpan.label,
                style: theme.typography.sm.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              child: const SizedBox.shrink(), // 空内容，因为我们只需要tab选择器
            );
          }).toList(),
        );
      },
    );
  }

  void _onTimeSpanChanged(TimeSpan timeSpan, WidgetRef ref) {
    print('时间跨度变更: ${timeSpan.label}, 天数: ${timeSpan.days}');

    // 🔥 根据时间跨度确定数据粒度
    String granularity;
    switch (timeSpan) {
      case TimeSpan.twoMonths:
        granularity = 'daily';
        break;
      case TimeSpan.sixMonths:
        granularity = 'weekly';
        break;
      case TimeSpan.oneYear:
        granularity = 'monthly';
        break;
    }

    // 🔥 直接更新原有的selectedForecastDaysProvider
    final forecastDaysOption = ForecastDaysOption.values.firstWhere(
      (option) => option.days == timeSpan.days,
      orElse: () => ForecastDaysOption.days60, // 默认值
    );

    ref.read(selectedForecastDaysProvider.notifier).state = forecastDaysOption;

    // 🔥 强制刷新forecast provider以触发API调用
    ref.invalidate(forecastProvider);

    print('API调用参数: 天数=${timeSpan.days}, 粒度=$granularity');
  }

  LineChartData _buildChartData(
    FColors colorScheme,
    double safetyThreshold,
    FinancialForecast forecast,
  ) {
    final spots = _buildSpots(forecast.dailyBreakdown);
    final simulatedSpots = forecast.simulatedDailyBreakdown != null
        ? _buildSpots(forecast.simulatedDailyBreakdown!)
        : <FlSpot>[];

    // 🔥 添加调试信息和数据验证
    final granularity = _detectGranularity(forecast);
    final expectedDataPoints = _getExpectedDataPoints(granularity);
    final actualDataPoints = forecast.dailyBreakdown.length;

    print('🔥 图表数据调试:');
    print('  - 粒度: $granularity');
    print('  - 期望数据点: $expectedDataPoints');
    print('  - 实际数据点: $actualDataPoints');
    print(
      '  - 数据完整性: ${(actualDataPoints / expectedDataPoints * 100).toStringAsFixed(1)}%',
    );

    if (forecast.dailyBreakdown.isNotEmpty) {
      print('  - 第一个日期: ${forecast.dailyBreakdown.first.date}');
      print('  - 最后一个日期: ${forecast.dailyBreakdown.last.date}');

      // 🔥 检查数据不足的情况
      if (actualDataPoints < expectedDataPoints * 0.5) {
        print('  ⚠️ 警告：数据点不足，可能影响图表显示');
      }
    }

    // --- FIX 1: 计算并设置图表的完整数据边界 ---
    // 合并所有数据点，以找到全局的最大最小值
    final allSpots = [...spots, ...simulatedSpots];

    if (allSpots.isEmpty) {
      return LineChartData();
    }

    // X轴边界将在后面计算

    // 计算Y轴边界
    double minY = allSpots
        .map((spot) => spot.y)
        .reduce((a, b) => a < b ? a : b);
    double maxY = allSpots
        .map((spot) => spot.y)
        .reduce((a, b) => a > b ? a : b);

    // 🔧 修复负值问题：确保安心线被正确包含
    minY = math.min(minY, safetyThreshold);
    maxY = math.max(maxY, safetyThreshold);

    // 🔧 智能padding：避免负值，确保合理的显示范围
    final range = maxY - minY;
    final yPadding = range * 0.15; // 增加上下留白

    // 确保minY不会变成负数（除非数据本身就有负数）
    final originalMinY = allSpots
        .map((spot) => spot.y)
        .reduce((a, b) => a < b ? a : b);
    if (originalMinY >= 0) {
      minY = math.max(0, minY - yPadding); // 不允许变成负数
    } else {
      minY -= yPadding;
    }
    maxY += yPadding;

    // 🔥 计算X轴边界 - 让transformationConfig自动处理缩放和平移
    final minX = 0.0;
    final maxX = (forecast.dailyBreakdown.length - 1).toDouble();

    return LineChartData(
      // --- 设置图表的完整数据边界 ---
      minX: minX,
      maxX: maxX,
      minY: minY,
      maxY: maxY,

      // 🔥 启用裁剪
      clipData: const FlClipData.all(),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: _calculateGridInterval(allSpots, minY, maxY),
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: colorScheme.border.withValues(alpha: 0.3),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            // 🔧 优化Y轴保留空间 - 根据数据动态调整
            reservedSize: _calculateYAxisReservedSize(allSpots),
            interval: _calculateGridInterval(allSpots, minY, maxY),
            getTitlesWidget: (value, meta) {
              return Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Text(
                  _formatCurrency(value),
                  style: TextStyle(
                    color: colorScheme.mutedForeground,
                    fontSize: 9, // 稍微减小字体
                  ),
                  textAlign: TextAlign.right, // 右对齐
                ),
              );
            },
          ),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            // 🔥 根据数据粒度设置合适的interval
            interval: _calculateInterval(forecast),
            getTitlesWidget: (value, meta) {
              return _buildSmartDateLabel(value, meta, colorScheme, forecast);
            },
          ),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
      ),
      borderData: FlBorderData(show: false),
      lineTouchData: LineTouchData(
        enabled: true,
        handleBuiltInTouches: true, // 🔥 启用内置触摸，让fl_chart处理缩放和平移
        touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
          // 🔥 处理所有触摸事件，包括拖拽和点击
          if (touchResponse?.lineBarSpots?.isNotEmpty == true) {
            final spot = touchResponse!.lineBarSpots!.first;
            final index = spot.x.toInt();
            if (index >= 0 && index < forecast.dailyBreakdown.length) {
              final date = forecast.dailyBreakdown[index].date;
              ref.read(selectedDateProvider.notifier).state = date;
            }
          } else if (event is FlTapUpEvent || event is FlPanEndEvent) {
            // 🔥 当用户停止触摸且没有选中点时，清除选中状态
            ref.read(selectedDateProvider.notifier).state = null;
          }
        },
        getTouchedSpotIndicator: (barData, spotIndexes) {
          return spotIndexes.map((index) {
            return TouchedSpotIndicatorData(
              FlLine(
                color: colorScheme.primary,
                strokeWidth: 2,
                dashArray: [3, 3],
              ),
              FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 6,
                    color: colorScheme.primary,
                    strokeWidth: 2,
                    strokeColor: colorScheme.background,
                  );
                },
              ),
            );
          }).toList();
        },
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) => colorScheme.background,
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((spot) {
              final index = spot.x.toInt();
              if (index >= 0 && index < forecast.dailyBreakdown.length) {
                final breakdown = forecast.dailyBreakdown[index];
                final date = breakdown.date;
                final balance = spot.y;

                return LineTooltipItem(
                  '${date.month}/${date.day}\n¥${_formatCurrency(balance)}',
                  TextStyle(
                    color: colorScheme.foreground,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                );
              }
              return null;
            }).toList();
          },
        ),
      ),
      extraLinesData: ExtraLinesData(
        horizontalLines: [
          // 安心线
          HorizontalLine(
            y: safetyThreshold,
            color: colorScheme.destructive.withValues(alpha: 0.7),
            strokeWidth: 2,
            dashArray: [5, 5],
            label: HorizontalLineLabel(
              show: true,
              labelResolver: (line) => '安心线',
              style: TextStyle(
                color: colorScheme.destructive,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      lineBarsData: [
        // 实际预测线
        LineChartBarData(
          spots: spots,
          isCurved: true,
          curveSmoothness: 0.4,
          color: colorScheme.primary,
          barWidth: 3,
          isStrokeCapRound: true,
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.primary.withValues(alpha: 0.3),
                colorScheme.primary.withValues(alpha: 0.1),
              ],
            ),
          ),
          dotData: const FlDotData(show: false),
        ),

        // 模拟预测线（What-If场景）
        if (simulatedSpots.isNotEmpty)
          LineChartBarData(
            spots: simulatedSpots,
            isCurved: true,
            curveSmoothness: 0.4,
            color: colorScheme.secondary,
            barWidth: 3,
            isStrokeCapRound: true,
            dashArray: [5, 5],
            belowBarData: BarAreaData(show: false),
            dotData: const FlDotData(show: false),
          ),
      ],
    );
  }

  List<FlSpot> _buildSpots(List<DailyBreakdown> breakdown) {
    return breakdown.asMap().entries.map((entry) {
      final index = entry.key;
      final day = entry.value;
      return FlSpot(index.toDouble(), day.closingBalance.toDouble());
    }).toList();
  }

  double _calculateGridInterval(List<FlSpot> spots, double min, double max) {
    if (spots.isEmpty) return 1000;

    final range = max - min;
    if (range <= 0) return 100;

    // 目标是画4-5条线
    final rawInterval = range / 4;

    // 寻找一个"漂亮"的数字 (1, 2, 5的倍数)
    final magnitude = pow(10, (log(rawInterval) / log(10)).floor());
    final residual = rawInterval / magnitude;

    double niceInterval;
    if (residual < 1.5) {
      niceInterval = (1 * magnitude).toDouble();
    } else if (residual < 3) {
      niceInterval = (2 * magnitude).toDouble();
    } else if (residual < 7) {
      niceInterval = (5 * magnitude).toDouble();
    } else {
      niceInterval = (10 * magnitude).toDouble();
    }

    return niceInterval;
  }

  /// 动态计算Y轴保留空间
  double _calculateYAxisReservedSize(List<FlSpot> spots) {
    if (spots.isEmpty) return 50;

    // 找到最大值来估算标签宽度
    final maxValue = spots
        .map((spot) => spot.y)
        .reduce((a, b) => a > b ? a : b);
    final formattedMax = _formatCurrency(maxValue);

    // 根据格式化后的字符串长度估算宽度
    if (formattedMax.length <= 4) return 35; // "1000"
    if (formattedMax.length <= 6) return 45; // "10.5万"
    if (formattedMax.length <= 8) return 55; // "100.5万"
    return 65; // 更长的数字
  }

  /// 智能计算日期间隔
  double _calculateSmartDateInterval(TitleMeta meta) {
    final totalRange = meta.max - meta.min;

    // 根据当前显示的范围和数据长度智能调整间隔
    if (totalRange <= 7) {
      // 显示范围在一周内，每天显示
      return 1;
    } else if (totalRange <= 30) {
      // 显示范围在一个月内，每3-5天显示
      return (totalRange / 6).ceil().toDouble();
    } else if (totalRange <= 90) {
      // 显示范围在三个月内，每周显示
      return (totalRange / 8).ceil().toDouble();
    } else {
      // 显示范围超过三个月，每月显示
      return (totalRange / 6).ceil().toDouble();
    }
  }

  /// 智能构建日期标签
  Widget _buildSmartDateLabel(
    double value,
    TitleMeta meta,
    FColors colorScheme,
    FinancialForecast forecast,
  ) {
    final index = value.toInt();
    if (index < 0 || index >= forecast.dailyBreakdown.length) {
      return const SizedBox.shrink();
    }

    final date = forecast.dailyBreakdown[index].date;
    final granularity = _detectGranularity(forecast);

    String labelText;

    // 🔥 根据数据粒度决定标签格式
    switch (granularity) {
      case 'monthly':
        // 月度数据：显示月份
        if (date.year == DateTime.now().year) {
          labelText = '${date.month}月';
        } else {
          labelText = '${date.year % 100}/${date.month}';
        }
        break;
      case 'weekly':
        // 周度数据：显示月日
        labelText = '${date.month}/${date.day}';
        break;
      case 'daily':
      default:
        // 日度数据：显示月日
        labelText = '${date.month}/${date.day}';
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        labelText,
        style: TextStyle(color: colorScheme.mutedForeground, fontSize: 10),
      ),
    );
  }

  /// 检测数据粒度 - 基于当前选择的时间跨度
  String _detectGranularity(FinancialForecast forecast) {
    // 🔥 直接从当前选择的时间跨度获取粒度，而不是分析数据间隔
    final selectedDays = ref.read(selectedForecastDaysProvider);

    switch (selectedDays.days) {
      case 60:
        return 'daily';
      case 180:
        return 'weekly';
      case 365:
        return 'monthly';
      default:
        return 'daily';
    }
  }

  /// 获取期望的数据点数量
  int _getExpectedDataPoints(String granularity) {
    final selectedDays = ref.read(selectedForecastDaysProvider);

    switch (granularity) {
      case 'monthly':
        return (selectedDays.days / 30).ceil(); // 大约每月一个点
      case 'weekly':
        return (selectedDays.days / 7).ceil(); // 大约每周一个点
      case 'daily':
      default:
        return selectedDays.days; // 每天一个点
    }
  }

  /// 根据数据粒度计算合适的interval
  double _calculateInterval(FinancialForecast forecast) {
    final granularity = _detectGranularity(forecast);
    final dataLength = forecast.dailyBreakdown.length;

    // 🔥 根据实际数据点数量和屏幕宽度动态调整interval
    // 目标：在标准屏幕宽度下，横坐标标签不重叠
    final maxLabelsOnScreen = 6; // 屏幕上最多显示6个标签，避免重叠

    switch (granularity) {
      case 'monthly':
        // 月度数据：确保标签不重叠
        if (dataLength <= maxLabelsOnScreen) return 1.0;
        return (dataLength / maxLabelsOnScreen).ceil().toDouble();
      case 'weekly':
        // 周度数据：确保标签不重叠
        if (dataLength <= maxLabelsOnScreen) return 1.0;
        return (dataLength / maxLabelsOnScreen).ceil().toDouble();
      case 'daily':
      default:
        // 日度数据：根据数据量调整
        if (dataLength <= 7) return 1.0; // 一周内每天显示
        if (dataLength <= 14) return 2.0; // 两周内每2天显示
        if (dataLength <= 30) return 5.0; // 一月内每5天显示
        if (dataLength <= 60) return 10.0; // 两月内每10天显示
        return (dataLength / maxLabelsOnScreen).ceil().toDouble(); // 动态计算
    }
  }

  /// 根据数据量动态计算最大缩放比例
  double _getMaxScale(FinancialForecast forecast) {
    final granularity = _detectGranularity(forecast);

    switch (granularity) {
      case 'monthly':
        // 月度数据：通常12个点，允许较大缩放
        return 8.0;
      case 'weekly':
        // 周度数据：通常26个点，中等缩放
        return 6.0;
      case 'daily':
      default:
        // 日度数据：60个点，较小缩放
        return 4.0;
    }
  }

  String _formatCurrency(double value) {
    if (value.abs() >= 10000) {
      return '${(value / 10000).toStringAsFixed(1)}万';
    }
    return value.toStringAsFixed(0);
  }

  Widget _buildLegend(
    FThemeData theme,
    FColors colorScheme,
    double safetyThreshold,
    FinancialForecast forecast,
  ) {
    return Row(
      children: [
        _buildLegendItem(theme, colorScheme.primary, '预测曲线', false),
        const SizedBox(width: 16),
        _buildLegendItem(theme, colorScheme.destructive, '安心线', true),
        if (forecast.simulatedDailyBreakdown != null) ...[
          const SizedBox(width: 16),
          _buildLegendItem(theme, colorScheme.secondary, '模拟曲线', true),
        ],
      ],
    );
  }

  Widget _buildLegendItem(
    FThemeData theme,
    Color color,
    String label,
    bool isDashed,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 2,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(1),
          ),
          child: isDashed
              ? CustomPaint(painter: DashedLinePainter(color: color))
              : null,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
          ),
        ),
      ],
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
