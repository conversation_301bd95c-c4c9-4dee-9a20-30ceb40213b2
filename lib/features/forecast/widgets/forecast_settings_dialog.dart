// features/forecast/widgets/forecast_settings_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import '../providers/forecast_providers.dart';
import '../services/forecast_service.dart';
import 'recurring_transaction_form.dart';

class ForecastSettingsDialog extends ConsumerWidget {
  const ForecastSettingsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24), // 🔥 左右边距
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width - 48, // 🔥 确保左右有24px边距
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: AlertDialog(
            title: const Text('预测设置'),
            content: Container(
              constraints: const BoxConstraints(maxHeight: 600),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12), // 🔥 增加圆角
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 设置选项列表
                  Flexible(
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                  _buildSettingItem(
                    context,
                    theme,
                    icon: FIcons.trendingUp,
                    title: '核心收入设置',
                    subtitle: '管理您的固定收入来源',
                    onTap: () => _showRecurringIncomeSettings(context, ref),
                  ),
                  
                  _buildSettingItem(
                    context,
                    theme,
                    icon: FIcons.trendingDown,
                    title: '核心支出设置',
                    subtitle: '管理您的固定支出项目',
                    onTap: () => _showRecurringExpenseSettings(context, ref),
                  ),
                  
                  _buildSettingItem(
                    context,
                    theme,
                    icon: FIcons.shield,
                    title: '安心线设置',
                    subtitle: '调整您的财务安全阈值',
                    onTap: () => _showSafetyThresholdSettings(context, ref),
                  ),
                  
                  _buildSettingItem(
                    context,
                    theme,
                    icon: FIcons.calculator,
                    title: '日常消费预估',
                    subtitle: '调整每日消费预测金额',
                    onTap: () => _showDailySpendingSettings(context, ref),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 重新引导按钮
                  FButton(
                    style: FButtonStyle.outline(),
                    onPress: () {
                      Navigator.of(context).pop();
                      context.go('/forecast/onboarding');
                    },
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(FIcons.refreshCw, size: 16),
                        SizedBox(width: 8),
                        Text('重新设置引导'),
                      ],
                    ),
                  ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 关闭按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      FButton(
                        style: FButtonStyle.ghost(),
                        onPress: () => Navigator.of(context).pop(),
                        child: const Text('关闭'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context,
    ShadThemeData theme, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Card(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: theme.colors.primary,
                  ),
                ),

                const SizedBox(width: 12),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.typography.sm.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: theme.typography.sm.copyWith(
                          color: theme.colors.mutedForeground,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),

                Icon(
                  FIcons.chevronRight,
                  size: 16,
                  color: theme.colors.mutedForeground,
                ),
              ],
            ),
          ),
        ),
      ),
    );

  }
  void _showRecurringExpenseSettings(BuildContext context, WidgetRef ref) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) =>
          RecurringTransactionForm(
            isIncome: false,
            onSubmit: ({
              required double amount,
              String? categoryId,
              String? description,
              required String recurrenceRule,
              required DateTime startDate,
              DateTime? endDate,
            }) {
              // 处理支出设置更新
              Navigator.of(context).pop();
              ref.invalidate(forecastProvider);
            },
          ),
    );
  }


  void _showRecurringIncomeSettings(BuildContext context, WidgetRef ref) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) =>
          RecurringTransactionForm(
            isIncome: true,
            onSubmit: ({
              required double amount,
              String? categoryId,
              String? description,
              required String recurrenceRule,
              required DateTime startDate,
              DateTime? endDate,
            }) {
              // 处理收入设置更新
              Navigator.of(context).pop();
              ref.invalidate(forecastProvider);
            },
          ),
    );
  }



  void _showSafetyThresholdSettings(BuildContext context, WidgetRef ref) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) => _SafetyThresholdDialog(ref: ref),
    );
  }

  void _showDailySpendingSettings(BuildContext context, WidgetRef ref) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) => _DailySpendingDialog(ref: ref),
    );
  }
}

/// 安心线设置对话框
class _SafetyThresholdDialog extends StatefulWidget {
  final WidgetRef ref;

  const _SafetyThresholdDialog({required this.ref});

  @override
  State<_SafetyThresholdDialog> createState() => _SafetyThresholdDialogState();
}

class _SafetyThresholdDialogState extends State<_SafetyThresholdDialog> {
  late TextEditingController _controller;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: '1000'); // 默认值
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _saveSettings() async {
    final value = double.tryParse(_controller.text);
    if (value == null || value <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的金额')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final forecastService = widget.ref.read(forecastServiceProvider);
      await forecastService.saveUserSettings(
        estimatedAvgDailySpending: 150.0, // 保持原值，只更新安心线
        safetyBalanceThreshold: value,
      );

      // 刷新预测数据
      widget.ref.invalidate(forecastProvider);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('安心线设置已保存')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24), // 🔥 左右边距
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width - 48, // 🔥 确保左右有24px边距
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: AlertDialog(
            title: const Text('安心线设置'),
            content: Container(
              width: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12), // 🔥 增加圆角
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
            Text(
              '设置您的财务安全阈值，当余额低于此值时会收到提醒',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),

            const SizedBox(height: 16),

            FTextField(
              controller: _controller,
              hint: '请输入安心线金额',
              keyboardType: TextInputType.number,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 24),

            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                FButton(
                  style: FButtonStyle.ghost(),
                  onPress: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),

                const SizedBox(width: 8),

                FButton(
                  onPress: _isLoading ? null : _saveSettings,
                  child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('保存'),
                ),
              ],
            ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 日常消费设置对话框
class _DailySpendingDialog extends StatefulWidget {
  final WidgetRef ref;

  const _DailySpendingDialog({required this.ref});

  @override
  State<_DailySpendingDialog> createState() => _DailySpendingDialogState();
}

class _DailySpendingDialogState extends State<_DailySpendingDialog> {
  late TextEditingController _controller;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: '150'); // 默认值
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _saveSettings() async {
    final value = double.tryParse(_controller.text);
    if (value == null || value <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的金额')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final forecastService = widget.ref.read(forecastServiceProvider);
      await forecastService.saveUserSettings(
        estimatedAvgDailySpending: value,
        safetyBalanceThreshold: 1000.0, // 保持原值，只更新日常消费
      );

      // 刷新预测数据
      widget.ref.invalidate(forecastProvider);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('日常消费预估已保存')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24), // 🔥 左右边距
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width - 48, // 🔥 确保左右有24px边距
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: AlertDialog(
            title: const Text('日常消费预估'),
            content: Container(
              width: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12), // 🔥 增加圆角
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
            Text(
              '设置您的平均每日消费金额，用于财务预测计算',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),

            const SizedBox(height: 16),

            FTextField(
              controller: _controller,
              hint: '请输入每日消费金额',
              keyboardType: TextInputType.number,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 24),

            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                FButton(
                  style: FButtonStyle.ghost(),
                  onPress: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),

                const SizedBox(width: 8),

                FButton(
                  onPress: _isLoading ? null : _saveSettings,
                  child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('保存'),
                ),
              ],
            ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}