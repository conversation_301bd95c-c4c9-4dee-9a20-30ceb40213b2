// features/forecast/widgets/forecast_warnings_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../models/forecast_models.dart';
import '../providers/forecast_providers.dart';

class ForecastWarningsDialog extends ConsumerWidget {
  const ForecastWarningsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final forecastAsync = ref.watch(forecastProvider);

    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24), // 🔥 左右边距
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width - 48, // 🔥 确保左右有24px边距
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: AlertDialog(
            title: Row(
              children: [
                Icon(
                  FIcons.triangleAlert,
                  size: 20,
                  color: theme.colors.destructive,
                ),
                const SizedBox(width: 8),
                const Text('风险预警'),
              ],
            ),
            content: Container(
              constraints: const BoxConstraints(maxHeight: 400),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12), // 🔥 增加圆角
              ),
              child: forecastAsync.when(
                data: (forecast) => _buildWarningsContent(context, theme, forecast.warnings),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text(
                    '加载失败: $error',
                    style: theme.typography.sm.copyWith(
                      color: theme.colors.destructive,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWarningsContent(BuildContext context, FThemeData theme, List<ForecastWarning> warnings) {
    if (warnings.isEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FIcons.check,
            size: 48,
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无风险预警',
            style: theme.typography.lg.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '您的财务状况良好！',
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              FButton(
                style: FButtonStyle.ghost(),
                onPress: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '发现 ${warnings.length} 个潜在风险点',
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
          ),
        ),
        const SizedBox(height: 16),
        Flexible(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: warnings.length,
            itemBuilder: (context, index) {
              final warning = warnings[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Card(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      // TODO: 跳转到对应日期
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Icon(
                            FIcons.triangleAlert,
                            size: 16,
                            color: theme.colors.destructive,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              warning.message,
                              style: theme.typography.sm,
                            ),
                          ),
                          Icon(
                            FIcons.chevronRight,
                            size: 16,
                            color: theme.colors.mutedForeground,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            FButton(
              style: FButtonStyle.ghost(),
              onPress: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      ],
    );
  }
}
