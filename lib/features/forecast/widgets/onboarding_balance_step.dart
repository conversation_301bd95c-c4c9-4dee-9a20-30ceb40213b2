// features/forecast/widgets/onboarding_balance_step.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import 'package:decimal/decimal.dart';

class OnboardingBalanceStep extends StatefulWidget {
  final Decimal? currentBalance;
  final Function(Decimal) onBalanceChanged;
  final VoidCallback onNext;
  final VoidCallback onBack;
  final bool canProceed;

  const OnboardingBalanceStep({
    super.key,
    required this.currentBalance,
    required this.onBalanceChanged,
    required this.onNext,
    required this.onBack,
    required this.canProceed,
  });

  @override
  State<OnboardingBalanceStep> createState() => _OnboardingBalanceStepState();
}

class _OnboardingBalanceStepState extends State<OnboardingBalanceStep> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.currentBalance?.toString() ?? '',
    );
    
    // 自动聚焦输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onBalanceChanged(String value) {
    if (value.isEmpty) {
      return;
    }
    
    try {
      final balance = Decimal.parse(value);
      widget.onBalanceChanged(balance);
    } catch (e) {
      // 忽略无效输入
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight - 48,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      const Spacer(),

              // 图标
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: colorScheme.secondary,
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Icon(
                    FIcons.wallet,
                    size: 40,
                    color: colorScheme.secondaryForeground,
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // 标题
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 100),
                child: Text(
                  '我们从哪里出发？',
                  style: theme.typography.xl2.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.foreground,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // 描述
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: Text(
                  '输入你所有账户的大概总额\n这将作为我们预测的起点',
                  style: theme.typography.base.copyWith(
                    color: colorScheme.mutedForeground,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 40),

              // 输入框
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 300),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: widget.canProceed 
                          ? colorScheme.primary 
                          : colorScheme.border,
                      width: 2,
                    ),
                    color: colorScheme.background,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Row(
                      children: [
                        Text(
                          '¥',
                          style: theme.typography.xl.copyWith(
                            color: colorScheme.mutedForeground,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: _controller,
                            focusNode: _focusNode,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                            ],
                            style: theme.typography.xl.copyWith(
                              color: colorScheme.foreground,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              hintText: '0.00',
                            ),
                            onChanged: _onBalanceChanged,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // 提示信息
              if (widget.canProceed)
                FadeInUp(
                  duration: const Duration(milliseconds: 400),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: colorScheme.secondary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colorScheme.secondary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          FIcons.info,
                          size: 16,
                          color: colorScheme.secondaryForeground,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '不用太精确，我们会根据你的实际使用情况不断优化预测',
                            style: theme.typography.sm.copyWith(
                              color: colorScheme.secondaryForeground,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              const Spacer(),

              // 按钮组
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Row(
                  children: [
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: widget.onBack,
                        child: const Text('上一步'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FButton(
                        onPress: widget.canProceed ? widget.onNext : null,
                        child: const Text('下一步'),
                      ),
                    ),
                  ],
                ),
              ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
