// features/forecast/widgets/onboarding_welcome_step.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import '/shared/l10n/app_strings.dart';

class OnboardingWelcomeStep extends StatelessWidget {
  final VoidCallback onNext;

  const OnboardingWelcomeStep({
    super.key,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 
                         MediaQuery.of(context).padding.top - 
                         MediaQuery.of(context).padding.bottom - 48,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 图标动画
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          colorScheme.primary,
                          colorScheme.primary.withValues(alpha: 0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(50),
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Icon(
                      FIcons.sparkles,
                      size: 50,
                      color: colorScheme.primaryForeground,
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // 主标题
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  delay: const Duration(milliseconds: 200),
                  child: Text(
                    AppStrings.get('financialNavigator'),
                    style: theme.typography.xl2.copyWith(
                      fontWeight: FontWeight.w700,
                      color: colorScheme.foreground,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 12),

                // 副标题
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  delay: const Duration(milliseconds: 400),
                  child: Text(
                    AppStrings.get('financialMapSubtitle'),
                    style: theme.typography.base.copyWith(
                      color: colorScheme.mutedForeground,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 40),

                // 特性列表
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  delay: const Duration(milliseconds: 600),
                  child: Column(
                    children: [
                      _buildFeatureItem(
                        theme,
                        icon: FIcons.trendingUp,
                        title: AppStrings.get('predictCashFlow'),
                        description: AppStrings.get('predictCashFlowDesc'),
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        theme,
                        icon: FIcons.lightbulb,
                        title: AppStrings.get('aiSmartSuggestions'),
                        description: AppStrings.get('aiSmartSuggestionsDesc'),
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                        theme,
                        icon: FIcons.shield,
                        title: AppStrings.get('riskWarning'),
                        description: AppStrings.get('riskWarningDesc'),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),

                // 开始按钮
                FadeInUp(
                  duration: const Duration(milliseconds: 800),
                  delay: const Duration(milliseconds: 800),
                  child: SizedBox(
                    width: double.infinity,
                    child: FButton(
                      onPress: onNext,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('开始领航'),
                          const SizedBox(width: 8),
                          Icon(
                            FIcons.arrowRight,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    FThemeData theme, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    final colorScheme = theme.colors;

    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: colorScheme.secondary,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            size: 20,
            color: colorScheme.secondaryForeground,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.typography.sm.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.foreground,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: theme.typography.sm.copyWith(
                  color: colorScheme.mutedForeground,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
