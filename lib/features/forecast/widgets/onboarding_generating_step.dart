// features/forecast/widgets/onboarding_generating_step.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import '/shared/l10n/app_strings.dart';

class OnboardingGeneratingStep extends StatefulWidget {
  final bool isLoading;
  final String? error;
  final VoidCallback onRetry;
  final VoidCallback onBack;
  final VoidCallback? onComplete; // 添加完成回调

  const OnboardingGeneratingStep({
    super.key,
    required this.isLoading,
    required this.error,
    required this.onRetry,
    required this.onBack,
    this.onComplete, // 可选的完成回调
  });

  @override
  State<OnboardingGeneratingStep> createState() => _OnboardingGeneratingStepState();
}

class _OnboardingGeneratingStepState extends State<OnboardingGeneratingStep>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    if (widget.isLoading) {
      _startAnimations();
    }
  }

  @override
  void didUpdateWidget(OnboardingGeneratingStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading && !oldWidget.isLoading) {
      _startAnimations();
    } else if (!widget.isLoading && oldWidget.isLoading) {
      _stopAnimations();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startAnimations() {
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _rotationController.stop();
    _pulseController.stop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    if (widget.error != null) {
      return _buildErrorState(theme, colorScheme);
    }

    if (widget.isLoading) {
      return _buildLoadingState(theme, colorScheme);
    }

    return _buildSuccessState(theme, colorScheme);
  }

  Widget _buildLoadingState(FThemeData theme, FColors colorScheme) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                         MediaQuery.of(context).padding.top -
                         MediaQuery.of(context).padding.bottom - 48,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // 动画图标
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (_pulseController.value * 0.1),
                      child: AnimatedBuilder(
                        animation: _rotationController,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _rotationController.value * 2 * 3.14159,
                            child: Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    colorScheme.primary,
                                    colorScheme.primary.withValues(alpha: 0.7),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(60),
                                boxShadow: [
                                  BoxShadow(
                                    color: colorScheme.primary.withValues(alpha: 0.3),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Icon(
                                FIcons.sparkles,
                                size: 60,
                                color: colorScheme.primaryForeground,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 40),

              // 标题
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: Text(
                  '正在绘制你的财务地图...',
                  style: theme.typography.xl2.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.foreground,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // 描述
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Text(
                  AppStrings.get('analyzingFinancialData'),
                  style: theme.typography.base.copyWith(
                    color: colorScheme.mutedForeground,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 60),

              // 进度提示
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 600),
                child: Column(
                  children: [
                    _buildProgressItem(theme, AppStrings.get('analyzeIncomeExpensePattern'), true),
                    const SizedBox(height: 12),
                    _buildProgressItem(theme, AppStrings.get('calculateCashFlowTrend'), true),
                    const SizedBox(height: 12),
                    _buildProgressItem(theme, AppStrings.get('generateRiskWarning'), false),
                  ],
                ),
              ),
            ],
          ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessState(FThemeData theme, FColors colorScheme) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                         MediaQuery.of(context).padding.top -
                         MediaQuery.of(context).padding.bottom - 48,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // 成功图标
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    size: 60,
                    color: Colors.green,
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // 标题
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: Text(
                  '财务地图绘制完成！',
                  style: theme.typography.xl2.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.foreground,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // 描述
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Text(
                  '你的个人财务飞行模拟器已经准备就绪\n现在可以开始探索你的财务未来了',
                  style: theme.typography.base.copyWith(
                    color: colorScheme.mutedForeground,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 60),

              // 特性预览
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 600),
                child: Column(
                  children: [
                    _buildFeaturePreview(theme, FIcons.trendingUp, '30天现金流预测'),
                    const SizedBox(height: 16),
                    _buildFeaturePreview(theme, FIcons.shield, '智能风险预警'),
                    const SizedBox(height: 16),
                    _buildFeaturePreview(theme, FIcons.lightbulb, 'AI财务建议'),
                  ],
                ),
              ),

              const SizedBox(height: 60),

              // 完成按钮
              if (widget.onComplete != null)
                FadeInUp(
                  duration: const Duration(milliseconds: 600),
                  delay: const Duration(milliseconds: 800),
                  child: SizedBox(
                    width: double.infinity,
                    child: FButton(
                      onPress: widget.onComplete,
                      child: const Text('开始探索'),
                    ),
                  ),
                ),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(FThemeData theme, FColors colorScheme) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                         MediaQuery.of(context).padding.top -
                         MediaQuery.of(context).padding.bottom - 48,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // 错误图标
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: colorScheme.destructive.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: colorScheme.destructive.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    FIcons.x,
                    size: 60,
                    color: colorScheme.destructive,
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // 错误信息
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: FAlert(
                  style: FAlertStyle.destructive(),
                  title: const Text('生成失败'),
                  subtitle: Text(widget.error!),
                ),
              ),

              const SizedBox(height: 40),

              // 按钮组
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 400),
                child: Row(
                  children: [
                    Expanded(
                      child: FButton(
                        style: FButtonStyle.outline(),
                        onPress: widget.onBack,
                        child: const Text('返回修改'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FButton(
                        onPress: widget.onRetry,
                        child: const Text('重试'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressItem(FThemeData theme, String text, bool isCompleted) {
    return Row(
      children: [
        Icon(
          isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 20,
          color: isCompleted ? Colors.green : theme.colors.mutedForeground,
        ),
        const SizedBox(width: 12),
        Text(
          text,
          style: theme.typography.base.copyWith(
            color: isCompleted
                ? theme.colors.foreground
                : theme.colors.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturePreview(FThemeData theme, IconData icon, String text) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: theme.colors.secondary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: theme.colors.secondaryForeground,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          text,
          style: theme.typography.base.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
