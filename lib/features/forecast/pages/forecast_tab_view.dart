import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';

import 'cash_sources_page.dart';
import 'forecast_page.dart';
import '../widgets/forecast_warnings_dialog.dart';

class ForecastTabView extends ConsumerStatefulWidget {
  const ForecastTabView({super.key});

  @override
  ConsumerState<ForecastTabView> createState() => _ForecastTabViewState();
}

class _ForecastTabViewState extends ConsumerState<ForecastTabView> {
  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        title: Text('财务', style: theme.typography.xl),
        backgroundColor: colorScheme.background,
        foregroundColor: colorScheme.foreground,
        elevation: 0,
        centerTitle: true,
        actions: [
          // 风险预警消息图标
          Consumer(
            builder: (context, ref, child) {
              return FButton(
                style: FButtonStyle.ghost(),
                onPress: () {
                  _showWarningsDialog(context, ref);
                },
                child: Stack(
                  children: [
                    Icon(FIcons.bell, size: 20, color: colorScheme.foreground),
                    // 预警数量徽章
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: colorScheme.destructive,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: Text(
                          '3', // 暂时硬编码，后续从forecast数据获取
                          style: TextStyle(
                            fontSize: 8,
                            color: colorScheme.destructiveForeground,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      // 添加抽屉栏
      drawer: _buildDrawer(context),
      // 直接显示预测页面内容
      body: const ForecastPage(),
    );
  }

  /// 构建左侧抽屉栏内容
  Widget _buildDrawer(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Drawer(
      backgroundColor: colorScheme.background,
      shape: const RoundedRectangleBorder(), // 移除圆角效果
      child: SafeArea(
        child: Column(
          children: [
            // 顶部：标题
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(FIcons.wallet, size: 24, color: colorScheme.primary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '财务管理',
                      style: theme.typography.lg.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.foreground,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 中间：功能列表
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 账户管理分组
                    Text(
                      '账户管理',
                      style: theme.typography.sm.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(height: 8),
                    FItemGroup(
                      children: [
                        FItem(
                          prefix: const Icon(FIcons.wallet),
                          title: const Text('现金来源'),
                          suffix: const Icon(FIcons.chevronRight),
                          onPress: () {
                            Navigator.of(context).pop(); // 关闭抽屉栏
                            _showCashSourcesPage(context);
                          },
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 财务设置分组
                    Text(
                      '财务设置',
                      style: theme.typography.sm.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(height: 8),
                    FItemGroup(
                      children: [
                        FItem(
                          prefix: const Icon(FIcons.trendingUp),
                          title: const Text('核心收入设置'),
                          suffix: const Icon(FIcons.chevronRight),
                          onPress: () {
                            Navigator.of(context).pop(); // 关闭抽屉栏
                            _showRecurringIncomeSettings(context);
                          },
                        ),
                        FItem(
                          prefix: const Icon(FIcons.trendingDown),
                          title: const Text('核心支出设置'),
                          suffix: const Icon(FIcons.chevronRight),
                          onPress: () {
                            Navigator.of(context).pop(); // 关闭抽屉栏
                            _showRecurringExpenseSettings(context);
                          },
                        ),
                        FItem(
                          prefix: const Icon(FIcons.shield),
                          title: const Text('财务安全线'),
                          suffix: const Icon(FIcons.chevronRight),
                          onPress: () {
                            Navigator.of(context).pop(); // 关闭抽屉栏
                            _showSafetyThresholdSettings(context);
                          },
                        ),
                        FItem(
                          prefix: const Icon(FIcons.calculator),
                          title: const Text('日常消费预估'),
                          suffix: const Icon(FIcons.chevronRight),
                          onPress: () {
                            Navigator.of(context).pop(); // 关闭抽屉栏
                            _showDailySpendingSettings(context);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // 底部：用户信息（可选）
            Container(
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: colorScheme.border)),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // 可以导航到个人资料页或其他功能
                    Navigator.of(context).pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            FIcons.user,
                            size: 16,
                            color: colorScheme.primaryForeground,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '财务助手',
                                style: theme.typography.sm.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '管理您的财务设置',
                                style: theme.typography.xs.copyWith(
                                  color: colorScheme.mutedForeground,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          FIcons.chevronRight,
                          size: 16,
                          color: colorScheme.mutedForeground,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示现金来源页面
  void _showCashSourcesPage(BuildContext context) {
    showFSheet(
      context: context,
      side: FLayout.btt,
      mainAxisMaxRatio: 0.9,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: context.theme.colors.background,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            // 顶部拖拽指示器和标题
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: context.theme.colors.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Row(
                children: [
                  Icon(FIcons.wallet, color: context.theme.colors.primary),
                  const SizedBox(width: 8),
                  Text(
                    '现金来源管理',
                    style: context.theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  FButton(
                    style: FButtonStyle.ghost(),
                    onPress: () => Navigator.of(context).pop(),
                    child: const Icon(FIcons.x),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // 现金来源页面内容
            const Expanded(child: CashSourcesPage()),
          ],
        ),
      ),
    );
  }

  /// 显示核心收入设置
  void _showRecurringIncomeSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _RecurringTransactionBottomSheet(
          ref: ref,
          type: RecurringEventType.income,
          title: '核心收入设置',
          subtitle: '管理您的固定收入来源',
        );
      },
    );
  }

  /// 显示核心支出设置
  void _showRecurringExpenseSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _RecurringTransactionBottomSheet(
          ref: ref,
          type: RecurringEventType.expense,
          title: '核心支出设置',
          subtitle: '管理您的固定支出项目',
        );
      },
    );
  }

  /// 显示财务安全线设置
  void _showSafetyThresholdSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _SafetyThresholdBottomSheet(ref: ref);
      },
    );
  }

  /// 显示日常消费预估设置
  void _showDailySpendingSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _DailySpendingBottomSheet(ref: ref);
      },
    );
  }

  void _showWarningsDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const ForecastWarningsDialog(),
    );
  }
}

// 以下是从 FinancialSafetyLinePage 移植过来的底部弹出框组件

enum RecurringEventType { income, expense }

class _RecurringTransactionBottomSheet extends StatelessWidget {
  final WidgetRef ref;
  final RecurringEventType type;
  final String title;
  final String subtitle;

  const _RecurringTransactionBottomSheet({
    required this.ref,
    required this.type,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colorScheme.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    title,
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    subtitle,
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 表单内容区域
            Flexible(
              child: Container(
                width: double.maxFinite,
                constraints: const BoxConstraints(maxHeight: 500),
                child: _RecurringTransactionFormBottomSheet(
                  isIncome: type == RecurringEventType.income,
                  onSubmit:
                      ({
                        required double amount,
                        String? categoryId,
                        String? description,
                        required String recurrenceRule,
                        required DateTime startDate,
                        DateTime? endDate,
                      }) async {
                        try {
                          // 这里需要实现保存逻辑，暂时使用简单的成功提示
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${type == RecurringEventType.income ? '收入' : '支出'}规则已保存',
                                ),
                              ),
                            );
                          }
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(
                              context,
                            ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
                          }
                        }
                      },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SafetyThresholdBottomSheet extends StatefulWidget {
  final WidgetRef ref;

  const _SafetyThresholdBottomSheet({required this.ref});

  @override
  State<_SafetyThresholdBottomSheet> createState() =>
      _SafetyThresholdBottomSheetState();
}

class _SafetyThresholdBottomSheetState
    extends State<_SafetyThresholdBottomSheet> {
  double _currentValue = 1000.0;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colorScheme.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '财务安全线设置',
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '设置您的财务安全阈值',
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 内容区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '¥${_currentValue.toStringAsFixed(0)}',
                    style: theme.typography.xl2.copyWith(
                      color: theme.colors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  Slider(
                    value: _currentValue,
                    max: 10000.0,
                    onChanged: _isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _currentValue = value;
                            });
                          },
                  ),

                  const SizedBox(height: 32),

                  // 按钮区域
                  Row(
                    children: [
                      Expanded(
                        child: FButton(
                          style: FButtonStyle.outline(),
                          onPress: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FButton(
                          onPress: _isLoading ? null : _handleSave,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text('保存'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    setState(() => _isLoading = true);

    try {
      // 暂时使用简单的成功提示，实际实现需要调用相应的API
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络请求

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('财务安全线已保存')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

class _DailySpendingBottomSheet extends StatefulWidget {
  final WidgetRef ref;

  const _DailySpendingBottomSheet({required this.ref});

  @override
  State<_DailySpendingBottomSheet> createState() =>
      _DailySpendingBottomSheetState();
}

class _DailySpendingBottomSheetState extends State<_DailySpendingBottomSheet> {
  double _currentValue = 150.0;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colors.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '日常消费预估',
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '设置您的日常消费预估金额',
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // 内容区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '¥${_currentValue.toStringAsFixed(0)} / 天',
                    style: theme.typography.xl2.copyWith(
                      color: theme.colors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 24),

                  Slider(
                    value: _currentValue,
                    max: 1000.0,
                    onChanged: _isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _currentValue = value;
                            });
                          },
                  ),

                  const SizedBox(height: 32),

                  // 按钮区域
                  Row(
                    children: [
                      Expanded(
                        child: FButton(
                          style: FButtonStyle.outline(),
                          onPress: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FButton(
                          onPress: _isLoading ? null : _handleSave,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text('保存'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    setState(() => _isLoading = true);

    try {
      // 暂时使用简单的成功提示，实际实现需要调用相应的API
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络请求

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('日常消费预估已保存')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// 专门用于底部弹出框的循环交易表单组件
class _RecurringTransactionFormBottomSheet extends StatefulWidget {
  final bool isIncome;
  final Function({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  })
  onSubmit;

  const _RecurringTransactionFormBottomSheet({
    required this.isIncome,
    required this.onSubmit,
  });

  @override
  State<_RecurringTransactionFormBottomSheet> createState() =>
      _RecurringTransactionFormBottomSheetState();
}

class _RecurringTransactionFormBottomSheetState
    extends State<_RecurringTransactionFormBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  int _dayOfMonth = 1;
  final DateTime _startDate = DateTime.now();

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 金额输入
            Text(
              '金额',
              style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(hintText: '请输入金额'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return '请输入金额';
                }
                if (double.tryParse(value!.trim()) == null) {
                  return '请输入有效的数字';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // 描述输入
            Text(
              '描述',
              style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: widget.isIncome ? '如：工资、奖金等' : '如：房租、水电费等',
              ),
            ),

            const SizedBox(height: 16),

            // 日期选择
            Text(
              '每月日期',
              style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Text(
              '第 $_dayOfMonth 天',
              style: theme.typography.base.copyWith(
                color: theme.colors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _dayOfMonth.toDouble(),
              min: 1,
              max: 28,
              onChanged: (value) {
                setState(() {
                  _dayOfMonth = value.round();
                });
              },
            ),

            const SizedBox(height: 32),

            // 按钮区域
            Row(
              children: [
                Expanded(
                  child: FButton(
                    style: FButtonStyle.outline(),
                    onPress: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FButton(
                    onPress: _handleSubmit,
                    child: const Text('保存'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text.trim());
    final finalAmount = widget.isIncome ? amount : -amount;

    final recurrenceRule = 'FREQ=MONTHLY;BYMONTHDAY=$_dayOfMonth';

    widget.onSubmit(
      amount: finalAmount,
      categoryId: null,
      description: _descriptionController.text.trim(),
      recurrenceRule: recurrenceRule,
      startDate: _startDate,
    );
  }
}
