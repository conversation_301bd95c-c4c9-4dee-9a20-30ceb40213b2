// features/forecast/pages/forecast_page.dart
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import '../models/forecast_models.dart';
import '../providers/forecast_providers.dart';
import '../providers/onboarding_status_provider.dart';
import '../widgets/forecast_chart.dart';
import '../widgets/forecast_spotlight_card.dart';
import '/shared/l10n/app_strings.dart';


/// 未来财务预测页面
class ForecastPage extends ConsumerStatefulWidget {
  const ForecastPage({super.key});

  @override
  ConsumerState<ForecastPage> createState() => _ForecastPageState();
}

class _ForecastPageState extends ConsumerState<ForecastPage> {
  @override
  void initState() {
    super.initState();
    // 只在用户点击未来Tab时检查引导状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkOnboardingStatusOnTabClick();
    });
  }

  /// 当用户点击未来Tab时检查引导状态
  Future<void> _checkOnboardingStatusOnTabClick() async {
    final onboardingStatus = ref.read(onboardingStatusProvider);

    // 如果状态是未完成，直接跳转到引导页
    if (onboardingStatus == OnboardingStatus.incomplete) {
      log('本地状态显示未完成引导，跳转到引导页');
      if (mounted) {
        context.go('/forecast/onboarding');
      }
      return;
    }

    // 如果状态是未知，需要检查
    if (onboardingStatus == OnboardingStatus.unknown) {
      log('引导状态未知，开始检查...');
      final statusNotifier = ref.read(onboardingStatusProvider.notifier);
      final finalStatus = await statusNotifier.checkOnboardingStatus();

      if (finalStatus == OnboardingStatus.incomplete && mounted) {
        log('检查结果：需要引导，跳转到引导页');
        context.go('/forecast/onboarding');
      }
    }

    // 如果状态是已完成，什么都不做，继续显示主页面
    log('引导状态：已完成，显示主页面');
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final forecastAsync = ref.watch(forecastProvider);

    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: forecastAsync.when(
          loading: () => _buildLoadingState(theme),
          error: (error, stack) => _buildErrorState(theme, error.toString()),
          data: (forecast) => _buildMainContent(theme, forecast),
        ),
      ),
    );
  }

  Widget _buildLoadingState(FThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            AppStrings.get('loadingFinancialForecast'),
            style: theme.typography.base.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(FThemeData theme, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FIcons.cloudOff,
              size: 64,
              color: theme.colors.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              '网络开小差了',
              style: theme.typography.xl.copyWith(
                color: theme.colors.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '无法连接到未来...',
              style: theme.typography.base.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FButton(
              onPress: () {
                ref.invalidate(forecastProvider);
              },
              child: const Text('点击重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent(FThemeData theme, FinancialForecast forecast) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 工具栏
          //const ForecastToolbar(),

          // 图表区域 - 使用合适的高度，让内容自然流动
          Container(
            height: 400, // 保持合适的图表高度
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              child: const ForecastChart(),
            ),
          ),

          // 动态焦点卡片区域 - 让内容自然展开
          Container(
            margin: const EdgeInsets.all(16),
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: ForecastSpotlightCard(
                forecast: forecast,
              ),
            ),
          ),

          // 底部留白，确保滑动体验良好
          const SizedBox(height: 20),
        ],
      ),
    );
  }

}
