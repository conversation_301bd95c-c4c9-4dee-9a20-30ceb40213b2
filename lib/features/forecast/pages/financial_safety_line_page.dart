// features/forecast/pages/financial_safety_line_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:decimal/decimal.dart';

import '../../profile/providers/financial_settings_provider.dart';
import '../../profile/models/financial_settings.dart';
import '../../profile/widgets/safety_line_preview_chart.dart';

import '../models/forecast_models.dart';
import '../providers/onboarding_status_provider.dart';
import '/shared/l10n/app_strings.dart';

/// 财务设置页面 - 整合所有财务相关设置
class FinancialSafetyLinePage extends ConsumerStatefulWidget {
  const FinancialSafetyLinePage({super.key});

  @override
  ConsumerState<FinancialSafetyLinePage> createState() => _FinancialSafetyLinePageState();
}

class _FinancialSafetyLinePageState extends ConsumerState<FinancialSafetyLinePage> {
  static const double minAmount = 0;
  static const double maxAmount = 10000;
  static const double defaultAmount = 1500;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final settingsState = ref.watch(financialSettingsProvider);

    return Container(
      color: colorScheme.background,
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 页面标题
              //_buildPageHeader(theme, colorScheme),

              //const SizedBox(height: 24),

              // 设置卡片列表
              _buildSettingsCards(theme, colorScheme, settingsState),

              const SizedBox(height: 24),

              // 重新引导按钮 - 暂时注释掉，因为财务设置页面已经包含了所有可以重新设置的功能
              // _buildOnboardingButton(theme, colorScheme),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建页面标题
  Widget _buildPageHeader(FThemeData theme, FColors colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '财务设置',
          style: theme.typography.xl2.copyWith(
            color: colorScheme.foreground,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '管理您的财务预测参数和安全设置',
          style: theme.typography.base.copyWith(
            color: colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  /// 构建设置卡片列表
  Widget _buildSettingsCards(FThemeData theme, FColors colorScheme, FinancialSettingsState state) {
    return Column(
      children: [
        //const SizedBox(height: 16),
        _buildSettingCard(
          theme,
          colorScheme,
          icon: FIcons.trendingUp,
          title: '核心收入设置',
          subtitle: '管理您的固定收入来源',
          onTap: () => _showRecurringIncomeSettings(context, ref),
        ),

        const SizedBox(height: 16),

        _buildSettingCard(
          theme,
          colorScheme,
          icon: FIcons.trendingDown,
          title: '核心支出设置',
          subtitle: '管理您的固定支出项目',
          onTap: () => _showRecurringExpenseSettings(context, ref),
        ),

        const SizedBox(height: 16),

        _buildSettingCard(
          theme,
          colorScheme,
          icon: FIcons.shield,
          title: AppStrings.get('financialSafetyLine'),
          subtitle: '${AppStrings.get('currentSetting')}: ¥${state.effectiveSafetyBalanceThreshold.toStringAsFixed(0)}',
          onTap: () => _showSafetyThresholdSettings(context, ref),
        ),

        const SizedBox(height: 16),

        _buildSettingCard(
          theme,
          colorScheme,
          icon: FIcons.calculator,
          title: AppStrings.get('dailySpendingEstimate'),
          subtitle: AppStrings.get('adjustDailySpendingAmount'),
          onTap: () => _showDailySpendingSettings(context, ref),
        ),
      ],
    );
  }

  /// 构建单个设置卡片
  Widget _buildSettingCard(
    FThemeData theme,
    FColors colorScheme, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.foreground,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: theme.typography.sm.copyWith(
                        color: colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                FIcons.chevronRight,
                color: colorScheme.mutedForeground,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建重新引导按钮
  Widget _buildOnboardingButton(FThemeData theme, FColors colorScheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '重新设置',
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '如果您的财务状况发生了变化，可以重新进行引导设置',
              style: theme.typography.sm.copyWith(
                color: colorScheme.mutedForeground,
              ),
            ),
            const SizedBox(height: 16),
            FButton(
              style: FButtonStyle.outline(),
              onPress: () async {
                // 重置引导状态
                await ref.read(onboardingStatusProvider.notifier).reset();
                // 跳转到引导页面
                if (mounted) {
                  context.go('/forecast/onboarding');
                }
              },
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(FIcons.refreshCw, size: 16),
                  SizedBox(width: 8),
                  Text('重新设置引导'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 以下方法从 ForecastSettingsDialog 复制过来，改为底部弹出框
  void _showRecurringIncomeSettings(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _RecurringTransactionBottomSheet(
          ref: ref,
          type: RecurringEventType.income,
          title: '核心收入设置',
          subtitle: '管理您的固定收入来源',
        );
      },
    );
  }

  void _showRecurringExpenseSettings(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _RecurringTransactionBottomSheet(
          ref: ref,
          type: RecurringEventType.expense,
          title: '核心支出设置',
          subtitle: '管理您的固定支出项目',
        );
      },
    );
  }

  void _showSafetyThresholdSettings(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _SafetyThresholdBottomSheet(ref: ref);
      },
    );
  }

  void _showDailySpendingSettings(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        return _DailySpendingBottomSheet(ref: ref);
      },
    );
  }


}



// 以下是从 ForecastSettingsDialog 移植过来的对话框组件

class _RecurringTransactionBottomSheet extends StatelessWidget {
  final WidgetRef ref;
  final RecurringEventType type;
  final String title;
  final String subtitle;

  const _RecurringTransactionBottomSheet({
    required this.ref,
    required this.type,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colorScheme.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    title,
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    subtitle,
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 表单内容区域
            Flexible(
              child: Container(
                width: double.maxFinite,
                constraints: const BoxConstraints(maxHeight: 500),
                child: _RecurringTransactionFormBottomSheet(
                  isIncome: type == RecurringEventType.income,
                  onSubmit: ({
                    required double amount,
                    String? categoryId,
                    String? description,
                    required String recurrenceRule,
                    required DateTime startDate,
                    DateTime? endDate,
                  }) async {
                    try {
                      // 这里需要实现保存逻辑，暂时使用简单的成功提示
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('${type == RecurringEventType.income ? '收入' : '支出'}规则已保存')),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('保存失败: $e')),
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SafetyThresholdBottomSheet extends StatefulWidget {
  final WidgetRef ref;

  const _SafetyThresholdBottomSheet({required this.ref});

  @override
  State<_SafetyThresholdBottomSheet> createState() => _SafetyThresholdBottomSheetState();
}

class _SafetyThresholdBottomSheetState extends State<_SafetyThresholdBottomSheet> {
  double _currentValue = 1000.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final settingsState = widget.ref.read(financialSettingsProvider);
    _currentValue = settingsState.effectiveSafetyBalanceThreshold.toDouble();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colorScheme.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '财务安全线设置',
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '设置您的财务安全阈值',
                    style: theme.typography.sm.copyWith(
                      color: colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 内容区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Container(
                    height: 200,
                    padding: const EdgeInsets.all(16),
                    child: SafetyLinePreviewChart(
                      safetyLineValue: _currentValue,
                    ),
                  ),

                  const SizedBox(height: 16),

                  Text(
                    '¥${_currentValue.toStringAsFixed(0)}',
                    style: theme.typography.xl2.copyWith(
                      color: theme.colors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  Slider(
                    value: _currentValue,
                    max: 10000.0,
                    onChanged: _isLoading ? null : (value) {
                      setState(() {
                        _currentValue = value;
                      });
                    },
                  ),

                  const SizedBox(height: 32),

                  // 按钮区域
                  Row(
                    children: [
                      Expanded(
                        child: FButton(
                          style: FButtonStyle.outline(),
                          onPress: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FButton(
                          onPress: _isLoading ? null : _handleSave,
                          child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('保存'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    setState(() => _isLoading = true);

    try {
      widget.ref.read(financialSettingsProvider.notifier)
          .updateSafetyThreshold(Decimal.fromInt(_currentValue.round()));

      final success = await widget.ref.read(financialSettingsProvider.notifier)
          .saveFinancialSettings();

      if (!mounted) return;

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('财务安全线已保存')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('保存失败')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

class _DailySpendingBottomSheet extends StatefulWidget {
  final WidgetRef ref;

  const _DailySpendingBottomSheet({required this.ref});

  @override
  State<_DailySpendingBottomSheet> createState() => _DailySpendingBottomSheetState();
}

class _DailySpendingBottomSheetState extends State<_DailySpendingBottomSheet> {
  double _currentValue = 150.0;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12.0, bottom: 16.0),
              decoration: BoxDecoration(
                color: colors.border.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '日常消费预估',
                    style: theme.typography.lg.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '设置您的日常消费预估金额',
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // 内容区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  Text(
                    '¥${_currentValue.toStringAsFixed(0)} / 天',
                    style: theme.typography.xl2.copyWith(
                      color: theme.colors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 24),

                  Slider(
                    value: _currentValue,
                    max: 1000.0,
                    onChanged: _isLoading ? null : (value) {
                      setState(() {
                        _currentValue = value;
                      });
                    },
                  ),

                  const SizedBox(height: 32),

                  // 按钮区域
                  Row(
                    children: [
                      Expanded(
                        child: FButton(
                          style: FButtonStyle.outline(),
                          onPress: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FButton(
                          onPress: _isLoading ? null : _handleSave,
                          child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('保存'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSave() async {
    setState(() => _isLoading = true);

    try {
      // 暂时使用简单的成功提示，实际实现需要调用相应的API
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络请求

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('日常消费预估已保存')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// 专门用于底部弹出框的循环交易表单组件
class _RecurringTransactionFormBottomSheet extends StatefulWidget {
  final bool isIncome;
  final Function({
    required double amount,
    String? categoryId,
    String? description,
    required String recurrenceRule,
    required DateTime startDate,
    DateTime? endDate,
  }) onSubmit;

  const _RecurringTransactionFormBottomSheet({
    required this.isIncome,
    required this.onSubmit,
  });

  @override
  State<_RecurringTransactionFormBottomSheet> createState() => _RecurringTransactionFormBottomSheetState();
}

class _RecurringTransactionFormBottomSheetState extends State<_RecurringTransactionFormBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  TransactionCategory? _selectedCategory;
  int _dayOfMonth = 1;
  final DateTime _startDate = DateTime.now();

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 金额输入
            Text(
              '金额',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                hintText: '请输入金额',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return '请输入金额';
                }
                if (double.tryParse(value!.trim()) == null) {
                  return '请输入有效的数字';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // 描述输入
            Text(
              '描述',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: widget.isIncome ? '如：工资、奖金等' : '如：房租、水电费等',
              ),
            ),

            const SizedBox(height: 16),

            // 分类选择（仅支出需要）
            if (!widget.isIncome) ...[
              Text(
                '分类',
                style: theme.typography.sm.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<TransactionCategory>(
                decoration: const InputDecoration(
                  hintText: '选择分类',
                ),
                items: TransactionCategory.values
                    .where((cat) => cat != TransactionCategory.income)
                    .map((category) => DropdownMenuItem(
                          value: category,
                          child: Text(category.displayName),
                        ))
                    .toList(),
                value: _selectedCategory,
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                },
              ),
              const SizedBox(height: 16),
            ],

            // 日期选择
            Text(
              '每月日期',
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '第 $_dayOfMonth 天',
              style: theme.typography.base.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _dayOfMonth.toDouble(),
              min: 1,
              max: 28,
              onChanged: (value) {
                setState(() {
                  _dayOfMonth = value.round();
                });
              },
            ),

            const SizedBox(height: 32),

            // 按钮区域
            Row(
              children: [
                Expanded(
                  child: FButton(
                    style: FButtonStyle.outline(),
                    onPress: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FButton(
                    onPress: _handleSubmit,
                    child: const Text('保存'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!widget.isIncome && _selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择分类')),
      );
      return;
    }

    final amount = double.parse(_amountController.text.trim());
    final finalAmount = widget.isIncome ? amount : -amount;

    final recurrenceRule = 'FREQ=MONTHLY;BYMONTHDAY=$_dayOfMonth';

    widget.onSubmit(
      amount: finalAmount,
      categoryId: widget.isIncome ? TransactionCategory.income.id : _selectedCategory?.id,
      description: _descriptionController.text.trim(),
      recurrenceRule: recurrenceRule,
      startDate: _startDate,
    );
  }
}
