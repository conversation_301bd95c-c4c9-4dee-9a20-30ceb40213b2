import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';

import 'package:tabler_icons_next/tabler_icons_next.dart' as tabler;
import '../../profile/models/cash_pocket.dart';
import '../../profile/providers/cash_pocket_provider.dart';
import '../../profile/widgets/cash_source_selection_sheet.dart';

/// 现金来源管理页面
class CashSourcesPage extends ConsumerStatefulWidget {
  const CashSourcesPage({super.key});

  @override
  ConsumerState<CashSourcesPage> createState() => _CashSourcesPageState();
}

class _CashSourcesPageState extends ConsumerState<CashSourcesPage> {
  List<CashSource> _cashSources = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    await ref.read(cashSourceProvider.notifier).loadCashSources();
    _initializeCashSources();
  }

  void _initializeCashSources() {
    final cashSourceState = ref.read(cashSourceProvider);
    _cashSources = List.from(cashSourceState.sources);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final cashSourceState = ref.watch(cashSourceProvider);

    return Container(
      color: colorScheme.background,
      child: SafeArea(
        child: cashSourceState.isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 页面标题
                  //_buildPageHeader(theme, colorScheme),

                  //const SizedBox(height: 24),

                  // 主要内容区域
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // const SizedBox(height: 16),

                          // 总余额显示 - 参考首页额度数字显示设计
                          if (_cashSources.isNotEmpty) ...[
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    colorScheme.primary,
                                    colorScheme.primary.withValues(alpha: 0.8),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: colorScheme.primary.withValues(alpha: 0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        FIcons.wallet,
                                        color: colorScheme.primaryForeground.withValues(alpha: 0.8),
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '总余额',
                                        style: theme.typography.sm.copyWith(
                                          color: colorScheme.primaryForeground.withValues(alpha: 0.8),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    _formatCurrency(_calculateTotal()),
                                    style: theme.typography.xl4.copyWith(
                                      color: colorScheme.primaryForeground,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 32,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],

                          // 现金来源列表
                          if (_cashSources.isNotEmpty) ...[
                            Text('现金来源', style: theme.typography.lg),
                            const SizedBox(height: 12),
                            ..._cashSources.asMap().entries.map((entry) {
                              final index = entry.key;
                              final source = entry.value;
                              return Padding(padding: const EdgeInsets.only(bottom: 8.0), child: _buildSourceCard(source, index));
                            }),
                          ] else ...[
                            // 空状态
                            Center(
                              child: Column(
                                children: [
                                  const SizedBox(height: 60),
                                  SizedBox(width: 64, height: 64, child: tabler.WalletOff(color: colorScheme.mutedForeground)),
                                  const SizedBox(height: 16),
                                  Text('还没有添加现金来源', style: theme.typography.lg.copyWith(color: colorScheme.mutedForeground)),
                                  const SizedBox(height: 8),
                                  Text('点击下方按钮添加您的第一个现金来源', style: theme.typography.sm.copyWith(color: colorScheme.mutedForeground)),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),

                  // 底部按钮区域
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: colorScheme.background,
                      border: Border(
                        top: BorderSide(
                          color: colorScheme.border,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        // 添加按钮
                        Expanded(
                          child: FButton(
                            mainAxisSize: MainAxisSize.min,
                            onPress: _showAddSourceSheet,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  FIcons.plus,
                                  color: colorScheme.primaryForeground,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text('添加现金来源'),
                              ],
                            ),
                          ),
                        ),

                        // 保存按钮 - 与添加按钮在同一水平线
                        if (_cashSources.isNotEmpty) ...[
                          const SizedBox(width: 12),
                          FButton(
                            style: FButtonStyle.outline(),
                            mainAxisSize: MainAxisSize.min,
                            onPress: _saveSources,
                            child: const Text('保存更改'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSourceCard(CashSource source, int index) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Dismissible(
      key: Key('source_$index'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(color: colorScheme.destructive, borderRadius: BorderRadius.circular(8)),
        child: SizedBox(width: 20, height: 20, child: tabler.Trash(color: colorScheme.destructiveForeground)),
      ),
      confirmDismiss: (direction) => _showDeleteConfirmation(source),
      onDismissed: (direction) => _removeSource(index),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // 图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(color: _getSourceColor(source).withValues(alpha: 0.1), borderRadius: BorderRadius.circular(20)),
                child: SizedBox(width: 20, height: 20, child: _getSourceIcon(source)),
              ),

              const SizedBox(width: 12),

              // 名称和余额
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(_getSourceDisplayName(source), style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500)),
                    const SizedBox(height: 4),
                    Text(_formatCurrency(source.balance.toDouble()), style: theme.typography.lg.copyWith(color: colorScheme.primary)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddSourceSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) => CashSourceSelectionSheet(onSourceAdded: _addSource),
    );
  }

  void _addSource(CashSource source) {
    setState(() {
      _cashSources.add(source);
    });
  }

  void _removeSource(int index) {
    setState(() {
      _cashSources.removeAt(index);
    });
  }

  Future<bool?> _showDeleteConfirmation(CashSource source) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Padding(padding: const EdgeInsets.only(bottom: 8), child: Text('确定要删除"${_getSourceDisplayName(source)}"吗？此操作无法撤销。')),
        actions: [
          FButton(style: FButtonStyle.outline(), onPress: () => Navigator.of(context).pop(false), child: const Text('取消')),
          FButton(style: FButtonStyle.destructive(), onPress: () => Navigator.of(context).pop(true), child: const Text('删除')),
        ],
      ),
    );
  }

  Future<void> _saveSources() async {
    if (_cashSources.isEmpty) {
      _showErrorSnackBar('请至少添加一个现金来源');
      return;
    }

    final success = await ref.read(cashSourceProvider.notifier).saveCashSources(_cashSources);

    if (success && mounted) {
      _showSuccessSnackBar('现金来源已更新');
      context.pop();
    }
  }

  double _calculateTotal() {
    return _cashSources.fold(0.0, (sum, source) => sum + source.balance.toDouble());
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(locale: 'zh_CN', symbol: '¥ ', decimalDigits: 2);
    return formatter.format(amount);
  }

  String _getSourceDisplayName(CashSource source) {
    if (source.sourceType == CashSourceType.preset) {
      return _getPresetDisplayName(source.presetKey ?? '');
    } else {
      return source.customName ?? '未知来源';
    }
  }

  String _getPresetDisplayName(String presetKey) {
    switch (presetKey) {
      case 'wechat':
        return '微信';
      case 'alipay':
        return '支付宝';
      case 'cash':
        return '现金';
      case 'paypal':
        return 'PayPal';
      case 'apple':
        return '苹果';
      case 'google':
        return '谷歌';
      default:
        return presetKey;
    }
  }

  Widget _getSourceIcon(CashSource source) {
    final color = _getSourceColor(source);

    if (source.sourceType == CashSourceType.preset) {
      switch (source.presetKey) {
        case 'wechat':
          return tabler.BrandWechat(color: color);
        case 'alipay':
          return tabler.BrandAlipay(color: color);
        case 'cash':
          return tabler.Cash(color: color);
        case 'paypal':
          return tabler.BrandPaypal(color: color);
        case 'apple':
          return tabler.BrandApple(color: color);
        case 'google':
          return tabler.BrandGoogle(color: color);
        default:
          return tabler.Wallet(color: color);
      }
    } else {
      return tabler.Wallet(color: color);
    }
  }

  Color _getSourceColor(CashSource source) {
    if (source.sourceType == CashSourceType.preset) {
      switch (source.presetKey) {
        case 'wechat':
          return const Color(0xFF07C160);
        case 'alipay':
          return const Color(0xFF1677FF);
        case 'cash':
          return const Color(0xFF52C41A);
        case 'paypal':
          return const Color(0xFF0070BA);
        case 'apple':
          return const Color(0xFF000000);
        case 'google':
          return const Color(0xFF4285F4);
        default:
          return const Color(0xFF6B7280);
      }
    } else {
      return const Color(0xFF6B7280);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: context.theme.colors.primary));
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: context.theme.colors.destructive));
  }

  /// 构建页面标题
  Widget _buildPageHeader(FThemeData theme, FColors colorScheme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '管理您的现金来源，包括微信、支付宝、银行卡等',
            style: theme.typography.base.copyWith(
              color: colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }
}
