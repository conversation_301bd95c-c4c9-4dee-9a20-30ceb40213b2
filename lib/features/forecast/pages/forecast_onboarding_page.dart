// features/forecast/pages/forecast_onboarding_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:animate_do/animate_do.dart';
import 'package:go_router/go_router.dart';
import '../models/forecast_models.dart';
import '../providers/onboarding_provider.dart';
import '../providers/onboarding_status_provider.dart';
import '../widgets/onboarding_welcome_step.dart';
import '../widgets/onboarding_balance_step.dart';
import '../widgets/onboarding_events_step.dart';
import '../widgets/onboarding_spending_step.dart';
import '../widgets/onboarding_safety_step.dart';
import '../widgets/onboarding_generating_step.dart';

/// 财务预测引导页面
class ForecastOnboardingPage extends ConsumerStatefulWidget {
  const ForecastOnboardingPage({super.key});

  @override
  ConsumerState<ForecastOnboardingPage> createState() => _ForecastOnboardingPageState();
}

class _ForecastOnboardingPageState extends ConsumerState<ForecastOnboardingPage> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final onboardingState = ref.watch(onboardingProvider);

    // 监听页面变化
    ref.listen<OnboardingState>(onboardingProvider, (previous, current) {
      if (previous?.currentPageIndex != current.currentPageIndex) {
        _pageController.animateToPage(
          current.currentPageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }

      // 如果完成生成，标记为已完成并导航到主页面
      if (current.currentStep == OnboardingStep.generating &&
          !current.isLoading &&
          current.error == null) {
        // 标记引导流程为已完成
        ref.read(onboardingStatusProvider.notifier).markAsCompleted();
        context.go('/forecast');
      }
    });

    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // 进度指示器
            _buildProgressIndicator(theme, onboardingState),
            
            // 主要内容
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(), // 禁用手动滑动
                children: [
                  OnboardingWelcomeStep(
                    onNext: () => ref.read(onboardingProvider.notifier).nextStep(),
                  ),
                  OnboardingBalanceStep(
                    currentBalance: onboardingState.currentBalance,
                    onBalanceChanged: (balance) =>
                        ref.read(onboardingProvider.notifier).setCurrentBalance(balance),
                    onNext: () async {
                      if (onboardingState.currentBalance != null) {
                        final success = await ref.read(onboardingProvider.notifier).setCurrentBalance(onboardingState.currentBalance!);
                        if (success) {
                          ref.read(onboardingProvider.notifier).nextStep();
                        }
                      }
                    },
                    onBack: () => ref.read(onboardingProvider.notifier).previousStep(),
                    canProceed: ref.read(onboardingProvider.notifier).canProceed(),
                  ),
                  OnboardingEventsStep(
                    recurringEvents: onboardingState.recurringEvents,
                    isLoading: onboardingState.isLoading,
                    error: onboardingState.error,
                    onAddTransaction: ({
                      required double amount,
                      String? categoryId,
                      String? description,
                      required String recurrenceRule,
                      required DateTime startDate,
                      DateTime? endDate,
                    }) async {
                      await ref.read(onboardingProvider.notifier).addRecurringTransaction(
                        amount: amount,
                        categoryId: categoryId,
                        description: description,
                        recurrenceRule: recurrenceRule,
                        startDate: startDate,
                        endDate: endDate,
                      );
                    },
                    onRemoveEvent: (index) =>
                        ref.read(onboardingProvider.notifier).removeRecurringEvent(index),
                    onNext: () => ref.read(onboardingProvider.notifier).nextStep(),
                    onBack: () => ref.read(onboardingProvider.notifier).previousStep(),
                    canProceed: ref.read(onboardingProvider.notifier).canProceed(),
                    onClearError: () => ref.read(onboardingProvider.notifier).clearError(),
                  ),
                  OnboardingSpendingStep(
                    estimatedDailySpending: onboardingState.estimatedDailySpending,
                    onSpendingChanged: (amount) =>
                        ref.read(onboardingProvider.notifier).setEstimatedDailySpending(amount),
                    onNext: () => ref.read(onboardingProvider.notifier).nextStep(),
                    onBack: () => ref.read(onboardingProvider.notifier).previousStep(),
                  ),
                  OnboardingSafetyStep(
                    safetyThreshold: onboardingState.safetyThreshold,
                    onThresholdChanged: (threshold) =>
                        ref.read(onboardingProvider.notifier).setSafetyThreshold(threshold),
                    onNext: () async {
                      final success = await ref.read(onboardingProvider.notifier).completeOnboarding();
                      if (success) {
                        ref.read(onboardingProvider.notifier).nextStep();
                      }
                    },
                    onBack: () => ref.read(onboardingProvider.notifier).previousStep(),
                  ),
                  OnboardingGeneratingStep(
                    isLoading: onboardingState.isLoading,
                    error: onboardingState.error,
                    onRetry: () => ref.read(onboardingProvider.notifier).nextStep(),
                    onBack: () => ref.read(onboardingProvider.notifier).previousStep(),
                    onComplete: () {
                      // 标记引导流程为已完成并导航到主页面
                      ref.read(onboardingStatusProvider.notifier).markAsCompleted();
                      context.go('/forecast');
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(FThemeData theme, OnboardingState state) {
    const totalSteps = 5;
    final currentStep = state.currentPageIndex;

    return FadeInDown(
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: List.generate(totalSteps, (index) {
            final isActive = index <= currentStep;
            final isCompleted = index < currentStep;

            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < totalSteps - 1 ? 8 : 0,
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: 4,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? theme.colors.primary
                        : isActive
                            ? theme.colors.primary.withValues(alpha: 0.6)
                            : theme.colors.muted,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
