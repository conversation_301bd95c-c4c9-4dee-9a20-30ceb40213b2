// features/forecast/data/datasources/forecast_remote_datasource.dart
import 'package:decimal/decimal.dart';
import '../../../../core/network/network_client.dart';
import '../../../../core/network/exceptions/app_exception.dart';
import '../../domain/entities/forecast_entities.dart';
import '../../models/forecast_models.dart';

/// 远程数据源接口
abstract class ForecastRemoteDataSource {
  Future<FinancialForecast> getForecast(ForecastRequest request);
  Future<OnboardingStatusResponse> getOnboardingStatus();
  Future<void> saveUserSettings(UserSettingsRequest request);
  Future<RecurringTransactionResponse> createRecurringTransaction(
    CreateRecurringEventRequest request,
  );
}

/// 远程数据源实现
class ForecastRemoteDataSourceImpl implements ForecastRemoteDataSource {
  final NetworkClient _networkClient;

  ForecastRemoteDataSourceImpl(this._networkClient);

  @override
  Future<FinancialForecast> getForecast(ForecastRequest request) async {
    try {
      return await _networkClient.request<FinancialForecast>(
        '/transactions/forecast',
        method: HttpMethod.post,
        data: request.toJson(),
        fromJsonT: (json) {
          if (json is Map<String, dynamic>) {
            return FinancialForecast.fromJson(json);
          }
          throw const DataParsingException("预测数据格式错误");
        },
      );
    } catch (e) {
      if (e is NetworkException) {
        throw ForecastError(
          message: '网络请求失败',
          type: ForecastErrorType.network,
          details: e.toString(),
        );
      } else if (e is DataParsingException) {
        throw ForecastError(
          message: '数据解析失败',
          type: ForecastErrorType.calculation,
          details: e.toString(),
        );
      } else {
        throw ForecastError(
          message: '获取预测失败',
          type: ForecastErrorType.unknown,
          details: e.toString(),
        );
      }
    }
  }

  @override
  Future<OnboardingStatusResponse> getOnboardingStatus() async {
    try {
      return await _networkClient.request<OnboardingStatusResponse>(
        '/onboarding/status',
        method: HttpMethod.get,
        fromJsonT: (json) {
          if (json is Map<String, dynamic>) {
            return OnboardingStatusResponse.fromJson(json);
          }
          throw const DataParsingException("引导状态数据格式错误");
        },
      );
    } catch (e) {
      throw ForecastError(
        message: '获取引导状态失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }

  @override
  Future<void> saveUserSettings(UserSettingsRequest request) async {
    try {
      await _networkClient.request<void>(
        '/user/settings',
        method: HttpMethod.put,
        data: request.toJson(),
        fromJsonT: (json) => null,
      );
    } catch (e) {
      throw ForecastError(
        message: '保存用户设置失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }

  @override
  Future<RecurringTransactionResponse> createRecurringTransaction(
    CreateRecurringEventRequest request,
  ) async {
    try {
      return await _networkClient.request<RecurringTransactionResponse>(
        '/transactions/recurring',
        method: HttpMethod.post,
        data: request.toJson(),
        fromJsonT: (json) {
          if (json is Map<String, dynamic>) {
            return RecurringTransactionResponse.fromJson(json);
          }
          throw const DataParsingException("周期性交易数据格式错误");
        },
      );
    } catch (e) {
      throw ForecastError(
        message: '创建周期性交易失败',
        type: ForecastErrorType.network,
        details: e.toString(),
      );
    }
  }
}
