import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:tabler_icons_next/tabler_icons_next.dart' as tabler;
import '../../home/<USER>/manual_entry/manual_entry_fab.dart';

/// 底部导航页面 - 使用Forui设计系统
///
/// 采用FScaffold + FBottomNavigationBar的组合，符合Forui最佳实践
/// 保持原有的tabler icons使用方式，确保视觉一致性
class BottomPage extends StatelessWidget {
  final StatefulNavigationShell navigationShell;

  const BottomPage({super.key, required this.navigationShell});

  @override
  Widget build(BuildContext context) {
    // 使用Forui主题访问方式
    final colors = context.theme.colors;

    final keyboardOpen = MediaQuery.of(context).viewInsets.bottom > 0;

    return FScaffold(
      // 禁用默认的内容区域内边距，保持原有的全屏布局
      childPad: false,
      // 使用Forui的底部导航栏（键盘弹出时隐藏，避免与底部输入条冲突）
      footer: keyboardOpen ? null : FBottomNavigationBar(
        index: navigationShell.currentIndex,
        onChange: (index) => navigationShell.goBranch(index),
        children: [
          // 首页
          FBottomNavigationBarItem(
            icon: _buildTablerIcon(
              icon: tabler.Home(
                width: 30,
                height: 30,
                strokeWidth: 1.4,
                color: colors.foreground,
              ),
              activeIcon: tabler.HomeFilled(
                width: 30,
                height: 30,
                strokeWidth: 1.4,
                color: colors.foreground,
              ),
              isActive: navigationShell.currentIndex == 0,
            ),
            label: const Text(''),
          ),
          // 探索
          FBottomNavigationBarItem(
            icon: _buildTablerIcon(
              icon: tabler.Compass(
                width: 30,
                height: 30,
                strokeWidth: 1.4,
                color: colors.foreground,
              ),
              activeIcon: tabler.CompassFilled(
                width: 30,
                height: 30,
                strokeWidth: 1.4,
                color: colors.foreground,
              ),
              isActive: navigationShell.currentIndex == 1,
            ),
            label: const Text(''),
          ),
          // AI助手
          FBottomNavigationBarItem(
            icon: _buildTablerIcon(
              icon: tabler.MessageChatbot(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              activeIcon: tabler.MessageChatbotFilled(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              isActive: navigationShell.currentIndex == 2,
            ),
            label: const Text(''),
          ),
          // 统计
          FBottomNavigationBarItem(
            icon: _buildTablerIcon(
              icon: tabler.ChartPie3(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              activeIcon: tabler.ChartPie3Filled(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              isActive: navigationShell.currentIndex == 3,
            ),
            label: const Text(''),
          ),
          // 个人中心
          FBottomNavigationBarItem(
            icon: _buildTablerIcon(
              icon: tabler.User(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              activeIcon: tabler.UserFilled(
                strokeWidth: 1.4,
                width: 30,
                height: 30,
                color: colors.foreground,
              ),
              isActive: navigationShell.currentIndex == 4,
            ),
            label: const Text(''),
          ),
        ],
      ),
      // 主体内容区域
      child: Stack(
        children: [
          navigationShell,
          // 手动记账FAB - 只在首页显示
          if (navigationShell.currentIndex == 0)
            Positioned(
              right: 16,
              bottom: 16 + (keyboardOpen ? 0 : 80), // 键盘弹出时紧贴底部，否则考虑底部导航高度
              child: const ManualEntryFab(),
            ),
        ],
      ),
    );
  }

  /// 构建Tabler图标组件
  ///
  /// 保持原有的图标使用方式，根据激活状态显示不同的图标
  Widget _buildTablerIcon({
    required Widget icon,
    required Widget activeIcon,
    required bool isActive,
  }) {
    return isActive ? activeIcon : icon;
  }
}
