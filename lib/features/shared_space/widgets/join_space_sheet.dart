// features/shared_space/widgets/join_space_sheet.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../providers/shared_space_provider.dart';
import '../models/shared_space_models.dart';

class JoinSpaceSheet extends ConsumerStatefulWidget {
  final Function(SharedSpace) onSpaceJoined;
  final String? initialCode;

  const JoinSpaceSheet({super.key, required this.onSpaceJoined, this.initialCode});

  @override
  ConsumerState<JoinSpaceSheet> createState() => _JoinSpaceSheetState();
}

class _JoinSpaceSheetState extends ConsumerState<JoinSpaceSheet> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // 如果有初始邀请码，设置到输入框中
    if (widget.initialCode != null) {
      _codeController.text = widget.initialCode!.toUpperCase();
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('加入共享空间', style: theme.typography.xl.copyWith(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Text('输入朋友分享的邀请码加入空间', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 邀请码输入
                  TextFormField(
                    controller: _codeController,
                    decoration: InputDecoration(
                      hintText: '请输入邀请码，例如：A8K2F9G7',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.only(left: 12, right: 8),
                        child: Icon(FIcons.key, size: 16, color: colors.mutedForeground),
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'monospace', letterSpacing: 1),
                    textCapitalization: TextCapitalization.characters,
                    validator: (value) {
                      if (value!.trim().isEmpty) {
                        return '请输入邀请码';
                      }
                      final code = value.trim().toUpperCase();
                      if (code.length != 8) {
                        return '邀请码应为8位字符';
                      }
                      if (!RegExp(r'^[A-Z0-9]+$').hasMatch(code)) {
                        return '邀请码只能包含字母和数字';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      // 清除错误信息
                      if (_errorMessage != null) {
                        setState(() {
                          _errorMessage = null;
                        });
                      }
                      // 自动转换为大写
                      final upperValue = value.toUpperCase();
                      if (upperValue != value) {
                        _codeController.value = _codeController.value.copyWith(
                          text: upperValue,
                          selection: TextSelection.collapsed(offset: upperValue.length),
                        );
                      }
                    },
                  ),

                  // 错误信息显示
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: colors.destructive.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: colors.destructive.withValues(alpha: 0.3), width: 1),
                      ),
                      child: Row(
                        children: [
                          Icon(FIcons.circleAlert, size: 16, color: colors.destructive),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(_errorMessage!, style: theme.typography.sm.copyWith(color: colors.destructive)),
                          ),
                        ],
                      ),
                    ),
                  ],

                  const SizedBox(height: 24),

                  // 加入按钮
                  FButton(
                    onPress: _isLoading ? null : _joinSpace,
                    child: _isLoading
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox.square(dimension: 16, child: CircularProgressIndicator(strokeWidth: 2, color: colors.primaryForeground)),
                              const SizedBox(width: 8),
                              const Text('加入中...'),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [Icon(FIcons.logIn, size: 16), const SizedBox(width: 8), const Text('加入空间')],
                          ),
                  ),

                  const SizedBox(height: 12),

                  // 提示文本
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(color: colors.muted.withValues(alpha: 0.5), borderRadius: BorderRadius.circular(8)),
                    child: Row(
                      children: [
                        Icon(FIcons.info, size: 16, color: colors.mutedForeground),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text('邀请码通常在24小时内有效，请确认输入正确', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _joinSpace() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final space = await ref.read(sharedSpaceProvider.notifier).joinSpaceWithCode(_codeController.text.trim().toUpperCase());

      if (space != null) {
        widget.onSpaceJoined(space);
      } else {
        // 如果返回null，说明有错误，错误信息已经在provider中处理
        final error = ref.read(sharedSpaceProvider).error;
        if (error != null) {
          setState(() {
            _errorMessage = error;
          });
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
