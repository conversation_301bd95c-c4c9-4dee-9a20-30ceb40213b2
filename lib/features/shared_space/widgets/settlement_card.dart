// features/shared_space/widgets/settlement_card.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../models/shared_space_models.dart';

class SettlementCard extends StatelessWidget {
  final Settlement settlement;

  const SettlementCard({
    super.key,
    required this.settlement,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: settlement.isSettled 
                        ? colors.primary.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    settlement.isSettled ? FIcons.circleCheck : FIcons.calculator,
                    size: 20,
                    color: settlement.isSettled
                        ? colors.primary
                        : Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '结算中心',
                        style: theme.typography.xl.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        settlement.isSettled ? '所有账目已结清！' : '待结算',
                        style: theme.typography.sm.copyWith(
                          color: settlement.isSettled 
                              ? colors.primary
                              : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (settlement.isSettled)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: colors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          FIcons.circleCheck,
                          size: 14,
                          color: colors.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '已结清',
                          style: theme.typography.sm.copyWith(
                            color: colors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),

            if (settlement.items.isNotEmpty) ...[
              const SizedBox(height: 16),
              
              // 结算项目列表
              ...settlement.items.map((item) => _buildSettlementItem(context, item)),
            ] else if (settlement.isSettled) ...[
              const SizedBox(height: 16),
              
              // 已结清状态
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colors.primary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      FIcons.smile,
                      size: 24,
                      color: colors.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '太棒了！所有账目都已经结清了',
                        style: theme.typography.base.copyWith(
                          color: colors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 12),

            // 计算时间
            Row(
              children: [
                Icon(
                  FIcons.clock,
                  size: 14,
                  color: colors.mutedForeground,
                ),
                const SizedBox(width: 4),
                Text(
                  '计算于 ${_formatDateTime(settlement.calculatedAt)}',
                  style: theme.typography.sm.copyWith(
                    color: colors.mutedForeground,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettlementItem(BuildContext context, SettlementItem item) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colors.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.border,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 付款人头像
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: colors.primary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              FIcons.user,
              size: 16,
              color: colors.primaryForeground,
            ),
          ),
          const SizedBox(width: 12),

          // 结算信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: theme.typography.base.copyWith(
                      color: colors.foreground,
                    ),
                    children: [
                      TextSpan(
                        text: item.fromUsername,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const TextSpan(text: ' 应付给 '),
                      TextSpan(
                        text: item.toUsername,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '¥${item.amount.toStringAsFixed(2)}',
                  style: theme.typography.xl.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // 箭头
          Icon(
            FIcons.arrowRight,
            size: 16,
            color: colors.mutedForeground,
          ),
          const SizedBox(width: 8),

          // 收款人头像
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: colors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              FIcons.user,
              size: 16,
              color: colors.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
