// features/shared_space/widgets/shared_space_card.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../models/shared_space_models.dart';

class SharedSpaceCard extends StatelessWidget {
  final SharedSpace space;
  final VoidCallback onTap;

  const SharedSpaceCard({
    super.key,
    required this.space,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部：空间名称和成员数量
              Row(
                children: [
                  // 空间图标
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      FIcons.users,
                      size: 24,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 空间信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          space.name,
                          style: theme.typography.lg.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (space.description != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            space.description!,
                            style: theme.typography.sm.copyWith(
                              color: colorScheme.mutedForeground,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // 箭头图标
                  Icon(
                    FIcons.chevronRight,
                    size: 20,
                    color: colorScheme.mutedForeground,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 底部信息
              Row(
                children: [
                  // 成员数量
                  _buildInfoChip(
                    context,
                    icon: FIcons.users,
                    label: '${space.members?.length ?? 1}人',
                    color: colorScheme.primary,
                  ),
                  const SizedBox(width: 8),

                  // 交易数量
                  _buildInfoChip(
                    context,
                    icon: FIcons.receipt,
                    label: '${space.transactionCount}笔',
                    color: colorScheme.secondary,
                  ),

                  const Spacer(),

                  // 角色标识
                  _buildRoleBadge(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    final theme = context.theme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.typography.sm.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBadge(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    // TODO: 这里需要从认证状态获取当前用户ID来判断是否是创建者
    // 暂时显示创建者信息
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FIcons.crown,
            size: 12,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 4),
          Text(
            '创建者',
            style: theme.typography.sm.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
