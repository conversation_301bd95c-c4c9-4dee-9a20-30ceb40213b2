// features/shared_space/widgets/create_space_sheet.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../providers/shared_space_provider.dart';
import '../models/shared_space_models.dart';

class CreateSpaceSheet extends ConsumerStatefulWidget {
  final Function(SharedSpace) onSpaceCreated;

  const CreateSpaceSheet({super.key, required this.onSpaceCreated});

  @override
  ConsumerState<CreateSpaceSheet> createState() => _CreateSpaceSheetState();
}

class _CreateSpaceSheetState extends ConsumerState<CreateSpaceSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: BoxDecoration(
        color: colors.background,
        border: Border.symmetric(horizontal: BorderSide(color: colors.border)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '创建共享空间',
              style: theme.typography.xl2.copyWith(fontWeight: FontWeight.w600, color: colors.foreground, height: 1.5),
            ),
            Text('创建一个新的共享空间，与朋友一起记账', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
            const SizedBox(height: 8),
            SizedBox(
              width: 450,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // 空间名称输入
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          hintText: '例如：清迈毕业旅行',
                          prefixIcon: Padding(
                            padding: const EdgeInsets.only(left: 12, right: 8),
                            child: Icon(FIcons.users, size: 16, color: colors.mutedForeground),
                          ),
                        ),
                        validator: (value) {
                          if (value!.trim().isEmpty) {
                            return '请输入空间名称';
                          }
                          if (value.trim().length < 2) {
                            return '空间名称至少需要2个字符';
                          }
                          if (value.trim().length > 50) {
                            return '空间名称不能超过50个字符';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // 空间描述输入（可选）
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          hintText: '例如：记录我们四人的所有公共开销（可选）',
                          prefixIcon: Padding(
                            padding: const EdgeInsets.only(left: 12, right: 8),
                            child: Icon(FIcons.fileText, size: 16, color: colors.mutedForeground),
                          ),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value!.trim().length > 200) {
                            return '描述不能超过200个字符';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      // 创建按钮
                      FButton(
                        onPress: _isLoading ? null : _createSpace,
                        child: _isLoading
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox.square(dimension: 16, child: CircularProgressIndicator(strokeWidth: 2, color: colors.primaryForeground)),
                                  const SizedBox(width: 8),
                                  const Text('创建中...'),
                                ],
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [Icon(FIcons.plus, size: 16), const SizedBox(width: 8), const Text('创建空间')],
                              ),
                      ),
                      const SizedBox(height: 12),
                      // 提示文本
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(color: colors.muted.withValues(alpha: 0.5), borderRadius: BorderRadius.circular(8)),
                        child: Row(
                          children: [
                            Icon(FIcons.info, size: 16, color: colors.mutedForeground),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text('创建后您将成为空间管理员，可以邀请朋友加入', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createSpace() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final space = await ref
          .read(sharedSpaceProvider.notifier)
          .createSpace(name: _nameController.text.trim(), description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim());

      if (space != null) {
        widget.onSpaceCreated(space);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
