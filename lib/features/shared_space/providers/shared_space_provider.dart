// features/shared_space/providers/shared_space_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/shared_space_models.dart';
import '../services/shared_space_service.dart';

// 共享空间状态
class SharedSpaceState {
  final List<SharedSpace> spaces;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final bool hasMore;

  const SharedSpaceState({
    this.spaces = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.hasMore = true,
  });

  SharedSpaceState copyWith({
    List<SharedSpace>? spaces,
    bool? isLoading,
    String? error,
    int? currentPage,
    bool? hasMore,
  }) {
    return SharedSpaceState(
      spaces: spaces ?? this.spaces,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// 共享空间状态管理器
class SharedSpaceNotifier extends StateNotifier<SharedSpaceState> {
  final SharedSpaceService _service;

  SharedSpaceNotifier(this._service) : super(const SharedSpaceState());

  /// 加载共享空间列表
  Future<void> loadSpaces({bool refresh = false}) async {
    if (refresh) {
      state = const SharedSpaceState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final spaces = await _service.getSharedSpaces(page: page);

      final newSpaces = refresh ? spaces : [...state.spaces, ...spaces];
      final hasMore = spaces.length >= 20; // 假设每页20条

      state = state.copyWith(
        spaces: newSpaces,
        isLoading: false,
        error: null,
        currentPage: page + 1,
        hasMore: hasMore,
      );
    } catch (e) {
      String errorMessage = '加载共享空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );
    }
  }

  /// 创建新空间
  Future<SharedSpace?> createSpace({
    required String name,
    String? description,
  }) async {
    try {
      final newSpace = await _service.createSharedSpace(
        name: name,
        description: description,
      );

      // 将新空间添加到列表顶部
      state = state.copyWith(
        spaces: [newSpace, ...state.spaces],
      );

      return newSpace;
    } catch (e) {
      String errorMessage = '创建空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return null;
    }
  }

  /// 通过邀请码加入空间
  Future<SharedSpace?> joinSpaceWithCode(String inviteCode) async {
    try {
      final space = await _service.joinSpaceWithCode(inviteCode);

      // 检查空间是否已存在，如果不存在则添加
      final existingIndex = state.spaces.indexWhere((s) => s.id == space.id);
      if (existingIndex == -1) {
        state = state.copyWith(
          spaces: [space, ...state.spaces],
        );
      } else {
        // 更新现有空间信息
        final updatedSpaces = [...state.spaces];
        updatedSpaces[existingIndex] = space;
        state = state.copyWith(spaces: updatedSpaces);
      }

      return space;
    } catch (e) {
      String errorMessage = '加入空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return null;
    }
  }

  /// 离开空间
  Future<bool> leaveSpace(String spaceId) async {
    try {
      await _service.leaveSpace(spaceId);

      // 从列表中移除空间
      state = state.copyWith(
        spaces: state.spaces.where((space) => space.id != spaceId).toList(),
      );

      return true;
    } catch (e) {
      String errorMessage = '离开空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return false;
    }
  }

  /// 删除空间
  Future<bool> deleteSpace(String spaceId) async {
    try {
      await _service.deleteSpace(spaceId);

      // 从列表中移除空间
      state = state.copyWith(
        spaces: state.spaces.where((space) => space.id != spaceId).toList(),
      );

      return true;
    } catch (e) {
      String errorMessage = '删除空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return false;
    }
  }

  /// 更新空间信息
  Future<bool> updateSpace(String spaceId, {
    String? name,
    String? description,
  }) async {
    try {
      final updatedSpace = await _service.updateSpace(
        spaceId,
        name: name,
        description: description,
      );

      // 更新列表中的空间信息
      final updatedSpaces = state.spaces.map((space) {
        return space.id == spaceId ? updatedSpace : space;
      }).toList();

      state = state.copyWith(spaces: updatedSpaces);
      return true;
    } catch (e) {
      String errorMessage = '更新空间失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return false;
    }
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final sharedSpaceProvider = StateNotifierProvider<SharedSpaceNotifier, SharedSpaceState>((ref) {
  final service = ref.watch(sharedSpaceServiceProvider);
  return SharedSpaceNotifier(service);
});

// 单个空间详情Provider
final spaceDetailProvider = FutureProvider.family<SharedSpace, String>((ref, spaceId) async {
  final service = ref.watch(sharedSpaceServiceProvider);
  return service.getSharedSpaceDetail(spaceId);
});

// 空间结算信息Provider
final spaceSettlementProvider = FutureProvider.family<Settlement, String>((ref, spaceId) async {
  final service = ref.watch(sharedSpaceServiceProvider);
  return service.getSpaceSettlement(spaceId);
});
