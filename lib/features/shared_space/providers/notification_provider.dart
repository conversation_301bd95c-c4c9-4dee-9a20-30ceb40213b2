// features/shared_space/providers/notification_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/shared_space_models.dart';
import '../services/notification_service.dart';

// 通知状态
class NotificationState {
  final List<NotificationModel> notifications;
  final bool isLoading;
  final String? error;
  final int unreadCount;
  final int currentPage;
  final bool hasMore;

  const NotificationState({
    this.notifications = const [],
    this.isLoading = false,
    this.error,
    this.unreadCount = 0,
    this.currentPage = 1,
    this.hasMore = true,
  });

  NotificationState copyWith({
    List<NotificationModel>? notifications,
    bool? isLoading,
    String? error,
    int? unreadCount,
    int? currentPage,
    bool? hasMore,
  }) {
    return NotificationState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      unreadCount: unreadCount ?? this.unreadCount,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// 通知状态管理器
class NotificationNotifier extends StateNotifier<NotificationState> {
  final NotificationService _service;

  NotificationNotifier(this._service) : super(const NotificationState());

  /// 加载通知列表
  Future<void> loadNotifications({bool refresh = false}) async {
    if (refresh) {
      state = const NotificationState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _service.getNotifications(page: page);

      final newNotifications = refresh 
          ? response.notifications 
          : [...state.notifications, ...response.notifications];
      final hasMore = response.notifications.length >= 20; // 假设每页20条

      state = state.copyWith(
        notifications: newNotifications,
        isLoading: false,
        error: null,
        unreadCount: response.unreadCount,
        currentPage: page + 1,
        hasMore: hasMore,
      );
    } catch (e) {
      String errorMessage = '加载通知失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );
    }
  }

  /// 加载未读通知数量
  Future<void> loadUnreadCount() async {
    try {
      final count = await _service.getUnreadCount();
      state = state.copyWith(unreadCount: count);
    } catch (e) {
      // 静默处理错误，不影响主要功能
      print('加载未读通知数量失败: $e');
    }
  }

  /// 标记通知为已读
  Future<void> markAsRead(String notificationId) async {
    try {
      await _service.markAsRead(notificationId);

      // 更新本地状态
      final updatedNotifications = state.notifications.map((notification) {
        if (notification.id == notificationId && !notification.isRead) {
          return notification.copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
        return notification;
      }).toList();

      // 减少未读数量
      final newUnreadCount = state.unreadCount > 0 ? state.unreadCount - 1 : 0;

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      );
    } catch (e) {
      String errorMessage = '标记已读失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
    }
  }

  /// 标记所有通知为已读
  Future<void> markAllAsRead() async {
    try {
      await _service.markAllAsRead();

      // 更新本地状态
      final updatedNotifications = state.notifications.map((notification) {
        if (!notification.isRead) {
          return notification.copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
        return notification;
      }).toList();

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: 0,
      );
    } catch (e) {
      String errorMessage = '标记全部已读失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
    }
  }

  /// 删除通知
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _service.deleteNotification(notificationId);

      // 从本地状态中移除
      final notification = state.notifications.firstWhere(
        (n) => n.id == notificationId,
        orElse: () => throw Exception('通知不存在'),
      );

      final updatedNotifications = state.notifications
          .where((n) => n.id != notificationId)
          .toList();

      // 如果删除的是未读通知，减少未读数量
      final newUnreadCount = !notification.isRead && state.unreadCount > 0
          ? state.unreadCount - 1
          : state.unreadCount;

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      );
    } catch (e) {
      String errorMessage = '删除通知失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
    }
  }

  /// 响应空间邀请
  Future<bool> respondToSpaceInvite(String spaceId, String action, String notificationId) async {
    try {
      await _service.respondToSpaceInvite(spaceId, action);

      // 如果是拒绝，直接删除通知
      if (action == 'reject') {
        await deleteNotification(notificationId);
      } else {
        // 如果是接受，标记为已读
        await markAsRead(notificationId);
      }

      return true;
    } catch (e) {
      String errorMessage = '响应邀请失败';
      if (e is AppException) {
        errorMessage = e.message;
      }

      state = state.copyWith(error: errorMessage);
      return false;
    }
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final notificationProvider = StateNotifierProvider<NotificationNotifier, NotificationState>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return NotificationNotifier(service);
});

// 未读通知数量Provider
final unreadCountProvider = Provider<int>((ref) {
  return ref.watch(notificationProvider).unreadCount;
});
