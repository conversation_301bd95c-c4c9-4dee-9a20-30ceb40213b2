// features/shared_space/models/shared_space_models.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:decimal/decimal.dart';

part 'shared_space_models.freezed.dart';
part 'shared_space_models.g.dart';

// 共享空间成员角色枚举
enum MemberRole {
  @JsonValue('owner')
  owner,
  @JsonValue('member')
  member,
}

// 邀请状态枚举
enum InviteStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('accepted')
  accepted,
  @JsonValue('rejected')
  rejected,
  @JsonValue('expired')
  expired,
}

// 通知类型枚举
enum NotificationType {
  @JsonValue('space_invite')
  spaceInvite,
  @JsonValue('new_transaction')
  newTransaction,
  @JsonValue('settlement_update')
  settlementUpdate,
  @JsonValue('member_joined')
  memberJoined,
  @JsonValue('member_left')
  memberLeft,
}

// 共享空间成员模型
@freezed
abstract class SharedSpaceMember with _$SharedSpaceMember {
  const factory SharedSpaceMember({
    required String userId,
    required String username,
    String? avatarUrl, // 改为可选，因为API可能返回null
    @Default(MemberRole.member) MemberRole role, // 提供默认值，因为API可能不返回此字段
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String)
    required DateTime joinedAt,
    String? email,
    @Default(InviteStatus.accepted) InviteStatus status, // 添加状态字段匹配API
  }) = _SharedSpaceMember;

  factory SharedSpaceMember.fromJson(Map<String, dynamic> json) =>
      _$SharedSpaceMemberFromJson(json);
}

// 创建者信息模型
@freezed
abstract class SpaceCreator with _$SpaceCreator {
  const factory SpaceCreator({
    required String id,
    required String username,
    String? avatarUrl,
  }) = _SpaceCreator;

  factory SpaceCreator.fromJson(Map<String, dynamic> json) =>
      _$SpaceCreatorFromJson(json);
}

// 共享空间模型
@freezed
abstract class SharedSpace with _$SharedSpace {
  const factory SharedSpace({
    required String id,
    required String name,
    String? description,
    required SpaceCreator creator,
    required DateTime createdAt,
    required DateTime updatedAt,
    // 这些字段在基础API中可能不存在，设为可选
    List<SharedSpaceMember>? members,
    @Default(0) int transactionCount,
    String? currentInviteCode,
    DateTime? inviteCodeExpiresAt,
  }) = _SharedSpace;

  factory SharedSpace.fromJson(Map<String, dynamic> json) =>
      _$SharedSpaceFromJson(json);
}

// 邀请码模型
@freezed
abstract class InviteCode with _$InviteCode {
  const factory InviteCode({
    required String code,
    required String spaceId,
    required String spaceName,
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String)
    required DateTime expiresAt,
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String)
    required DateTime createdAt,
    required String createdBy,
    @Default(InviteStatus.pending) InviteStatus status,
  }) = _InviteCode;

  factory InviteCode.fromJson(Map<String, dynamic> json) =>
      _$InviteCodeFromJson(json);
}

// 结算项模型
@freezed
abstract class SettlementItem with _$SettlementItem {
  const factory SettlementItem({
    required String fromUserId,
    required String fromUsername,
    required String toUserId,
    required String toUsername,
    @JsonKey(fromJson: Decimal.parse, toJson: _decimalToString)
    required Decimal amount,
  }) = _SettlementItem;

  factory SettlementItem.fromJson(Map<String, dynamic> json) =>
      _$SettlementItemFromJson(json);
}

// 结算信息模型
@freezed
abstract class Settlement with _$Settlement {
  const factory Settlement({
    required String spaceId,
    required List<SettlementItem> items,
    @JsonKey(fromJson: Decimal.parse, toJson: _decimalToString)
    required Decimal totalAmount,
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String)
    required DateTime calculatedAt,
    @Default(false) bool isSettled,
  }) = _Settlement;

  factory Settlement.fromJson(Map<String, dynamic> json) =>
      _$SettlementFromJson(json);
}

// 通知模型
@freezed
abstract class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required String id,
    required String userId,
    required NotificationType type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    @Default(false) bool isRead,
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String)
    required DateTime createdAt,
    @JsonKey(fromJson: _parseDateTime, toJson: _dateTimeToIso8601StringNullable)
    DateTime? readAt,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}

// 共享空间列表响应模型
@freezed
abstract class SharedSpaceListResponse with _$SharedSpaceListResponse {
  const factory SharedSpaceListResponse({
    required List<SharedSpace> spaces,
    required int total,
    required int page,
    required int limit,
  }) = _SharedSpaceListResponse;

  factory SharedSpaceListResponse.fromJson(Map<String, dynamic> json) =>
      _$SharedSpaceListResponseFromJson(json);
}

// 通知列表响应模型
@freezed
abstract class NotificationListResponse with _$NotificationListResponse {
  const factory NotificationListResponse({
    required List<NotificationModel> notifications,
    required int total,
    required int unreadCount,
    required int page,
    required int limit,
  }) = _NotificationListResponse;

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationListResponseFromJson(json);
}

// 辅助函数
String _dateTimeToIso8601String(DateTime dateTime) => dateTime.toIso8601String();

String? _dateTimeToIso8601StringNullable(DateTime? dateTime) => 
    dateTime?.toIso8601String();

DateTime? _parseDateTime(String? dateTimeString) => 
    dateTimeString != null ? DateTime.parse(dateTimeString) : null;

String _decimalToString(Decimal decimal) => decimal.toString();
