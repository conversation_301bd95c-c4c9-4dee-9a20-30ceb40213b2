// features/shared_space/services/notification_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/network_client.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/shared_space_models.dart';

class NotificationService {
  final NetworkClient _networkClient;

  NotificationService(this._networkClient);

  /// 获取通知列表
  Future<NotificationListResponse> getNotifications({
    int page = 1,
    int limit = 20,
    bool? unreadOnly,
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'limit': limit,
    };
    if (unreadOnly != null) {
      queryParameters['unread_only'] = unreadOnly;
    }

    return await _networkClient.request<NotificationListResponse>(
      '/notifications',
      method: HttpMethod.get,
      queryParameters: queryParameters,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return NotificationListResponse.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析通知列表失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /notifications 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 获取未读通知数量
  Future<int> getUnreadCount() async {
    final response = await _networkClient.request<Map<String, dynamic>>(
      '/notifications/unread-count',
      method: HttpMethod.get,
      fromJsonT: (json) => json as Map<String, dynamic>,
    );
    
    return response['count'] as int? ?? 0;
  }

  /// 标记通知为已读
  Future<void> markAsRead(String notificationId) async {
    await _networkClient.request<void>(
      '/notifications/$notificationId/read',
      method: HttpMethod.patch,
    );
  }

  /// 标记所有通知为已读
  Future<void> markAllAsRead() async {
    await _networkClient.request<void>(
      '/notifications/mark-all-read',
      method: HttpMethod.patch,
    );
  }

  /// 删除通知
  Future<void> deleteNotification(String notificationId) async {
    await _networkClient.request<void>(
      '/notifications/$notificationId',
      method: HttpMethod.delete,
    );
  }

  /// 响应共享空间邀请
  Future<void> respondToSpaceInvite(String spaceId, String action) async {
    await _networkClient.request<void>(
      '/shared-spaces/$spaceId/invites/respond',
      method: HttpMethod.put,
      data: {
        'action': action, // 'accept' or 'reject'
      },
    );
  }
}

// Provider for NotificationService
final notificationServiceProvider = Provider<NotificationService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return NotificationService(networkClient);
});
