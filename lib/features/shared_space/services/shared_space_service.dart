// features/shared_space/services/shared_space_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/network_client.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../models/shared_space_models.dart';

class SharedSpaceService {
  final NetworkClient _networkClient;

  SharedSpaceService(this._networkClient);

  /// 获取用户的共享空间列表
  Future<List<SharedSpace>> getSharedSpaces({
    int page = 1,
    int limit = 20,
  }) async {
    return await _networkClient.request<List<SharedSpace>>(
      '/shared-spaces',
      method: HttpMethod.get,
      queryParameters: {
        'page': page,
        'limit': limit,
      },
      fromJsonT: (json) {
        if (json is List) {
          try {
            return json.map((item) => SharedSpace.fromJson(item as Map<String, dynamic>)).toList();
          } catch (e) {
            throw DataParsingException("解析共享空间列表失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /shared-spaces 期望返回一个列表，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 创建新的共享空间
  Future<SharedSpace> createSharedSpace({
    required String name,
    String? description,
  }) async {
    return await _networkClient.request<SharedSpace>(
      '/shared-spaces',
      method: HttpMethod.post,
      data: {
        'name': name,
        if (description != null) 'description': description,
      },
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return SharedSpace.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析创建空间响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API POST /shared-spaces 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 获取共享空间详情
  Future<SharedSpace> getSharedSpaceDetail(String spaceId) async {
    return await _networkClient.request<SharedSpace>(
      '/shared-spaces/$spaceId',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return SharedSpace.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析空间详情失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /shared-spaces/$spaceId 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 生成邀请码
  Future<InviteCode> generateInviteCode(String spaceId) async {
    return await _networkClient.request<InviteCode>(
      '/shared-spaces/$spaceId/invite-code',
      method: HttpMethod.post,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return InviteCode.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析邀请码响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API POST /shared-spaces/$spaceId/invite-code 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 通过邀请码加入空间
  Future<SharedSpace> joinSpaceWithCode(String inviteCode) async {
    return await _networkClient.request<SharedSpace>(
      '/shared-spaces/join-with-code',
      method: HttpMethod.post,
      data: {
        'code': inviteCode,
      },
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return SharedSpace.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析加入空间响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API POST /shared-spaces/join-with-code 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 移除成员
  Future<void> removeMember(String spaceId, String userId) async {
    await _networkClient.request<void>(
      '/shared-spaces/$spaceId/members/$userId',
      method: HttpMethod.delete,
    );
  }

  /// 离开空间
  Future<void> leaveSpace(String spaceId) async {
    await _networkClient.request<void>(
      '/shared-spaces/$spaceId/leave',
      method: HttpMethod.post,
    );
  }

  /// 解散空间
  Future<void> deleteSpace(String spaceId) async {
    await _networkClient.request<void>(
      '/shared-spaces/$spaceId',
      method: HttpMethod.delete,
    );
  }

  /// 获取空间结算信息
  Future<Settlement> getSpaceSettlement(String spaceId) async {
    return await _networkClient.request<Settlement>(
      '/shared-spaces/$spaceId/settlement',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return Settlement.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析结算信息失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /shared-spaces/$spaceId/settlement 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  /// 更新空间信息
  Future<SharedSpace> updateSpace(String spaceId, {
    String? name,
    String? description,
  }) async {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;

    return await _networkClient.request<SharedSpace>(
      '/shared-spaces/$spaceId',
      method: HttpMethod.put,
      data: data,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return SharedSpace.fromJson(json);
          } catch (e) {
            throw DataParsingException("解析更新空间响应失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API PUT /shared-spaces/$spaceId 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }
}

// Provider for SharedSpaceService
final sharedSpaceServiceProvider = Provider<SharedSpaceService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return SharedSpaceService(networkClient);
});
