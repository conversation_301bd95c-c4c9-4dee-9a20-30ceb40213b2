// features/shared_space/pages/shared_space_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../providers/shared_space_provider.dart';
import '../widgets/shared_space_card.dart';
import '../widgets/create_space_sheet.dart';
import '../widgets/join_space_sheet.dart';
import '../../../shared/services/toast_service.dart';

class SharedSpaceListPage extends ConsumerStatefulWidget {
  const SharedSpaceListPage({super.key});

  @override
  ConsumerState<SharedSpaceListPage> createState() => _SharedSpaceListPageState();
}

class _SharedSpaceListPageState extends ConsumerState<SharedSpaceListPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // 初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(sharedSpaceProvider.notifier).loadSpaces(refresh: true);

      // 检查是否有深度链接传递的邀请码
      _checkForInviteCode();
    });
  }

  void _checkForInviteCode() {
    final uri = GoRouterState.of(context).uri;
    final joinCode = uri.queryParameters['join_code'];
    if (joinCode != null) {
      // 自动弹出加入空间弹窗并填入邀请码
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showJoinSpaceSheetWithCode(joinCode);
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // 接近底部时加载更多
      ref.read(sharedSpaceProvider.notifier).loadSpaces();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final state = ref.watch(sharedSpaceProvider);

    // 监听错误状态
    ref.listen<String?>(
      sharedSpaceProvider.select((state) => state.error),
      (previous, error) {
        if (error != null) {
          ToastService.showDestructive(
            description: Text(error),
          );
          ref.read(sharedSpaceProvider.notifier).clearError();
        }
      },
    );

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        title: Text(
          '共享空间',
          style: theme.typography.xl,
        ),
        backgroundColor: colorScheme.background,
        foregroundColor: colorScheme.foreground,
        elevation: 0,
        centerTitle: true,
        actions: [
          // 加入空间按钮
          FButton(
            style: FButtonStyle.ghost(),
            onPress: _showJoinSpaceSheet,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FIcons.logIn,
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text('加入'),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(sharedSpaceProvider.notifier).loadSpaces(refresh: true);
        },
        child: state.spaces.isEmpty && !state.isLoading
            ? _buildEmptyState(context)
            : _buildSpacesList(context, state),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateSpaceSheet,
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.primaryForeground,
        child: const Icon(FIcons.plus),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: colorScheme.muted,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                FIcons.users,
                size: 40,
                color: colorScheme.mutedForeground,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '还没有共享空间',
              style: theme.typography.xl.copyWith(
                color: colorScheme.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '创建一个空间开始与朋友共同记账，\n或者通过邀请码加入朋友的空间',
              style: theme.typography.base.copyWith(
                color: colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: FButton(
                    style: FButtonStyle.outline(),
                    onPress: _showJoinSpaceSheet,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FIcons.logIn,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        const Text('加入空间'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FButton(
                    onPress: _showCreateSpaceSheet,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FIcons.plus,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        const Text('创建空间'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpacesList(BuildContext context, state) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: state.spaces.length + (state.isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.spaces.length) {
          // 加载指示器
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final space = state.spaces[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: SharedSpaceCard(
            space: space,
            onTap: () => _navigateToSpaceDetail(space.id),
          ),
        );
      },
    );
  }

  void _showCreateSpaceSheet() {
    showFSheet(
      context: context,
      side: FLayout.btt,
      builder: (context) => CreateSpaceSheet(
        onSpaceCreated: (space) {
          Navigator.of(context).pop();
          _navigateToInviteSuccess(space);
        },
      ),
    );
  }

  void _showJoinSpaceSheet() {
    showFSheet(
      context: context,
      side: FLayout.btt,
      builder: (context) => JoinSpaceSheet(
        onSpaceJoined: (space) {
          Navigator.of(context).pop();
          ToastService.show(
            description: Text('成功加入"${space.name}"！'),
          );
          _navigateToSpaceDetail(space.id);
        },
      ),
    );
  }

  void _showJoinSpaceSheetWithCode(String inviteCode) {
    showFSheet(
      context: context,
      side: FLayout.btt,
      builder: (context) => JoinSpaceSheet(
        initialCode: inviteCode,
        onSpaceJoined: (space) {
          Navigator.of(context).pop();
          ToastService.show(
            description: Text('成功加入"${space.name}"！'),
          );
          _navigateToSpaceDetail(space.id);
        },
      ),
    );
  }

  void _navigateToSpaceDetail(String spaceId) {
    context.push('/shared-space/$spaceId');
  }

  void _navigateToInviteSuccess(space) {
    context.push('/shared-space/invite-success', extra: space);
  }
}
