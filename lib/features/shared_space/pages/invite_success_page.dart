// features/shared_space/pages/invite_success_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../models/shared_space_models.dart';
import '../services/shared_space_service.dart';
import '../widgets/invite_code_card.dart';

class InviteSuccessPage extends ConsumerStatefulWidget {
  final SharedSpace space;

  const InviteSuccessPage({
    super.key,
    required this.space,
  });

  @override
  ConsumerState<InviteSuccessPage> createState() => _InviteSuccessPageState();
}

class _InviteSuccessPageState extends ConsumerState<InviteSuccessPage> {
  InviteCode? _inviteCode;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _generateInviteCode();
  }

  Future<void> _generateInviteCode() async {
    try {
      final service = ref.read(sharedSpaceServiceProvider);
      final inviteCode = await service.generateInviteCode(widget.space.id);
      
      if (mounted) {
        setState(() {
          _inviteCode = inviteCode;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = '生成邀请码失败，请稍后重试';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        title: Text(
          '邀请成员',
          style: theme.typography.xl,
        ),
        backgroundColor: colorScheme.background,
        foregroundColor: colorScheme.foreground,
        elevation: 0,
        centerTitle: true,
        leading: FButton(
          style: FButtonStyle.ghost(),
          onPress: () => context.pop(),
          child: Icon(
            FIcons.x,
            size: 20,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 成功提示
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        FIcons.check,
                        size: 30,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '恭喜！',
                      style: theme.typography.xl.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '"${widget.space.name}" 已创建成功',
                      style: theme.typography.base.copyWith(
                        color: colorScheme.foreground,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // 邀请码卡片
              if (_isLoading)
                _buildLoadingCard(context)
              else if (_error != null)
                _buildErrorCard(context)
              else if (_inviteCode != null)
                InviteCodeCard(inviteCode: _inviteCode!),

              const SizedBox(height: 24),

              // 提示信息
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.muted.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          FIcons.info,
                          size: 20,
                          color: colorScheme.mutedForeground,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '邀请提示',
                          style: theme.typography.base.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.foreground,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildTipItem(
                      context,
                      '邀请码有效期为24小时，过期后需要重新生成',
                    ),
                    const SizedBox(height: 8),
                    _buildTipItem(
                      context,
                      '朋友可以通过邀请码或邀请链接加入空间',
                    ),
                    const SizedBox(height: 8),
                    _buildTipItem(
                      context,
                      '作为空间管理员，您可以随时管理成员',
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // 底部按钮
              Row(
                children: [
                  Expanded(
                    child: FButton(
                      style: FButtonStyle.outline(),
                      onPress: () {
                        context.pop();
                        context.pop(); // 返回到空间列表
                      },
                      child: const Text('稍后邀请'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: FButton(
                      onPress: () {
                        context.pop();
                        context.push('/shared-space/${widget.space.id}');
                      },
                      child: const Text('进入空间'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingCard(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            CircularProgressIndicator(
              color: colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              '正在生成邀请码...',
              style: theme.typography.base.copyWith(
                color: colorScheme.mutedForeground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Icon(
              FIcons.circleAlert,
              size: 40,
              color: colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: theme.typography.base.copyWith(
                color: colorScheme.destructive,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            FButton(
              style: FButtonStyle.outline(),
              onPress: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _generateInviteCode();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(BuildContext context, String text) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 4,
          height: 4,
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: colorScheme.mutedForeground,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: theme.typography.sm.copyWith(
              color: colorScheme.mutedForeground,
            ),
          ),
        ),
      ],
    );
  }
}
