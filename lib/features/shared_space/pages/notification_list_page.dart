// features/shared_space/pages/notification_list_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../providers/notification_provider.dart';
import '../providers/shared_space_provider.dart';
import '../widgets/notification_card.dart';
import '../models/shared_space_models.dart';
import '../../../shared/services/toast_service.dart';

class NotificationListPage extends ConsumerStatefulWidget {
  const NotificationListPage({super.key});

  @override
  ConsumerState<NotificationListPage> createState() => _NotificationListPageState();
}

class _NotificationListPageState extends ConsumerState<NotificationListPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // 初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationProvider.notifier).loadNotifications(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // 接近底部时加载更多
      ref.read(notificationProvider.notifier).loadNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final state = ref.watch(notificationProvider);

    // 监听错误状态
    ref.listen<String?>(
      notificationProvider.select((state) => state.error),
      (previous, error) {
        if (error != null) {
          ToastService.showDestructive(
            description: Text(error),
          );
          ref.read(notificationProvider.notifier).clearError();
        }
      },
    );

    return FScaffold(
      header: FHeader(
        title: Text(
          '通知',
          style: theme.typography.xl.copyWith(
            color: colors.foreground,
          ),
        ),
        suffixes: [
          // 全部已读按钮
          if (state.unreadCount > 0)
            FHeaderAction(
              icon: Icon(FIcons.checkCheck),
              onPress: _markAllAsRead,
            ),
        ],
      ),
      child: RefreshIndicator(
        onRefresh: () async {
          await ref.read(notificationProvider.notifier).loadNotifications(refresh: true);
        },
        child: state.notifications.isEmpty && !state.isLoading
            ? _buildEmptyState(context)
            : _buildNotificationsList(context, state),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: colors.muted,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                FIcons.bell,
                size: 40,
                color: colors.mutedForeground,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '暂无通知',
              style: theme.typography.xl.copyWith(
                color: colors.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '当有新的邀请或活动时，\n您会在这里收到通知',
              style: theme.typography.base.copyWith(
                color: colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(BuildContext context, state) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: state.notifications.length + (state.isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.notifications.length) {
          // 加载指示器
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final notification = state.notifications[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: NotificationCard(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
            onAccept: notification.type == NotificationType.spaceInvite
                ? () => _handleInviteResponse(notification, 'accept')
                : null,
            onReject: notification.type == NotificationType.spaceInvite
                ? () => _handleInviteResponse(notification, 'reject')
                : null,
            onDelete: () => _deleteNotification(notification.id),
          ),
        );
      },
    );
  }

  void _handleNotificationTap(NotificationModel notification) {
    // 标记为已读
    if (!notification.isRead) {
      ref.read(notificationProvider.notifier).markAsRead(notification.id);
    }

    // 根据通知类型进行相应的导航
    switch (notification.type) {
      case NotificationType.spaceInvite:
        // 邀请通知，不需要额外导航，用户可以直接在卡片上操作
        break;
      case NotificationType.newTransaction:
        // 新交易通知，导航到交易详情
        final transactionId = notification.data?['transactionId'] as String?;
        if (transactionId != null) {
          context.push('/home/<USER>/$transactionId');
        }
        break;
      case NotificationType.settlementUpdate:
        // 结算更新通知，导航到空间详情
        final spaceId = notification.data?['spaceId'] as String?;
        if (spaceId != null) {
          context.push('/shared-space/$spaceId');
        }
        break;
      case NotificationType.memberJoined:
      case NotificationType.memberLeft:
        // 成员变动通知，导航到空间设置
        final spaceId = notification.data?['spaceId'] as String?;
        if (spaceId != null) {
          context.push('/shared-space/$spaceId/settings');
        }
        break;
    }
  }

  Future<void> _handleInviteResponse(NotificationModel notification, String action) async {
    final spaceId = notification.data?['spaceId'] as String?;
    if (spaceId == null) {
      ToastService.showDestructive(
        description: const Text('邀请信息不完整'),
      );
      return;
    }

    final success = await ref.read(notificationProvider.notifier).respondToSpaceInvite(
      spaceId,
      action,
      notification.id,
    );

    if (success) {
      if (action == 'accept') {
        ToastService.show(
          description: const Text('已接受邀请！'),
        );
        
        // 刷新共享空间列表
        ref.read(sharedSpaceProvider.notifier).loadSpaces(refresh: true);
        
        // 导航到空间详情
        if (mounted) {
          context.push('/shared-space/$spaceId');
        }
      } else {
        ToastService.show(
          description: const Text('已拒绝邀请'),
        );
      }
    }
  }

  Future<void> _deleteNotification(String notificationId) async {
    await ref.read(notificationProvider.notifier).deleteNotification(notificationId);
  }

  Future<void> _markAllAsRead() async {
    await ref.read(notificationProvider.notifier).markAllAsRead();
    ToastService.show(
      description: const Text('所有通知已标记为已读'),
    );
  }
}
