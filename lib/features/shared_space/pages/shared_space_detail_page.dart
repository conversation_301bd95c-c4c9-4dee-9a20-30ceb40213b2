// features/shared_space/pages/shared_space_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../providers/shared_space_provider.dart';
import '../widgets/settlement_card.dart';
import '../models/shared_space_models.dart';
import '../../../shared/services/toast_service.dart';

class SharedSpaceDetailPage extends ConsumerStatefulWidget {
  final String spaceId;

  const SharedSpaceDetailPage({
    super.key,
    required this.spaceId,
  });

  @override
  ConsumerState<SharedSpaceDetailPage> createState() => _SharedSpaceDetailPageState();
}

class _SharedSpaceDetailPageState extends ConsumerState<SharedSpaceDetailPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(spaceDetailProvider(widget.spaceId));
      ref.invalidate(spaceSettlementProvider(widget.spaceId));
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colorScheme = theme.colors;

    final spaceAsync = ref.watch(spaceDetailProvider(widget.spaceId));
    final settlementAsync = ref.watch(spaceSettlementProvider(widget.spaceId));

    return Scaffold(
      backgroundColor: colorScheme.background,
      body: spaceAsync.when(
        loading: () => _buildLoadingState(context),
        error: (error, stack) => _buildErrorState(context, error.toString()),
        data: (space) => _buildContent(context, space, settlementAsync),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddTransaction(),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.primaryForeground,
        child: const Icon(FIcons.plus),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '共享空间',
          style: theme.typography.xl,
        ),
        backgroundColor: colors.background,
        foregroundColor: colors.foreground,
        elevation: 0,
        centerTitle: true,
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    final theme = context.theme;
    final colors = theme.colors;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '共享空间',
          style: theme.typography.xl,
        ),
        backgroundColor: colors.background,
        foregroundColor: colors.foreground,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FIcons.circleAlert,
                size: 64,
                color: colors.destructive,
              ),
              const SizedBox(height: 16),
              Text(
                '加载失败',
                style: theme.typography.xl.copyWith(
                  color: colors.foreground,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: theme.typography.base.copyWith(
                  color: colors.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              FButton(
                onPress: () {
                  ref.invalidate(spaceDetailProvider(widget.spaceId));
                  ref.invalidate(spaceSettlementProvider(widget.spaceId));
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, space, AsyncValue<Settlement> settlementAsync) {
    final theme = context.theme;
    final colors = theme.colors;

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // App Bar
        SliverAppBar(
          title: Text(
            space.name,
            style: theme.typography.xl,
          ),
          backgroundColor: colors.background,
          foregroundColor: colors.foreground,
          elevation: 0,
          centerTitle: true,
          floating: true,
          snap: true,
          actions: [
            FButton(
              style: FButtonStyle.ghost(),
              onPress: () => _navigateToSettings(space),
              child: Icon(
                FIcons.settings,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),

        // Content
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 结算总览
                settlementAsync.when(
                  loading: () => _buildSettlementLoading(context),
                  error: (error, stack) => _buildSettlementError(context),
                  data: (settlement) => SettlementCard(settlement: settlement),
                ),

                const SizedBox(height: 24),

                // 账单流标题
                Row(
                  children: [
                    Icon(
                      FIcons.receipt,
                      size: 20,
                      color: colors.foreground,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '账单流',
                      style: theme.typography.xl.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${space.transactionCount}笔记录',
                      style: theme.typography.sm.copyWith(
                        color: colors.mutedForeground,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 账单列表 (这里使用模拟数据，实际应该从API获取)
                _buildTransactionList(context),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSettlementLoading(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            CircularProgressIndicator(
              color: colors.primary,
            ),
            const SizedBox(width: 16),
            Text(
              '正在计算结算信息...',
              style: theme.typography.base.copyWith(
                color: colors.mutedForeground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettlementError(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              FIcons.circleAlert,
              size: 20,
              color: colors.destructive,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '结算信息加载失败',
                style: theme.typography.base.copyWith(
                  color: colors.destructive,
                ),
              ),
            ),
            FButton(
              style: FButtonStyle.outline(),
              onPress: () {
                ref.invalidate(spaceSettlementProvider(widget.spaceId));
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionList(BuildContext context) {
    // 这里应该从API获取实际的交易数据
    // 暂时使用空状态
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            FIcons.receipt,
            size: 48,
            color: colors.mutedForeground,
          ),
          const SizedBox(height: 16),
          Text(
            '还没有账单记录',
            style: theme.typography.xl.copyWith(
              color: colors.foreground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右下角的"+"按钮开始记账',
            style: theme.typography.base.copyWith(
              color: colors.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToSettings(space) {
    context.push('/shared-space/${widget.spaceId}/settings', extra: space);
  }

  void _navigateToAddTransaction() {
    // 这里应该导航到记账页面，并关联到当前共享空间
    ToastService.show(
      description: const Text('记账功能开发中...'),
    );
  }
}
