// features/search/pages/search_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../../footprint/pages/footprint_page.dart';

/// 搜索页面 - 现在重定向到足迹页面
/// 保持原有的路由结构，但实际显示足迹内容
class SearchPage extends ConsumerWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 直接显示足迹页面内容
    return const FootprintPage();
  }
}
