// features/home/<USER>/transaction_detail_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_expense_tracker/shared/models/action_item_model.dart';
import 'package:flutter_expense_tracker/shared/widgets/dialogs/action_bottom_sheet.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:intl/intl.dart';
import '../models/transaction_model.dart';
import '../widgets/feed/comment_section_widget.dart';
import '../widgets/feed/comment_input_bar.dart';
import '../providers/transaction_detail_provider.dart';
import '../widgets/transaction_detail_skeleton.dart';
import '/shared/config/category_config.dart';

class TransactionDetailPage extends ConsumerWidget {
  final String transactionId;

  const TransactionDetailPage({super.key, required this.transactionId});

  /// 获取分类显示名称
  String _getCategoryDisplayName(TransactionModel transaction) {
    // 优先使用服务端本地化的分类名称
    if (transaction.categoryText != null && transaction.categoryText!.isNotEmpty) {
      return transaction.categoryText!;
    }
    // 回退到客户端分类ID映射
    if (transaction.categoryId != null) {
      return CategoryConfig.getCategoryName(transaction.categoryId);
    }
    // 最后回退到原有的category字段
    return transaction.category;
  }

  /// 获取支付方式显示名称
  String _getPaymentMethodDisplayName(TransactionModel transaction) {
    // 优先使用服务端本地化的支付方式名称
    if (transaction.paymentMethodText != null && transaction.paymentMethodText!.isNotEmpty) {
      // 处理可能的多语言key格式，如 "messages.payment_method.其他"
      final text = transaction.paymentMethodText!;
      if (text.startsWith('messages.payment_method.')) {
        return text.substring('messages.payment_method.'.length);
      }
      return text;
    }
    // 回退到原有的paymentMethod字段
    return transaction.paymentMethod ?? '未知';
  }

  /// 获取金额显示文本
  String _getAmountDisplayText(TransactionModel transaction) {
    // 优先使用API返回的display字段
    if (transaction.display != null) {
      return transaction.display!.fullString;
    }

    // 向后兼容：使用原有的amount字段格式化
    final isExpense = transaction.type == TransactionType.expense;
    final currencyFormat = NumberFormat.currency(locale: 'zh_CN', symbol: '¥', decimalDigits: 2);

    return '${isExpense ? '-' : '+'}${currencyFormat.format(transaction.amount.abs())}';
  }

  /// 获取金额颜色
  Color _getAmountColor(TransactionModel transaction, FColors colors) {
    // 统一处理：支出用红色，收入用主色调
    return transaction.type == TransactionType.expense ? colors.destructive : colors.primary;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final detailState = ref.watch(transactionDetailProvider(transactionId));
    final theme = context.theme;
    final colors = theme.colors;

    // 显示骨架屏
    if (detailState.isLoading && detailState.transaction == null) {
      return const TransactionDetailSkeleton();
    }

    // 显示错误状态
    if (detailState.errorMessage != null && detailState.transaction == null) {
      return Scaffold(
        backgroundColor: colors.background,
        body: SafeArea(
          child: Column(
            children: [
              _buildPageHeader(context, theme, colors, null),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(FIcons.ellipsis, size: 48, color: colors.destructive),
                      const SizedBox(height: 16),
                      Text('加载失败', style: theme.typography.xl2),
                      const SizedBox(height: 8),
                      Text(
                        detailState.errorMessage!,
                        style: theme.typography.sm.copyWith(color: colors.mutedForeground),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      FButton(
                        onPress: () {
                          ref.read(transactionDetailProvider(transactionId).notifier).reload(transactionId);
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final transaction = detailState.transaction;
    if (transaction == null) {
      return const TransactionDetailSkeleton();
    }

    timeago.setLocaleMessages('zh_CN', timeago.ZhCnMessages());

    // 页面头部
    final pageHeader = _buildPageHeader(context, theme, colors, transaction);

    return Scaffold(
      // 仍然可以使用 Scaffold 作为根，以便 CommentInputBar 能正确固定在底部
      resizeToAvoidBottomInset: false,
      backgroundColor: colors.background, // 页面背景色来自主题
      body: SafeArea(
        // 3. 使用 SafeArea
        child: Column(
          children: [
            pageHeader,
            Expanded(
              child: CustomScrollView(
                // 保留 CustomScrollView 以便内容过多时滚动
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                      // 4. 使用 ShadCard 包裹主要详情区域
                      child: FCard.raw(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // --- 顶部：类别图标、类别名称、时间、更多操作按钮 ---
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 2.0),
                                    child: Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(color: colors.primary.withValues(alpha: 0.1), shape: BoxShape.circle),
                                      child: Icon(
                                        transaction.categoryId != null
                                            ? CategoryConfig.getCategoryIcon(transaction.categoryId)
                                            : CategoryConfig.getCategoryIconByName(transaction.category),
                                        size: 24,
                                        color: colors.mutedForeground,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(_getCategoryDisplayName(transaction), style: theme.typography.lg.copyWith(fontWeight: FontWeight.bold)),
                                        const SizedBox(height: 4),
                                        Text(
                                          timeago.format(transaction.timestamp, locale: 'zh_CN'),
                                          style: theme.typography.sm.copyWith(color: colors.mutedForeground),
                                        ),
                                      ],
                                    ),
                                  ),
                                  FButton.icon(
                                    // 6. 更多操作按钮
                                    style: FButtonStyle.ghost(),
                                    onPress: () => _showTransactionActions(context, ref, transaction),
                                    child: Icon(FIcons.ellipsis, color: colors.mutedForeground, size: 20),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 24),
                              // 金额
                              Center(
                                child: Text(
                                  _getAmountDisplayText(transaction),
                                  style: theme.typography.xl2.copyWith(
                                    // 使用更大的字号
                                    fontWeight: FontWeight.bold,
                                    color: _getAmountColor(transaction, colors),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 24),
                              FDivider(axis: Axis.horizontal), // 8. 使用 FDivider
                              const SizedBox(height: 12),
                              // 详细信息行
                              _buildDetailRow(
                                context,
                                icon: FIcons.text, // 替换图标
                                label: '描述',
                                valueWidget: Text(
                                  transaction.description ?? '无描述',
                                  style: theme.typography.sm.copyWith(
                                    color: colors.foreground, // 值颜色
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),

                              // 支付方式（统一显示）
                              _buildDetailRow(
                                context,
                                icon: FIcons.wallet, // 替换图标
                                label: '支付',
                                valueWidget: Text(
                                  _getPaymentMethodDisplayName(transaction),
                                  style: theme.typography.sm.copyWith(
                                    color: colors.foreground, // 值颜色
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),

                              // 现金来源信息显示
                              if (transaction.cashSource != null)
                                _buildDetailRow(
                                  context,
                                  icon: FIcons.banknote,
                                  label: '账户',
                                  valueWidget: Text(
                                    transaction.cashSource!.name,
                                    style: theme.typography.sm.copyWith(color: colors.foreground, fontWeight: FontWeight.normal),
                                  ),
                                ),

                              _buildDetailRow(
                                context,
                                icon: FIcons.calendarClock, // 替换图标
                                label: '时间',
                                valueWidget: Text(
                                  DateFormat('yyyy年M月d日 HH:mm:ss', 'zh_CN').format(transaction.timestamp),
                                  style: theme.typography.sm.copyWith(
                                    color: colors.foreground, // 值颜色
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),
                              if (transaction.location != null && transaction.location!.isNotEmpty)
                                _buildDetailRow(
                                  context,
                                  icon: FIcons.mapPin, // 替换图标
                                  label: '地点',
                                  valueWidget: Text(
                                    transaction.location!,
                                    style: theme.typography.sm.copyWith(
                                      color: colors.foreground, // 值颜色
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ),
                                ),

                              if (transaction.tags.isNotEmpty) ...[
                                const SizedBox(height: 6),
                                _buildDetailRow(
                                  context,
                                  icon: FIcons.tags, // 替换图标
                                  label: '标签',
                                  valueWidget: Wrap(
                                    spacing: 8.0,
                                    runSpacing: 6.0,
                                    children: transaction.tags
                                        .map(
                                          (tag) => FBadge(
                                            // 9. 使用 FBadge
                                            style: FBadgeStyle.secondary(),
                                            child: Text(tag, style: theme.typography.sm.copyWith(fontSize: 11)),
                                            // FBadge 默认有合适的 padding 和圆角
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ),
                              ],

                              // if (transaction.isAiBuild == true)
                              //   Padding(
                              //     padding: const EdgeInsets.only(top: 20.0),
                              //     child: Row(
                              //       mainAxisAlignment: MainAxisAlignment.end,
                              //       children: [
                              //         Icon(FIcons.sparkles, size: 16, color: colorScheme.primary), // 使用主题色
                              //         const SizedBox(width: 4),
                              //         Text(
                              //           '由 AI 智能记录',
                              //           style: theme.textTheme.small.copyWith(
                              //             color: colorScheme.primary,
                              //             fontStyle: FontStyle.italic,
                              //           ),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // --- 评论区 ---
                  SliverToBoxAdapter(child: CommentSectionWidget(transactionId: transaction.id)),
                  SliverToBoxAdapter(
                    child: SizedBox(height: 100 + MediaQuery.of(context).viewInsets.bottom), // 底部留白，防止被输入框遮挡
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      // 评论输入栏移到页面内容中，避免键盘遮挡问题
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: CommentInputBar(transactionId: transaction.id),
      ),
    );
  }

  // 构建页面头部
  Widget _buildPageHeader(BuildContext context, FThemeData theme, FColors colors, TransactionModel? transaction) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        children: [
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: () => GoRouter.of(context).pop(),
            child: Icon(FIcons.arrowLeft, size: 20, color: colors.foreground),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '交易详情',
              style: theme.typography.xl2.copyWith(fontWeight: FontWeight.w600),
              textAlign: TextAlign.center, // 标题居中
            ),
          ),
          // 右侧占位，保持标题居中，或者可以放其他操作按钮
          SizedBox(width: 40),
        ],
      ),
    );
  }

  // 详细信息行
  Widget _buildDetailRow(BuildContext context, {required IconData icon, required String label, required Widget valueWidget}) {
    final theme = context.theme;
    final colors = theme.colors;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: colors.mutedForeground),
          const SizedBox(width: 16),
          SizedBox(
            width: 70,
            child: Text(
              '$label:',
              style: theme.typography.sm.copyWith(color: colors.mutedForeground, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  // 显示操作表单
  void _showTransactionActions(BuildContext context, WidgetRef ref, TransactionModel transaction) {
    final theme = context.theme;
    final colors = theme.colors;

    final List<ActionItem> primaryActions = [];
    final List<ActionItem> destructiveActions = [];

    primaryActions.add(
      ActionItem(
        title: '编辑',
        icon: FIcons.pencil,
        onTap: () {
          Navigator.of(context).pop();
          // TODO: 导航到编辑页面
        },
      ),
    );
    if (transaction.isShared) {
      primaryActions.add(
        ActionItem(
          title: '取消共享',
          icon: FIcons.eyeOff,
          onTap: () {
            Navigator.of(context).pop();
          },
        ),
      );
    } else {
      primaryActions.add(
        ActionItem(
          title: '共享',
          icon: FIcons.share2,
          onTap: () {
            Navigator.of(context).pop();
          },
        ),
      );
    }

    primaryActions.add(
      ActionItem(
        title: '收藏',
        icon: FIcons.bookmark, // 或 FIcons.bookmarkCheck
        onTap: () {
          Navigator.of(context).pop();
        },
      ),
    );
    destructiveActions.add(
      ActionItem(
        title: '删除',
        icon: FIcons.trash2,
        // color 属性在 ActionItem 中不再直接使用，由 ShadButton 的 foregroundColor 控制
        onTap: () {
          Navigator.of(context).pop(); // 先关闭 Sheet
          showFDialog(
            context: context, // 使用当前的 context
            builder: (dialogContext, style, animation) => FDialog(
              style: style,
              animation: animation,
              title: const Text('确认删除'),
              body: const Text('您确定要删除此条交易记录吗？此操作无法撤销。'),
              actions: [
                FButton(style: FButtonStyle.outline(), onPress: () => Navigator.of(dialogContext).pop(), child: const Text('取消')),
                FButton(
                  onPress: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('删除'),
                ),
              ],
            ),
          );
        },
      ),
    );
    if (primaryActions.isEmpty && destructiveActions.isEmpty) {
      showFToast(context: context, title: const Text('没有可用的操作'));
      return;
    }

    showModalBottomSheet(
      context: GoRouter.of(context).routerDelegate.navigatorKey.currentContext!, // 获取GoRouter的根Navigator的context
      // 或者，如果你的 MaterialApp 有一个全局的 navigatorKey:
      // context: rootNavigatorKey.currentContext!,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        // sheetContext 现在是根Navigator下的context
        return ActionBottomSheet(actions: primaryActions, destructiveActions: destructiveActions.isNotEmpty ? destructiveActions : null);
      },
    );
  }
}
