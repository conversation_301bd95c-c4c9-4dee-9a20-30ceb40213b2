// features/transaction_detail/models/comment_model.dart (新文件或已有文件)
import 'package:freezed_annotation/freezed_annotation.dart';

part 'comment_model.freezed.dart';
part 'comment_model.g.dart';

@freezed
abstract class CommentModel with _$CommentModel {
  @JsonSerializable(explicitToJson: true)
  const factory CommentModel({
    required String id,
    required String transactionId,  // 确保API返回或可以设置
    required String userId,         // 发表评论的用户ID
    required String userName,       // 发表评论的用户名
    required String userAvatarUrl,  // 发表评论的用户头像
    String? parentCommentId,        // 父评论ID，用于回复
    required String commentText,    // 回复文本内容
    String? repliedToUserId,        // 被回复的用户的ID (可选，但推荐)
    String? repliedToUserName,      // 被回复的用户的名称 (可选，但推荐)
    required DateTime createdAt,
    @JsonKey(fromJson: _dateTimeNullableParse, toJson: _dateTimeNullableToIso8601String)
    DateTime? updatedAt,
    @Default([]) List<CommentModel> replies, // 这个字段现在只用于后端返回数据，UI上不再递归渲染
    // 或者后端直接返回嵌套结构，但要注意层级深度
    @Default(0) int likeCount, // 可选：评论点赞数
    @Default(false) bool likedByCurrentUser, // 可选：当前用户是否点赞
  }) = _CommentModel;

  factory CommentModel.fromJson(Map<String, dynamic> json) =>
      _$CommentModelFromJson(json);
}

// 辅助函数用于日期序列化/反序列化
String _dateTimeToIso8601String(DateTime dt) => dt.toIso8601String();
DateTime? _dateTimeNullableParse(String? dateString) => dateString != null ? DateTime.parse(dateString) : null;
String? _dateTimeNullableToIso8601String(DateTime? dt) => dt?.toIso8601String();