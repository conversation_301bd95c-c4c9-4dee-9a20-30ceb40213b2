// features/home/<USER>/calendar/daily_transaction_modal.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '/features/home/<USER>/transaction_model.dart';
import '/features/home/<USER>/home_providers.dart';

class DailyTransactionModal extends ConsumerWidget {
  final DateTime selectedDate;

  const DailyTransactionModal({super.key, required this.selectedDate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final transactionsAsyncValue = ref.watch(transactionsForSelectedDateProvider(selectedDate));

    return DraggableScrollableSheet(
      initialChildSize: 0.5, // 初始高度为屏幕的一半
      minChildSize: 0.3,   // 最小高度
      maxChildSize: 0.8,   // 最大高度
      expand: false,
      builder: (BuildContext context, ScrollController scrollController) {
        return Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: theme.cardColor, // 使用卡片颜色作为背景
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部拖拽指示器和标题
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  margin: const EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              Text(
                '${DateFormat('yyyy年M月d日', 'zh_CN').format(selectedDate)} 交易记录',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 16.0),
              // 交易列表
              Expanded(
                child: transactionsAsyncValue.when(
                  data: (transactions) {
                    if (transactions.isEmpty) {
                      return const Center(child: Text('当日无交易记录'));
                    }
                    return ListView.separated(
                      controller: scrollController, // 关联滚动控制器
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        return ListTile(
                          leading: CircleAvatar(
                            // child: Icon(transaction.type == TransactionType.expense ? Icons.arrow_downward : Icons.arrow_upward),
                            // backgroundColor: transaction.type == TransactionType.expense ? Colors.red.shade100 : Colors.green.shade100,
                            child: Image.asset(transaction.iconUrl, errorBuilder: (c,e,s) => Icon(Icons.help_outline)), // 简单的图标
                          ),
                          title: Text(transaction.category),
                          subtitle: Text(transaction.description ?? '无描述'),
                          trailing: Text(
                            '${transaction.type == TransactionType.expense ? '-' : '+'}${transaction.amount.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: transaction.type == TransactionType.expense
                                  ? Colors.redAccent
                                  : Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                      separatorBuilder: (context, index) => const Divider(height: 1),
                    );
                  },
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (err, stack) => Center(child: Text('加载交易失败: $err')),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}