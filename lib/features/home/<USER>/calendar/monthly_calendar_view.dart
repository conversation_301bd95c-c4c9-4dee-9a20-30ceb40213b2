// features/home/<USER>/calendar/monthly_calendar_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shimmer/shimmer.dart'; // 导入 Shimmer 包
import '/features/home/<USER>/home_providers.dart';
import '/features/home/<USER>/calendar/daily_cell_widget.dart';
import '/features/home/<USER>/daily_expense_summary_model.dart';
import 'package:forui/forui.dart'; // 1. 引入 forui
import 'package:intl/intl.dart';

class MonthlyCalendarView extends ConsumerWidget {
  const MonthlyCalendarView({super.key});

  // 骨架屏单元格 (适配 Shadcn UI 风格)
  Widget _buildShimmerCell(BuildContext context) {
    final theme = context.theme;
    return AspectRatio(
      aspectRatio: 1.0, // 保持方形
      child: Container(
        margin: const EdgeInsets.all(1.0),
        decoration: BoxDecoration(
          color: Colors.grey.shade200, // 使用主题颜色
          borderRadius: theme.style.borderRadius, // 使用主题圆角
        ),
      ),
    );
  }

  // 骨架屏日历
  Widget _buildCalendarSkeleton(BuildContext context) {
    final theme = context.theme;
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      child: GridView.count(
        crossAxisCount: 7,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        childAspectRatio: 1.6,
        padding: EdgeInsets.zero,
        mainAxisSpacing: 2.0,
        crossAxisSpacing: 2.0,
        children: List.generate(35, (index) => _buildShimmerCell(context)),
      ),
    );
  }

  // 辅助方法：为底部趋势条获取颜色 (与单元格背景色逻辑保持一致或类似)
  Color _getStripColorForHeatLevel(BuildContext context, ExpenseHeatLevel level) {
    final theme = context.theme;
    final colors = theme.colors;
    final isDark = colors.brightness == Brightness.dark;
    final baseHotColor = colors.primary;
    final baseColdBackgroundColor = colors.background; // 使用 background 颜色

    switch (level) {
      case ExpenseHeatLevel.none: // 趋势条中通常不显示none
        return Colors.transparent;
      case ExpenseHeatLevel.low:
        return Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.15 : 0.12), baseColdBackgroundColor);
      case ExpenseHeatLevel.medium:
        return Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.3 : 0.25), baseColdBackgroundColor);
      case ExpenseHeatLevel.high:
        return Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.5 : 0.45), baseColdBackgroundColor);
      case ExpenseHeatLevel.veryHigh:
        return baseHotColor.withValues(alpha: isDark ? 0.75 : 0.7);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme; // 获取 Forui 主题
    final colors = theme.colors; // 3. 获取当前的 FColors

    final displayMonth = ref.watch(currentDisplayMonthProvider);
    // 现在 watch calendarMonthDataProvider
    final calendarDataAsyncValue = ref.watch(calendarMonthDataProvider(displayMonth));
    final selectedDateState = ref.watch(selectedDateProvider);

    // 月份切换逻辑
    final now = DateTime.now();
    final nextMonthToDisplay = DateTime(displayMonth.year, displayMonth.month + 1, 1);
    final canNavigateToNextMonth = !(nextMonthToDisplay.year > now.year || (nextMonthToDisplay.year == now.year && nextMonthToDisplay.month > now.month));
    // 2. 移除外部容器的 Material 风格装饰 (BoxDecoration)
    // 如果需要卡片外观，可以考虑用 ShadCard 包裹，但日历本身通常不需要。
    // 背景色会由父级或 ShadAppBuilder 提供。

    return Padding(
      // 使用 Padding 替代之前的 Container margin 和 padding
      padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 4.0, bottom: 12.0), // 减少顶部padding
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 3. 顶部：标题和月份切换 (使用 Shadcn UI 组件)
          Padding(
            padding: const EdgeInsets.only(
              bottom: 8.0, // 减少底部间距
              top: 8.0, // 移除状态栏高度，因为现在由父组件处理
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '消费日历',
                  style: theme.typography.xl2.copyWith(fontWeight: FontWeight.w600, color: colors.foreground),
                ),
                Row(
                  children: [
                    FButton.icon(
                      style: FButtonStyle.ghost(),
                      onPress: () {
                        ref.read(currentDisplayMonthProvider.notifier).update((state) => DateTime(state.year, state.month - 1));
                      },
                      child: Icon(FIcons.chevronLeft, color: colors.primary, size: 18),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      DateFormat('yyyy年 M月', 'zh_CN').format(displayMonth),
                      style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500, color: colors.foreground),
                    ),
                    const SizedBox(width: 4),
                    FButton.icon(
                      style: FButtonStyle.ghost(),
                      onPress: canNavigateToNextMonth
                          ? () {
                              ref.read(currentDisplayMonthProvider.notifier).update((state) => DateTime(state.year, state.month + 1));
                            }
                          : null,
                      child: Icon(
                        FIcons.chevronRight,
                        color: canNavigateToNextMonth ? colors.primary : colors.mutedForeground,
                        size: 18,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 4. 星期头部 (使用 Shadcn UI 文本样式)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: ['一', '二', '三', '四', '五', '六', '日'].map((day) => Text(day, style: theme.typography.sm.copyWith(color: colors.mutedForeground))).toList(),
            ),
          ),
          // 5. 日期网络
          calendarDataAsyncValue.when(
            data: (calendarData) {
              final daysInMonth = DateUtils.getDaysInMonth(calendarData.year, calendarData.month);
              final firstDayOfMonth = DateTime(calendarData.year, calendarData.month, 1);
              final firstWeekdayOfMonth = firstDayOfMonth.weekday;
              int daysToPadAtStart = firstWeekdayOfMonth - 1; // 假设周一 (1) 对应索引 0, 周日 (7) 对应索引 6
              final List<Widget> dayWidgets = [];

              // 填充上个月的尾巴
              for (int i = 0; i < daysToPadAtStart; i++) {
                final padDay = firstDayOfMonth.subtract(Duration(days: daysToPadAtStart - i));
                // 重要：DailyCellWidget 需要内部重构
                dayWidgets.add(DailyCellWidget(day: padDay, isOutOfMonth: true));
              }

              // 填充当月的日期
              for (int i = 0; i < daysInMonth; i++) {
                final day = DateTime(calendarData.year, calendarData.month, i + 1);
                final summary = calendarData.dailySummaries.firstWhere(
                  (s) => DateUtils.isSameDay(s.date, day),
                  orElse: () => DailyExpenseSummaryModel(date: day, totalExpense: 0, heatLevel: ExpenseHeatLevel.none),
                );
                // 重要：DailyCellWidget 需要内部重构
                dayWidgets.add(
                  DailyCellWidget(
                    day: day,
                    summary: summary,
                    isSelected: selectedDateState != null && DateUtils.isSameDay(selectedDateState, day),
                    isToday: DateUtils.isSameDay(now, day),
                    onTap: () {
                      // 6. 弹窗替换
                      final currentSelectedDateValue = ref.read(selectedDateProvider);
                      if (currentSelectedDateValue != null && DateUtils.isSameDay(currentSelectedDateValue, day)) {
                        ref.read(selectedDateProvider.notifier).state = null;
                      } else {
                        ref.read(selectedDateProvider.notifier).state = day;
                      }
                    },
                  ),
                );
              }

              // 填充下个月的开头
              int totalCellsFilled = daysToPadAtStart + daysInMonth;
              int daysToPadAtEnd = (totalCellsFilled % 7 == 0) ? 0 : 7 - (totalCellsFilled % 7);
              final DateTime firstDayOfNextCalendarMonth = DateTime(calendarData.year, calendarData.month + 1, 1);
              for (int i = 0; i < daysToPadAtEnd; i++) {
                final padDay = firstDayOfNextCalendarMonth.add(Duration(days: i));
                // 重要：DailyCellWidget 需要内部重构
                dayWidgets.add(DailyCellWidget(day: padDay, isOutOfMonth: true));
              }

              return GridView.count(
                crossAxisCount: 7,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.6,
                padding: EdgeInsets.zero,
                mainAxisSpacing: 2.0,
                // 可以根据 ShadTheme 调整
                crossAxisSpacing: 2.0,
                // 可以根据 ShadTheme 调整
                children: dayWidgets,
              );
            },
            loading: () => _buildCalendarSkeleton(context),
            error: (err, stack) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 20.0),
              child: Center(child: Text('加载日历数据失败', style: theme.typography.sm.copyWith(color: colors.mutedForeground))),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 12.0), // 增加顶部间距
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: calendarDataAsyncValue.when(
                    data: (calendarData) {
                      final currencyFormat = NumberFormat.currency(locale: 'zh_CN', symbol: '¥', decimalDigits: 2);
                      return Text(
                        '本月: ${currencyFormat.format(calendarData.totalExpenseForMonth)}',
                        style: theme.typography.sm.copyWith(color: colors.mutedForeground),
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                    loading: () => Text('统计中...', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                    error: (error, stacktrace) => Text('无法统计', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                  ),
                ),
                const SizedBox(width: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('趋势: ', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
                    if (calendarDataAsyncValue.hasValue &&
                        calendarDataAsyncValue.value?.trendColors != null &&
                        calendarDataAsyncValue.value!.trendColors!.isNotEmpty)
                      ...calendarDataAsyncValue.value!.trendColors!.map((hexColor) {
                        try {
                          final colorValue = int.parse(hexColor.startsWith('#') ? hexColor.substring(1) : hexColor, radix: 16);
                          final color = Color(0xFF000000 | colorValue);
                          return Container(
                            width: 10,
                            height: 8,
                            margin: const EdgeInsets.only(left: 1.5),
                            decoration: BoxDecoration(color: color, borderRadius: theme.style.borderRadius / 2), // 使用主题圆角
                          );
                        } catch (e) {
                          return const SizedBox.shrink();
                        }
                      })
                    else
                      ...ExpenseHeatLevel.values
                          .where((level) => level != ExpenseHeatLevel.none)
                          .map(
                            (level) => Container(
                              width: 10,
                              height: 8,
                              margin: const EdgeInsets.only(left: 1.5),
                              decoration: BoxDecoration(
                                color: _getStripColorForHeatLevel(context, level),
                                // 根据主题调整
                                borderRadius: theme.style.borderRadius / 2, // 使用主题圆角
                              ),
                            ),
                          ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
