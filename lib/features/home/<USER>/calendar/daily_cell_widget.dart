// features/home/<USER>/calendar/daily_cell_widget.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../../models/daily_expense_summary_model.dart';

class DailyCellWidget extends StatelessWidget {
  final DateTime day;
  final DailyExpenseSummaryModel? summary;
  final bool isSelected;
  final bool isToday;
  final bool isOutOfMonth;
  final VoidCallback? onTap;

  const DailyCellWidget({super.key, required this.day, this.summary, this.isSelected = false, this.isToday = false, this.isOutOfMonth = false, this.onTap});

  // 辅助方法：根据热力等级和主题获取背景色和文本色
  // 辅助方法：根据热力等级和主题获取背景色和文本色
  ({Color backgroundColor, Color textColor}) _getHeatColors(BuildContext context, ExpenseHeatLevel heatLevel) {
    final theme = context.theme;
    final colors = theme.colors;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // 基准颜色，我们将从这个颜色派生出不同热度
    // 使用 primary 作为最高热度的基准，因为它通常是主题中最突出的颜色
    final baseHotColor = colors.primary;
    final baseHotForegroundColor = colors.primaryForeground;

    // 最低热度的背景（接近透明或卡片/背景色）
    final baseColdBackgroundColor = Colors.transparent; // 或者 colorScheme.card / colorScheme.background

    // 最低热度的文本颜色
    final baseColdForegroundColor = colors.foreground;

    // 定义5级热力背景色和对应的前景文本色
    // 通过调整 baseHotColor 相对于 baseColdBackgroundColor 的不透明度来创建递增效果
    switch (heatLevel) {
      case ExpenseHeatLevel.none:
        return (
          backgroundColor: baseColdBackgroundColor,
          textColor: baseColdForegroundColor.withValues(alpha: 0.6), // 无消费时文本稍淡
        );

      case ExpenseHeatLevel.low:
        return (
          // 混合少量 baseHotColor 到 baseColdBackgroundColor (或直接使用低透明度的 baseHotColor)
          backgroundColor: Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.12 : 0.1), colors.background), // 如果background是白色，则相当于浅色
          // 文本颜色需要确保在浅色背景上可读
          textColor: isDark ? baseHotForegroundColor.withValues(alpha: 0.7) : baseHotColor.withValues(alpha: 0.7),
        );
      case ExpenseHeatLevel.medium:
        return (
          backgroundColor: Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.25 : 0.2), colors.background),
          textColor: isDark ? baseHotForegroundColor.withValues(alpha: 0.85) : baseHotColor.withValues(alpha: 0.85),
        );
      case ExpenseHeatLevel.high:
        return (backgroundColor: Color.alphaBlend(baseHotColor.withValues(alpha: isDark ? 0.45 : 0.4), colors.background), textColor: baseHotForegroundColor);
      case ExpenseHeatLevel.veryHigh:
        // 最高热度可以直接使用 baseHotColor
        return (
          backgroundColor: baseHotColor.withValues(alpha: isDark ? 0.7 : 0.65), // 确保不会完全覆盖，除非设计需要
          textColor: baseHotForegroundColor,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    Color cellTextColor;
    Color? cellBackgroundColor; // 背景色现在不能为空
    FontWeight cellFontWeight = FontWeight.normal;
    Border? cellEffectiveBorder;

    final cellRadius = theme.style.borderRadius.topLeft.x / 1.5; // 单元格的圆角

    if (isOutOfMonth) {
      cellTextColor = colors.mutedForeground.withValues(alpha: 0.4);
      cellBackgroundColor = Colors.transparent; // 月份外背景透明
      cellEffectiveBorder = Border.all(color: Colors.transparent, width: 0);
    } else {
      // 优先处理选中和今日状态
      if (isSelected) {
        cellBackgroundColor = colors.primary;
        cellTextColor = colors.primaryForeground;
        cellFontWeight = FontWeight.bold;
        cellEffectiveBorder = Border.all(
          color: colors.primary, // 选中状态使用 primary 颜色作为边框
          width: 2, // 边框加粗
        );
      } else if (isToday) {
        cellBackgroundColor = colors.secondary; // 今日使用 secondary 背景
        cellTextColor = colors.secondaryForeground;
        cellFontWeight = FontWeight.bold;
        cellEffectiveBorder = Border.all(
          color: colors.border, // 今日边框
          width: 1,
        );
      } else {
        // 如果非选中且非今日，则根据消费热力决定背景和文本颜色
        if (summary != null && summary!.totalExpense >= 0) {
          // totalExpense >= 0 包括了0消费的情况
          final heatColors = _getHeatColors(context, summary!.heatLevel);
          cellBackgroundColor = heatColors.backgroundColor;
          cellTextColor = heatColors.textColor;
          cellFontWeight = summary!.heatLevel == ExpenseHeatLevel.none ? FontWeight.normal : FontWeight.w500; // 无消费则普通，有消费则稍粗
          cellEffectiveBorder = Border.all(
            // 给一个默认的细边框或透明边框
            color: summary!.heatLevel == ExpenseHeatLevel.none ? Colors.transparent : colors.border.withValues(alpha: 0.2), // 有热度时给个淡边框
            width: summary!.heatLevel == ExpenseHeatLevel.none ? 0 : 0.5,
          );
        } else {
          // 理论上 summary 不会为 null (因为 orElse)，但为防万一
          cellBackgroundColor = Colors.transparent;
          cellTextColor = colors.foreground;
          cellEffectiveBorder = Border.all(color: Colors.transparent, width: 0);
        }
      }
    }

    return AspectRatio(
      aspectRatio: 1.0,
      child: FButton.raw(
        style: FButtonStyle.ghost(),
        onPress: (!isOutOfMonth && onTap != null) ? onTap : null,
        child: Container(
          decoration: BoxDecoration(
            color: cellBackgroundColor,
            border: cellEffectiveBorder,
            borderRadius: BorderRadius.circular(cellRadius),
          ),
          child: Center(
            child: Text(
              '${day.day}',
              style: theme.typography.sm.copyWith(
                color: cellTextColor, // 应用计算出的文本颜色
                fontWeight: cellFontWeight,
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
