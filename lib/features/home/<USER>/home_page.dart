// features/home/<USER>/home_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/calendar/monthly_calendar_view.dart';
import '../widgets/feed/transaction_feed_view.dart';
import '../providers/home_providers.dart';
import 'package:forui/forui.dart';
import '../../../../app/assets/app_vectors.dart';
import '/shared/l10n/app_strings.dart';
import '../../shared_space/widgets/notification_icon.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    return Scaffold(
      backgroundColor: theme.colors.primary,
      resizeToAvoidBottomInset: false,
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              expandedHeight: 220.0,
              floating: false,
              pinned: false,
              backgroundColor: theme.colors.primary,
              flexibleSpace: const _WelcomeHeader(),
              actions: const [NotificationIcon(), SizedBox(width: 16)],
            ),
          ];
        },
        body: _MainContent(),
      ),
    );
  }
}

class _MainContent extends ConsumerWidget {
  const _MainContent();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    return Container(
      decoration: BoxDecoration(
        color: theme.colors.background,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(theme.style.borderRadius.topLeft.x),
          topRight: Radius.circular(theme.style.borderRadius.topRight.x)
        ),
      ),
      child: Column(
        children: [
          // 固定在顶部的日历组件
          Padding(
            padding: const EdgeInsets.only(
              top: 8,
              bottom: 4,
            ),
            child: const MonthlyCalendarView(),
          ),
          // 固定的Tab按钮栏
          const _FixedTabBar(),
          // 可滚动的交易数据内容
          Expanded(
            child: const _ScrollableTransactionContent(),
          ),
        ],
      ),
    );
  }
}

// 固定的Tab按钮栏组件
class _FixedTabBar extends ConsumerWidget {
  const _FixedTabBar();

  // 定义 Tab 的数据结构
  static const List<({TransactionFeedType type, String label})> _tabData = [
    (type: TransactionFeedType.all, label: '全部'),
    (type: TransactionFeedType.expense, label: '支出'),
    (type: TransactionFeedType.income, label: '收入'),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;
    final currentSelectedType = ref.watch(currentTransactionFeedTypeProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: _tabData.map((tabInfo) {
          final isSelected = tabInfo.type == currentSelectedType;

          if (isSelected) {
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: FButton(
                  mainAxisSize: MainAxisSize.min,
                  onPress: () {}, // 空函数，保持按钮启用状态
                  child: Text(
                    tabInfo.label,
                    style: theme.typography.sm.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.primaryForeground,
                    ),
                  ),
                ),
              ),
            );
          } else {
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: FButton(
                  style: FButtonStyle.outline(),
                  mainAxisSize: MainAxisSize.min,
                  onPress: () {
                    ref.read(currentTransactionFeedTypeProvider.notifier).state = tabInfo.type;
                  },
                  child: Text(
                    tabInfo.label,
                    style: theme.typography.sm.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colors.mutedForeground,
                    ),
                  ),
                ),
              ),
            );
          }
        }).toList(),
      ),
    );
  }
}

// 可滚动的交易内容组件
class _ScrollableTransactionContent extends ConsumerWidget {
  const _ScrollableTransactionContent();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentSelectedType = ref.watch(currentTransactionFeedTypeProvider);

    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + 16),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: TransactionFeedView(key: ValueKey(currentSelectedType), intendedFeedType: currentSelectedType),
      ),
    );
  }
}

// 恢复的头部组件，显示总消费金额和年度进度
class _WelcomeHeader extends ConsumerStatefulWidget {
  const _WelcomeHeader();

  @override
  ConsumerState<_WelcomeHeader> createState() => _WelcomeHeaderState();
}

class _WelcomeHeaderState extends ConsumerState<_WelcomeHeader> {
  bool _isAmountVisible = true;

  // 计算年度时间进度
  double _getYearProgress() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year + 1, 1, 1);
    final totalDays = endOfYear.difference(startOfYear).inDays;
    final passedDays = now.difference(startOfYear).inDays;
    return passedDays / totalDays;
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final now = DateTime.now();

    String greeting;
    if (now.hour < 12) {
      greeting = AppStrings.get('goodMorning');
    } else if (now.hour < 18) {
      greeting = AppStrings.get('goodAfternoon');
    } else {
      greeting = AppStrings.get('goodEvening');
    }

    final yearProgress = _getYearProgress();
    final progressPercentage = (yearProgress * 100).toInt();

    return FlexibleSpaceBar(
      background: Container(
        decoration: BoxDecoration(color: colors.primary),
        child: ClipRect(
          child: Stack(
            children: [
              // Positioned(
              //   right: 0,
              //   top: 45,
              //   child: SvgPicture.asset(AppVectors.lineBg),
              // ),
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                    const Spacer(), // 使用 Spacer 推送内容到底部

                    // 总消费金额标签和眼睛图标
                    Row(
                      children: [
                        Text(
                          '总消费金额',
                          style: theme.typography.sm.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colors.primaryForeground.withValues(alpha: 0.9)
                          ),
                        ),
                        const SizedBox(width: 8),
                        FButton.icon(
                          style: FButtonStyle.ghost(),
                          onPress: () {
                            setState(() {
                              _isAmountVisible = !_isAmountVisible;
                            });
                          },
                          child: Icon(
                            _isAmountVisible ? FIcons.eye : FIcons.eyeOff,
                            color: colors.primaryForeground.withValues(alpha: 0.8),
                            size: 16
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // 总消费金额
                    Text(
                      _isAmountVisible ? '¥125,680.50' : '••••••••',
                      style: theme.typography.xl2.copyWith(
                        color: colors.primaryForeground,
                        fontWeight: FontWeight.bold
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 年度时间进度条 - 简化版本
                    Row(
                      children: [
                        Text(
                          '${now.year}年进度',
                          style: theme.typography.xs.copyWith(
                            color: colors.primaryForeground.withValues(alpha: 0.8)
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Container(
                            height: 3,
                            decoration: BoxDecoration(
                              color: colors.primaryForeground.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(1.5)
                            ),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: FractionallySizedBox(
                                widthFactor: yearProgress,
                                child: Container(
                                  height: 3,
                                  decoration: BoxDecoration(
                                    color: colors.primaryForeground,
                                    borderRadius: BorderRadius.circular(1.5)
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '$progressPercentage%',
                          style: theme.typography.xs.copyWith(
                            color: colors.primaryForeground.withValues(alpha: 0.7)
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8), // 减少底部间距
                  ],
                ),
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }
}
