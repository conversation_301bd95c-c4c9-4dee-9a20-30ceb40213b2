// features/transaction_detail/services/comment_service.dart (新文件)
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '/core/network/network_client.dart';
import '../models/comment_model.dart';
import '/core/network/exceptions/app_exception.dart';

class CommentService {
  final NetworkClient _networkClient;
  CommentService(this._networkClient);

  Future<List<CommentModel>> getComments(String transactionId) async {
    return await _networkClient.request<List<CommentModel>>(
      '/transactions/$transactionId/comments', // 你的API端点
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is List) {
          return json.map((item) => CommentModel.fromJson(item as Map<String, dynamic>)).toList();
        }
        throw DataParsingException("API /transactions/$transactionId/comments 期望返回一个列表，但收到 ${json.runtimeType}");
      },
    );
  }

  Future<CommentModel> addComment({
    required String transactionId,
    required String commentText,
    String? parentCommentId,
  }) async {
    final Map<String, dynamic> requestData = {
      'comment_text': commentText,
    };
    if (parentCommentId != null) {
      requestData['parent_comment_id'] = parentCommentId;
    }

    return await _networkClient.request<CommentModel>(
      '/transactions/$transactionId/comments',
      method: HttpMethod.post,
      data: requestData,
      fromJsonT: (json) => CommentModel.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<void> deleteComment(String commentId) async {
    await _networkClient.request<void>( // 或者 <Map<String,dynamic>> 如果有响应体
      '/transactions/comments/$commentId',
      method: HttpMethod.delete,
      // fromJsonT: (json) => {}, // 如果是 void 且拦截器不处理空data，可能不需要
    );
  }

}

final commentServiceProvider = Provider<CommentService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return CommentService(networkClient);
});

// Provider for fetching comments for a specific transaction
final commentsProvider = FutureProvider.autoDispose
    .family<List<CommentModel>, String>((ref, transactionId) async {
  final commentService = ref.watch(commentServiceProvider);
  final comments = await commentService.getComments(transactionId);
  // 你可能需要在这里处理评论的层级关系，如果后端返回的是扁平列表
  // 例如，将回复组织到其父评论的 replies 列表中
  // 为了简单起见，这里假设后端返回的已经是嵌套好的，或者我们暂时只显示顶层评论
  return comments;
});


// Provider/Notifier for adding a comment (可选，如果需要管理提交状态)
// 或者直接在Widget中使用 FutureBuilder/调用 service然后刷新 commentsProvider
// 这里用一个简单的 FutureProvider 来演示提交动作
final addCommentProvider = FutureProvider.autoDispose
    .family<CommentModel, ({String transactionId, String commentText, String? parentCommentId})>(
        (ref, params) async {
      final commentService = ref.watch(commentServiceProvider);
      final newComment = await commentService.addComment(
        transactionId: params.transactionId,
        commentText: params.commentText,
        parentCommentId: params.parentCommentId,
      );
      // 成功添加评论后，让相关的 commentsProvider 失效以重新获取最新评论
      ref.invalidate(commentsProvider(params.transactionId));
      return newComment;
    });