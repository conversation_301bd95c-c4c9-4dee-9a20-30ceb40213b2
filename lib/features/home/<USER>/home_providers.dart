// features/home/<USER>/home_providers.dart
import 'dart:async';
import 'dart:math'; // For static data generation

import 'package:flutter/material.dart'; // For DateUtils
import 'package:flutter_expense_tracker/features/auth/providers/auth_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '/features/home/<USER>/daily_expense_summary_model.dart';
import '/features/home/<USER>/transaction_model.dart';
import '/features/home/<USER>/home_service.dart';

// 定义交易类型枚举，方便在UI和Notifier中使用
enum TransactionFeedType { all, expense, income }

// Provider 用于存储当前选中的Tab (交易类型)
// 使用 StateProvider 因为它的状态可以直接被UI修改和监听
final currentTransactionFeedTypeProvider = StateProvider<TransactionFeedType>((ref) => TransactionFeedType.all);

// 当前日历显示的年月
final currentDisplayMonthProvider = StateProvider<DateTime>((ref) {
  final now = DateTime.now();
  return DateTime(now.year, now.month);
});

// 日历中选中的日期
final selectedDateProvider = StateProvider<DateTime?>((ref) => null);

// Provider 获取指定月份的完整日历数据
final calendarMonthDataProvider = FutureProvider.autoDispose.family<CalendarMonthData, DateTime>((ref, monthYear) async {
  // 1. 监听 token
  final token = ref.watch(authTokenProvider);
  // 2. 如果未登录 (token 为 null)，则不执行API调用
  //    可以直接返回一个空对象、抛出异常，或者返回一个特定的状态。
  //    抛出异常是比较清晰的方式。
  if (token == null) {
    throw Exception('用户未登录，无法加载日历数据。');
    // 或者 return CalendarMonthData.empty(); // 如果你有一个空状态工厂
  }
  // 3. 如果已登录，则正常执行API调用
  final homeService = ref.watch(homeServiceProvider);
  return homeService.getCalendarMonthDetails(monthYear.year, monthYear.month);
});

// Provider 获取选中日期的交易记录 (用于底部Modal)
// 使用 .autoDispose确保在不再需要时释放资源
final transactionsForSelectedDateProvider = FutureProvider.autoDispose.family<List<TransactionModel>, DateTime>((ref, date) async {
  // 如果没有日期被选中，可以返回空列表或抛出错误，这里我们期望调用时一定有日期
  final homeService = ref.watch(homeServiceProvider);
  return homeService.getTransactionsForDate(date);
});

// --- 交易 Feed 相关 ---
class TransactionFeedState {
  final List<TransactionModel> transactions;
  final bool isLoadingMore;
  final bool hasReachedMax;
  final int currentPage;
  final String? errorMessage; // 可选：添加错误消息状态

  TransactionFeedState({this.transactions = const [], this.isLoadingMore = false, this.hasReachedMax = false, this.currentPage = 1, this.errorMessage});

  TransactionFeedState copyWith({
    List<TransactionModel>? transactions,
    bool? isLoadingMore,
    bool? hasReachedMax,
    int? currentPage,
    String? errorMessage,
    bool clearErrorMessage = false, // 用于清除错误消息
  }) {
    return TransactionFeedState(
      transactions: transactions ?? this.transactions,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      errorMessage: clearErrorMessage ? null : errorMessage ?? this.errorMessage,
    );
  }
}

class TransactionFeedNotifier extends StateNotifier<TransactionFeedState> {
  final Ref _ref;

  // Notifier 现在不需要自己去监听 authProvider 了
  TransactionFeedNotifier(this._ref) : super(TransactionFeedState()) {
    // 监听 currentTransactionFeedTypeProvider 和 selectedDateProvider 的变化
    // 当 tab 切换或日期选择变化时，自动刷新数据
    _ref.listen<TransactionFeedType>(currentTransactionFeedTypeProvider, (previous, next) {
      if (previous != next) {
        // 当 Tab 类型改变时，我们应该清除选中的日期，恢复到默认的 Feed 视图
        _ref.read(selectedDateProvider.notifier).state = null;
        // refreshFeed 会读取最新的 selectedDateProvider (此刻为null) 和 currentFeedType
        refreshFeed();
      }
    });

    _ref.listen<DateTime?>(selectedDateProvider, (previous, next) {
      // 只有在日期实际发生变化时才刷新
      // 如果日期从某个值变为null（例如切换Tab清除了日期），也需要刷新以显示默认Feed
      // 如果日期从null变为某个值（用户点击日历），也需要刷新
      // 如果日期从一个值变为另一个值（用户点击另一个日期），也需要刷新
      if (previous != next) {
        // refreshFeed 会读取最新的 selectedDateProvider 和 currentFeedType
        refreshFeed();
      }
    });
  }

  String? _mapFeedTypeToApiString(TransactionFeedType feedType) {
    switch (feedType) {
      case TransactionFeedType.expense:
        return 'expense';
      case TransactionFeedType.income:
        return 'income';
      case TransactionFeedType.all:
      default:
        return null; // "all" 类型不传递 type 参数
    }
  }

  Future<void> _fetchInitialTransactions({bool isRefresh = false}) async {
    final currentFeedType = _ref.read(currentTransactionFeedTypeProvider);
    final selectedDate = _ref.read(selectedDateProvider); // 读取当前选中的日期
    final apiTypeString = _mapFeedTypeToApiString(currentFeedType);
    final apiDateString = selectedDate != null ? DateFormat('yyyy-MM-dd').format(selectedDate) : null;

    // 只有在不是由 selectedDate 变化触发的 refresh (即 tab 切换或下拉刷新)
    // 并且当前不是特定日期视图时，才清空列表。
    // 如果是特定日期视图（即 apiDateString != null），我们总是希望清空并加载新日期的数据。
    List<TransactionModel> initialTransactions = [];
    if (isRefresh || apiDateString != null || state.transactions.isEmpty) {
      // 如果是下拉刷新、查看特定日期、或者列表本来就是空的（首次加载），则清空
      state = state.copyWith(
        isLoadingMore: true,
        currentPage: 1,
        transactions: [],
        // 清空现有数据
        hasReachedMax: false,
        clearErrorMessage: true,
      );
    } else {
      // 如果只是分页加载（不是刷新也不是特定日期切换），不清空现有 transactions
      state = state.copyWith(isLoadingMore: true, clearErrorMessage: true);
    }
    try {
      final homeService = _ref.read(homeServiceProvider);
      final newTransactions = await homeService.getTransactionFeed(
        page: 1, // 总是从第一页开始加载初始数据
        type: apiTypeString,
        date: apiDateString, // <--- 传递日期参数
      );
      if (mounted) {
        // 检查 notifier 是否仍然 mounted
        state = state.copyWith(transactions: newTransactions, isLoadingMore: false, hasReachedMax: newTransactions.isEmpty, currentPage: 1);
      }
    } catch (e) {
      print("Error fetching initial transaction feed for type $apiTypeString: $e");
      if (mounted) {
        state = state.copyWith(
          isLoadingMore: false,
          hasReachedMax: true, // 标记为已达最大以防止继续加载
          errorMessage: e.toString(), // 或者更友好的错误消息
        );
      }
    }
  }

  Future<void> fetchMoreTransactions() async {
    if (state.isLoadingMore || state.hasReachedMax) return;

    final currentFeedType = _ref.read(currentTransactionFeedTypeProvider);
    final selectedDate = _ref.read(selectedDateProvider);
    final apiTypeString = _mapFeedTypeToApiString(currentFeedType);
    final apiDateString = selectedDate != null ? DateFormat('yyyy-MM-dd').format(selectedDate) : null;

    state = state.copyWith(isLoadingMore: true, clearErrorMessage: true);
    final nextPage = state.currentPage + 1;
    try {
      final homeService = _ref.read(homeServiceProvider);
      final newTransactions = await homeService.getTransactionFeed(
        page: nextPage,
        type: apiTypeString, // 传递类型参数
        date: apiDateString, // <--- 传递日期参数
      );
      if (mounted) {
        if (newTransactions.isEmpty) {
          state = state.copyWith(hasReachedMax: true, isLoadingMore: false);
        } else {
          state = state.copyWith(transactions: [...state.transactions, ...newTransactions], isLoadingMore: false, currentPage: nextPage);
        }
      }
    } catch (e) {
      print("Error fetching more transactions for type $apiTypeString: $e");
      if (mounted) {
        state = state.copyWith(
          isLoadingMore: false,
          hasReachedMax: true, // 出错时也标记为已达最大
          errorMessage: e.toString(),
        );
      }
    }
  }

  Future<void> refreshFeed() async {
    await _fetchInitialTransactions(isRefresh: true);
  }
}

// transactionFeedNotifierProvider 的定义 (当依赖改变时，autoDispose 会重新创建 Notifier)
final transactionFeedNotifierProvider = StateNotifierProvider.autoDispose<TransactionFeedNotifier, TransactionFeedState>((ref) {

  // --- 关键：在这里也建立对认证状态的依赖 ---
  final token = ref.watch(authTokenProvider);

  // 创建 Notifier 实例
  final notifier = TransactionFeedNotifier(ref);

  // 当 Provider 被创建时（或 token 从 null 变为有值导致重建时），
  // 并且已登录，就触发一次初始加载。
  if (token != null) {
    notifier.refreshFeed(); // 或者 _fetchInitialTransactions()
  }
  return notifier;
});
