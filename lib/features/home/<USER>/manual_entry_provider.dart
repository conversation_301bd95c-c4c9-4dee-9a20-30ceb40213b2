import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/transaction_model.dart';
import '../services/home_service.dart';

part 'manual_entry_provider.freezed.dart';

@freezed
abstract class ManualEntryState with _$ManualEntryState {
  const factory ManualEntryState({
    @Default(false) bool isLoading,
    @Default(false) bool isSubmitting,
    String? error,
    String? successMessage,
  }) = _ManualEntryState;
}

@freezed
abstract class TransactionFormData with _$TransactionFormData {
  const factory TransactionFormData({
    @Default(0.0) double amount,
    @Default('') String category,
    @Default('') String categoryId,
    @Default('') String description,
    @Default('') String merchantName,
    @Default([]) List<String> participants,
    @Default('') String location,
    @Default('') String paymentMethod,
    @Default('') String paymentMethodId,
    DateTime? selectedDate,
    @Default('') String notes,
    @Default('') String photoPath,
    @Default('') String moodEmoji,
    @Default('expense') String type, // 添加类型字段：expense 或 income
    // 转账特有字段已移除，统一使用通用字段
  }) = _TransactionFormData;
}

class ManualEntryNotifier extends StateNotifier<ManualEntryState> {
  ManualEntryNotifier(this._homeService) : super(const ManualEntryState());

  final HomeService _homeService;

  // 创建支出记录
  Future<void> createExpense(TransactionFormData formData) async {
    await _createTransaction(formData, TransactionType.expense);
  }

  // 创建收入记录
  Future<void> createIncome(TransactionFormData formData) async {
    await _createTransaction(formData, TransactionType.income);
  }

  // 转账功能已移除，统一处理所有交易类型

  Future<void> _createTransaction(TransactionFormData formData, TransactionType type) async {
    state = state.copyWith(isSubmitting: true, error: null);

    try {
      // 构建交易数据
      final transactionData = {
        'type': type.name,
        'amount': formData.amount,
        'category': formData.category,
        'categoryId': formData.categoryId,
        'description': formData.description,
        'merchantName': formData.merchantName,
        'participants': formData.participants,
        'location': formData.location,
        'paymentMethod': formData.paymentMethod,
        'paymentMethodId': formData.paymentMethodId,
        'timestamp': (formData.selectedDate ?? DateTime.now()).toIso8601String(),
        'notes': formData.notes,
        'photoPath': formData.photoPath,
        'moodEmoji': formData.moodEmoji,
        'isAiBuild': false, // 标记为手动创建
        'isManualEntry': true, // 新增字段标识手动记账
      };

      // 转账特殊字段处理已移除，统一处理所有交易类型

      // 调用API创建交易
      await _homeService.createTransaction(transactionData);

      state = state.copyWith(
        isSubmitting: false,
        successMessage: '记账成功！',
      );
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: '记账失败：${e.toString()}',
      );
    }
  }

  // 清除错误和成功消息
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }

  // 重置状态
  void reset() {
    state = const ManualEntryState();
  }
}

// Provider
final manualEntryProvider = StateNotifierProvider<ManualEntryNotifier, ManualEntryState>((ref) {
  final homeService = ref.watch(homeServiceProvider);
  return ManualEntryNotifier(homeService);
});

// 所有表单相关的Provider已移除，现在使用manual_entry_page.dart中的本地状态管理
