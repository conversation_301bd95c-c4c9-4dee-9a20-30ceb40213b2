// features/home/<USER>/transaction_model.dart
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:forui/forui.dart';

part 'transaction_model.freezed.dart';
part 'transaction_model.g.dart';

// 交易类型枚举
enum TransactionType {
  expense, // 支出
  income, // 收入
}
// 新增：用于表示共享用户信息
@freezed
abstract class SharedUserInfo with _$SharedUserInfo {
  const factory SharedUserInfo({
    required String userId,
    required String avatarUrl, // 用户头像URL
    String? username,    // 可选的显示名称
  }) = _SharedUserInfo;

  factory SharedUserInfo.fromJson(Map<String, dynamic> json) =>
      _$SharedUserInfoFromJson(json);
}

// AccountInfo已移除，不再使用

// 新增：现金来源信息
@freezed
abstract class CashSourceInfo with _$CashSourceInfo {
  const factory CashSourceInfo({
    required String id,
    required String name,
  }) = _CashSourceInfo;

  factory CashSourceInfo.fromJson(Map<String, dynamic> json) =>
      _$CashSourceInfoFromJson(json);
}

// 新增：金额显示信息
@freezed
abstract class AmountDisplay with _$AmountDisplay {
  const factory AmountDisplay({
    required String sign,
    required String value,
    required String currencySymbol,
    required String fullString,
  }) = _AmountDisplay;

  factory AmountDisplay.fromJson(Map<String, dynamic> json) =>
      _$AmountDisplayFromJson(json);
}

@freezed
abstract class TransactionModel with _$TransactionModel {
  const factory TransactionModel({
    required String id, // 唯一ID
    required TransactionType type, // 交易类型 (支出/收入/转账)
    required String category, // 类别 (例如：餐饮, 交通, 工资)
    String? categoryId, // 分类ID
    String? categoryText, // 服务端本地化的分类名称
    required String iconUrl, // 类别对应的图标URL (或本地资源路径)
    required double amount, // 金额
    required DateTime timestamp, // 交易时间
    String? description, // 描述 (可选)
    String? userComment, // 用户添加的评语 (可选)
    @Default(0) int shareCount, // 分享次数
    @Default(0) int commentCount, // 评论次数 (这里的评论可能指社区评论，如果只是用户评语用userComment)
    @Default(false) bool isShared,
    @Default([]) List<SharedUserInfo> sharedWith,
    String? paymentMethod,
    String? paymentMethodText, // 服务端本地化的支付方式名称
    String? location,
    @Default([]) List<String> tags,
    @JsonKey(name: 'is_ai_build') // 明确指定JSON字段名以匹配数据库
    @Default(true) bool isAiBuild,

    // === 转账相关字段已移除 ===

    // === 现金来源字段 ===
    CashSourceInfo? cashSource, // 现金来源信息

    // === 金额显示字段 ===
    AmountDisplay? display, // 金额显示信息

    // === 时间戳字段 ===
    DateTime? createdAt, // 创建时间
    DateTime? updatedAt, // 更新时间

    // === 足迹模块新增字段 ===
    String? aiNarrativeTitle, // AI生成的叙事标题，如"在街角咖啡店，享受了一个人的下午茶"
    String? photoPath, // 用户添加的照片路径
    String? moodEmoji, // 心情表情
    @Default([]) List<String> participants, // 参与人员，如["小明", "小红"]
    String? merchantName, // 商家名称
    String? geoLocation, // 地理位置信息
    @Default(false) bool hasStoryContent, // 是否包含故事内容（照片或心情）

  }) = _TransactionModel;

  factory TransactionModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionModelFromJson(json);

  // 专门用于处理API返回的数据格式
  factory TransactionModel.fromApiJson(Map<String, dynamic> json) {
    // 解析交易类型
    TransactionType type;
    final typeStr = json['type'] as String?;
    switch (typeStr?.toLowerCase()) {
      case 'income':
        type = TransactionType.income;
        break;
      case 'expense':
      default:
        type = TransactionType.expense;
        break;
    }

    // 转账相关字段已移除

    // 解析现金来源信息
    CashSourceInfo? cashSource;
    if (json['cashSource'] != null) {
      cashSource = CashSourceInfo.fromJson(json['cashSource'] as Map<String, dynamic>);
    }

    // 解析金额显示信息
    AmountDisplay? display;
    if (json['display'] != null) {
      display = AmountDisplay.fromJson(json['display'] as Map<String, dynamic>);
    }

    return TransactionModel(
      id: json['id'] as String,
      type: type,
      category: json['category'] as String? ?? '其他',
      categoryId: json['categoryId'] as String?,
      categoryText: json['categoryText'] as String?, // 服务端本地化的分类名称
      iconUrl: json['iconUrl'] as String? ?? '', // 使用API返回的图标URL
      amount: _parseAmount(json['amount']), // 解析amount字段，支持字符串和数字
      timestamp: DateTime.tryParse(json['timestamp'] as String? ?? '') ?? DateTime.now(),
      description: json['description'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      paymentMethodText: json['paymentMethodText'] as String?, // 服务端本地化的支付方式
      location: json['location'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      commentCount: json['commentCount'] as int? ?? 0,
      isShared: json['isShared'] as bool? ?? false,

      cashSource: cashSource,
      display: display,
      createdAt: DateTime.tryParse(json['createdAt'] as String? ?? ''),
      updatedAt: DateTime.tryParse(json['updatedAt'] as String? ?? ''),
      isAiBuild: json['isAiBuild'] as bool? ?? false,
    );
  }
}

const Map<String, IconData> lucideIconMap = {
  'shoppingBag': FIcons.shoppingCart,
  'carTaxiFront': FIcons.carTaxiFront,
  'briefcaseMedical': FIcons.briefcaseMedical,
  'housePlus': FIcons.housePlus,
  'libraryBig': FIcons.libraryBig,
  'walletCards': FIcons.walletCards,
  'gift': FIcons.gift,
  'folderSync': FIcons.folderSync
};
IconData lucideIconFromString(String raw) {
  // 允许 "LucideIcons.shoppingCart" 或 "shoppingCart"
  final key = raw.replaceAll('LucideIcons.', '');
  return lucideIconMap[key] ?? FIcons.shoppingBag;
}

// TransactionType 序列化/反序列化
TransactionType _transactionTypeFromString(String typeStr) {
  switch (typeStr.toLowerCase()) {
    case 'income':
      return TransactionType.income;
    case 'expense':
    default:
      return TransactionType.expense;
  }
}

String _transactionTypeToString(TransactionType type) {
  switch (type) {
    case TransactionType.income:
      return 'income';
    case TransactionType.expense:
      return 'expense';
  }
}

// DateTime 序列化/反序列化 (只取日期部分，如果API需要完整时间戳则用 .toIso8601String())
String _dateTimeToIso8601String(DateTime dt) => dt.toIso8601String(); // API通常期望完整ISO字符串

// 解析金额字段，支持字符串和数字格式
double _parseAmount(dynamic value) {
  if (value == null) return 0.0;
  if (value is num) return value.toDouble();
  if (value is String) {
    // 移除可能的符号和货币符号
    final cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
    return double.tryParse(cleanValue) ?? 0.0;
  }
  return 0.0;
}

// 如果你的API对于 is_ai_build 返回的可能是 0/1 而不是 true/false，你可能需要一个自定义的 fromJson
// bool _boolFromInt(dynamic value) => value == 1 || value == true;