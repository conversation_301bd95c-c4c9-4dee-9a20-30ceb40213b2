// features/home/<USER>/transaction_detail_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction_model.dart';
import '../services/home_service.dart';

// 交易详情状态
class TransactionDetailState {
  final TransactionModel? transaction;
  final bool isLoading;
  final String? errorMessage;

  const TransactionDetailState({
    this.transaction,
    this.isLoading = false,
    this.errorMessage,
  });

  TransactionDetailState copyWith({
    TransactionModel? transaction,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
  }) {
    return TransactionDetailState(
      transaction: transaction ?? this.transaction,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }
}

// 交易详情 StateNotifier
class TransactionDetailNotifier extends StateNotifier<TransactionDetailState> {
  final Ref _ref;

  TransactionDetailNotifier(this._ref) : super(const TransactionDetailState());

  // 获取交易详情
  Future<void> fetchTransactionDetail(String transactionId) async {
    if (state.isLoading) return; // 防止重复请求

    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final homeService = _ref.read(homeServiceProvider);
      final transaction = await homeService.getTransactionDetail(transactionId);
      
      if (mounted) {
        state = state.copyWith(
          transaction: transaction,
          isLoading: false,
        );
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: e.toString(),
        );
      }
    }
  }

  // 重新加载
  Future<void> reload(String transactionId) async {
    await fetchTransactionDetail(transactionId);
  }
}

// Provider for TransactionDetailNotifier
final transactionDetailProvider = StateNotifierProvider.family<TransactionDetailNotifier, TransactionDetailState, String>(
  (ref, transactionId) {
    final notifier = TransactionDetailNotifier(ref);
    // 自动加载数据
    Future.microtask(() => notifier.fetchTransactionDetail(transactionId));
    return notifier;
  },
);
