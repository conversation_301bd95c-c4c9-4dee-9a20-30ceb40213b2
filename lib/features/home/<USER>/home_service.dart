// features/home/<USER>/home_service.dart
import 'dart:math';

import 'package:flutter_expense_tracker/core/network/network_client.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '/features/home/<USER>/daily_expense_summary_model.dart';
import '/features/home/<USER>/transaction_model.dart';

import '/core/network/exceptions/app_exception.dart';

class HomeService {
  final NetworkClient _networkClient;

  HomeService(this._networkClient);

  // 获取指定月份的每日消费概要 (用于日历热力图)
  Future<CalendarMonthData> getCalendarMonthDetails(int year, int month) async {
    // 现在，Service 方法可以直接调用 networkClient.request，无需 try-catch DioException
    // 如果发生错误，networkClient.request 会抛出 AppException 或 Exception
    // Service 层可以选择不捕获，让错误直接传递到 Provider 和 UI 层进行统一处理
    return await _networkClient.request<CalendarMonthData>(
      '/home/<USER>', // API端点
      method: HttpMethod.get,
      queryParameters: {'year': year, 'month': month},
      // 提供 CalendarMonthData 的 fromJson 工厂方法给拦截器
      fromJsonT: (json) => CalendarMonthData.fromJson(json as Map<String, dynamic>),
    );
    // 注意：之前的 try-catch 块完全被移除了。
    // 如果你想在特定的 Service 方法中对某种 AppException 进行特殊处理（比如日志），
    // 你仍然可以在这里添加 try-catch (AppException e) { ... rethrow; }，
    // 但通常不需要。
  }

  // 修改此方法以从API获取数据
  Future<List<TransactionModel>> getTransactionFeed({
    int page = 1,
    int limit = 10,
    String? type,
    String? date, // (YYYY-MM-DD)
  }) async {
    // NetworkClient.request 会处理通用的 try-catch 和异常转换
    // fromJsonT 回调函数负责将API响应的 data 部分转换为 List<TransactionModel>
    final Map<String, dynamic> queryParameters = {'page': page, 'limit': limit};
    if (type != null && type.isNotEmpty) {
      queryParameters['type'] = type; // 如果提供了type，则添加到查询参数
    }
    if (date != null && date.isNotEmpty) {
      queryParameters['date'] = date; // 如果提供了date，则添加到查询参数
    }
    return await _networkClient.request<List<TransactionModel>>(
      '/home/<USER>',
      method: HttpMethod.get,
      queryParameters: queryParameters,
      fromJsonT: (json) {
        // 新的API响应格式包含 data 和 meta 字段
        if (json is Map<String, dynamic>) {
          final data = json['data'];
          if (data is List) {
            try {
              return data.map((item) => TransactionModel.fromApiJson(item as Map<String, dynamic>)).toList();
            } catch (e) {
              throw DataParsingException("解析交易列表中的项目失败: ${e.toString()}");
            }
          }
          throw DataParsingException("API /home/<USER>");
        }
        // 向后兼容：如果直接返回数组格式
        if (json is List) {
          try {
            return json.map((item) => TransactionModel.fromApiJson(item as Map<String, dynamic>)).toList();
          } catch (e) {
            throw DataParsingException("解析交易列表中的项目失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /home/<USER>");
      },
    );
  }

  // 获取指定日期的交易记录 (用于底部Modal)
  Future<List<TransactionModel>> getTransactionsForDate(DateTime date) async {
    // TODO: 实现API调用
    // final response = await _dio.get('/home/<USER>', queryParameters: {'date': date.toIso8601String().substring(0,10)});
    // return (response.data as List).map((item) => TransactionModel.fromJson(item)).toList();

    // 静态数据模拟
    await Future.delayed(const Duration(milliseconds: 300));
    final random = Random();
    if (random.nextDouble() < 0.3 && date.day % 5 != 0) return []; // 模拟有些天没数据
    return List.generate(random.nextInt(5) + 1, (index) {
      final type = random.nextBool() ? TransactionType.expense : TransactionType.income;
      return TransactionModel(
        id: 'txn-${date.toIso8601String()}-$index',
        type: type,
        category: type == TransactionType.expense ? ['餐饮', '交通', '购物'][random.nextInt(3)] : '工资',
        iconUrl: 'assets/images/icons/default_category.png',
        // 替换为真实图标路径
        amount: random.nextDouble() * 100 + 10,
        timestamp: date.add(Duration(hours: random.nextInt(23), minutes: random.nextInt(59))),
        description: random.nextBool() ? '这是一条${type == TransactionType.expense ? "支出" : "收入"}描述' : null,
      );
    });
  }

  // 获取交易详情
  Future<TransactionModel> getTransactionDetail(String transactionId) async {
    // 真实API调用
    return await _networkClient.request<TransactionModel>(
      '/transactions/$transactionId',
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return TransactionModel.fromApiJson(json);
          } catch (e) {
            throw DataParsingException("解析交易详情失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /transactions/$transactionId 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }

  // 创建交易记录
  Future<TransactionModel> createTransaction(Map<String, dynamic> transactionData) async {
    return await _networkClient.request<TransactionModel>(
      '/transactions',
      method: HttpMethod.post,
      data: transactionData,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          try {
            return TransactionModel.fromApiJson(json);
          } catch (e) {
            throw DataParsingException("解析创建的交易失败: ${e.toString()}");
          }
        }
        throw DataParsingException("API /transactions 期望返回一个对象，但收到了 ${json.runtimeType}");
      },
    );
  }
}

// Provider for HomeService
final homeServiceProvider = Provider<HomeService>((ref) {
  final networkClient = ref.watch(networkClientProvider); // 假设你已配置好 dioProvider
  return HomeService(networkClient);
});

// 解释和优点：
// 集中错误处理: 所有的 DioException 捕获、检查 e.error is AppException、以及对未映射错误的备用处理都集中在 NetworkClient.request 方法中。
// Service 层简洁: Service 方法现在非常干净，只关注于定义请求的参数（路径、方法、数据、类型转换逻辑）和调用 _networkClient.request。
// 强类型和一致性: 错误现在会以更一致的方式（主要是 AppException 的子类）从网络层抛出。
// 依赖拦截器: 这个方案仍然高度依赖你的 ErrorInterceptor（负责将 DioException.error 填充为 AppException）和 ApiResponseInterceptor（负责处理业务响应码 code 并转换 data）。NetworkClient 是在这些拦截器工作的基础上进行封装的。
// 可维护性: 如果将来需要修改通用的网络错误处理逻辑（例如，增加全局的错误上报），你只需要修改 NetworkClient.request 方法。
// 关于你的 ErrorInterceptor 和 ApiResponseInterceptor 的重要性：
// ErrorInterceptor: 它的核心职责是将各种 DioException（网络超时、连接错误、HTTP 4xx/5xx 错误等）转换为你定义的 AppException 子类（如 NetworkException, BadRequestException, NotFoundException, InternalServerErrorException 等），并将这个 AppException 实例赋值给 dioException.error 字段。
// ApiResponseInterceptor: 它的核心职责是处理你的API特定响应结构 {code, message, data}。
// 如果 code != 0，它应该构造并抛出一个 BusinessErrorException（一个 AppException 子类）。这个抛出通常会通过 handler.reject(DioException(error: businessException, ...)) 来完成。
// 如果 code == 0，它应该使用传入的 fromJsonT 回调函数将 responseMap['data'] 部分解析成期望的 T 类型，并将 response.data 替换为这个解析后的 T 实例，然后调用 handler.next(response)。
// 当这两个拦截器正确工作时，NetworkClient 中的逻辑会非常顺畅：
// dio.request 调用。
// 如果发生HTTP错误或网络错误，ErrorInterceptor 会介入，将 DioException.error 填充为 AppException。然后 DioException 会被 NetworkClient 的 catch (DioException e) 块捕获，并重新抛出 e.error。
// 如果HTTP请求成功（2xx），ApiResponseInterceptor 会介入。
// 如果业务错误 (code != 0)，它会 reject 一个带有 BusinessErrorException 的 DioException。这个 DioException 同样会被 NetworkClient 的 catch (DioException e) 块捕获，并重新抛出 BusinessErrorException。
// 如果业务成功 (code == 0)，它会修改 response.data 为 T 类型并 next。然后 NetworkClient 中的 if (response.data is T) 检查通过，直接返回 response.data。
// 这种分层封装使得每一层都有清晰的职责，是构建健壮网络请求层的良好实践。
