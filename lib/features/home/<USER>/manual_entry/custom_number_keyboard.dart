import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

/// 自定义数字键盘组件
/// 专为记账金额输入设计，提供友好的数字输入体验
class CustomNumberKeyboard extends StatelessWidget {
  final Function(String) onKeyTap;
  final VoidCallback? onDeleteTap;
  final VoidCallback? onConfirmTap;
  final bool showDecimal;
  final String confirmText;

  const CustomNumberKeyboard({
    super.key,
    required this.onKeyTap,
    this.onDeleteTap,
    this.onConfirmTap,
    this.showDecimal = true,
    this.confirmText = '确认',
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.fromLTRB(8, 6, 8, 8),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
        border: Border(
          top: BorderSide(color: colors.border, width: 0.5),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 第一行: 1, 2, 3
          Row(
            children: [
              _buildKey('1', context),
              _buildKey('2', context),
              _buildKey('3', context),
            ],
          ),
          const SizedBox(height: 4),

          // 第二行: 4, 5, 6
          Row(
            children: [
              _buildKey('4', context),
              _buildKey('5', context),
              _buildKey('6', context),
            ],
          ),
          const SizedBox(height: 4),

          // 第三行: 7, 8, 9
          Row(
            children: [
              _buildKey('7', context),
              _buildKey('8', context),
              _buildKey('9', context),
            ],
          ),
          const SizedBox(height: 4),

          // 第四行: 小数点/空白, 0, 删除
          Row(
            children: [
              showDecimal
                ? _buildKey('.', context)
                : _buildEmptyKey(context),
              _buildKey('0', context),
              _buildDeleteKey(context),
            ],
          ),

          if (onConfirmTap != null) ...[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              height: 48, // 增加按钮高度
              child: FButton(
                style: FButtonStyle.primary(),
                onPress: onConfirmTap,
                child: Text(
                  confirmText,
                  style: theme.typography.sm.copyWith( // 使用更小的字体
                    fontWeight: FontWeight.w600,
                    color: colors.primaryForeground,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildKey(String value, BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2),
        child: GestureDetector(
          onTap: () => onKeyTap(value),
          child: Container(
            height: 36,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: colors.secondary,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: colors.border, width: 0.5),
            ),
            child: Text(
              value,
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w600,
                color: colors.foreground,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteKey(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2),
        child: GestureDetector(
          onTap: onDeleteTap,
          child: Container(
            height: 36,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: colors.secondary,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: colors.border, width: 0.5),
            ),
            child: Icon(
              FIcons.delete,
              size: 16,
              color: colors.destructive,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyKey(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2),
        child: Container(
          height: 36,
        ),
      ),
    );
  }
}

/// 数字键盘控制器
/// 管理数字输入的状态和逻辑
class NumberKeyboardController extends ChangeNotifier {
  String _value = '';
  final int maxLength;
  final int decimalPlaces;

  NumberKeyboardController({
    this.maxLength = 10,
    this.decimalPlaces = 2,
  });

  String get value => _value;
  
  double get doubleValue => double.tryParse(_value) ?? 0.0;

  bool get isEmpty => _value.isEmpty;

  void onKeyTap(String key) {
    if (key == '.') {
      _handleDecimalPoint();
    } else {
      _handleDigit(key);
    }
    notifyListeners();
  }

  void onDeleteTap() {
    if (_value.isNotEmpty) {
      _value = _value.substring(0, _value.length - 1);
      notifyListeners();
    }
  }

  void clear() {
    _value = '';
    notifyListeners();
  }

  void setValue(String value) {
    _value = value;
    notifyListeners();
  }

  void _handleDecimalPoint() {
    // 如果已经有小数点，忽略
    if (_value.contains('.')) return;
    
    // 如果为空，添加 "0."
    if (_value.isEmpty) {
      _value = '0.';
    } else {
      _value += '.';
    }
  }

  void _handleDigit(String digit) {
    // 检查是否超过最大长度
    if (_value.length >= maxLength) return;
    
    // 检查小数位数限制
    if (_value.contains('.')) {
      final parts = _value.split('.');
      if (parts.length > 1 && parts[1].length >= decimalPlaces) {
        return;
      }
    }
    
    // 如果当前值是 "0"，替换为新数字（除非是小数点）
    if (_value == '0') {
      _value = digit;
    } else {
      _value += digit;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
