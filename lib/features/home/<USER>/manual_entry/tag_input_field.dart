import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';


/// 标签输入字段组件
/// 支持输入自定义标签并以Badge形式显示
class TagInputField extends ConsumerStatefulWidget {
  final List<String> initialTags;
  final Function(List<String>) onTagsChanged;
  final String? label;
  final String? hint;
  final int maxTags;

  const TagInputField({
    super.key,
    this.initialTags = const [],
    required this.onTagsChanged,
    this.label,
    this.hint,
    this.maxTags = 5,
  });

  @override
  ConsumerState<TagInputField> createState() => TagInputFieldState();
}

class TagInputFieldState extends ConsumerState<TagInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _tags = [];

  @override
  void initState() {
    super.initState();
    _tags = List.from(widget.initialTags);
  }

  @override
  void didUpdateWidget(TagInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当initialTags发生变化时，更新内部状态
    if (!_listEquals(oldWidget.initialTags, widget.initialTags)) {
      setState(() {
        _tags = List.from(widget.initialTags);
      });
    }
  }

  // 辅助方法：比较两个列表是否相等
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isEmpty) return;
    if (_tags.contains(trimmedTag)) return;
    if (_tags.length >= widget.maxTags) return;

    setState(() {
      _tags.add(trimmedTag);
    });
    
    _controller.clear();
    widget.onTagsChanged(_tags);
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
    widget.onTagsChanged(_tags);
  }



  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: theme.typography.sm.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // 标签显示区域
        if (_tags.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _tags.map((tag) => _buildTagBadge(tag, context)).toList(),
          ),
          const SizedBox(height: 12),
        ],
        
        // 输入框
        FTextField(
          controller: _controller,
          focusNode: _focusNode,
          hint: widget.hint ?? '输入标签后按回车添加',
          enabled: _tags.length < widget.maxTags,
          onEditingComplete: () => _addTag(_controller.text),
          suffixBuilder: _tags.length < widget.maxTags
            ? (context, style, child) => Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FButton.icon(
                  style: FButtonStyle.ghost(),
                  onPress: () => _addTag(_controller.text),
                  child: Icon(
                    FIcons.plus,
                    size: 16,
                    color: colors.primary,
                  ),
                ),
              )
            : null,
        ),
        
        // 提示文本
        if (_tags.length >= widget.maxTags) ...[
          const SizedBox(height: 4),
          Text(
            '最多添加 ${widget.maxTags} 个标签',
            style: theme.typography.xs.copyWith(
              color: colors.mutedForeground,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTagBadge(String tag, BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return FBadge.raw(
      style: FBadgeStyle.secondary(),
      builder: (context, style) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: colors.secondary,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: colors.border),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              tag,
              style: theme.typography.sm.copyWith(
                color: colors.secondaryForeground,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            GestureDetector(
              onTap: () => _removeTag(tag),
              child: Icon(
                FIcons.x,
                size: 14,
                color: colors.secondaryForeground.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 预设标签建议组件
class TagSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onTagSelected;
  final List<String> selectedTags;

  const TagSuggestions({
    super.key,
    required this.suggestions,
    required this.onTagSelected,
    this.selectedTags = const [],
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    final availableSuggestions = suggestions
        .where((tag) => !selectedTags.contains(tag))
        .toList();

    // 调试信息
    print('TagSuggestions: suggestions=${suggestions.length}, selectedTags=${selectedTags.length}, available=${availableSuggestions.length}');

    if (availableSuggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '常用标签',
          style: theme.typography.sm.copyWith(
            fontWeight: FontWeight.w500,
            color: colors.mutedForeground,
          ),
        ),
        const SizedBox(height: 6),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: availableSuggestions.map((tag) {
            return GestureDetector(
              onTap: () => onTagSelected(tag),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: colors.secondary,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: colors.border, width: 0.5),
                ),
                child: Text(
                  tag,
                  style: theme.typography.xs.copyWith(
                    color: colors.secondaryForeground,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// 常用标签预设
class CommonTags {
  static const List<String> expense = [
    '必需品',
    '娱乐',
    '紧急',
    '计划内',
    '冲动消费',
    '优惠',
    '团购',
    '打折',
  ];

  static const List<String> income = [
    '主要收入',
    '副业',
    '投资',
    '奖金',
    '礼金',
    '退款',
    '兼职',
    '分红',
  ];

  static List<String> getTagsForType(bool isExpense) {
    return isExpense ? expense : income;
  }
}
