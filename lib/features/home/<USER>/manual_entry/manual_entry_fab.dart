import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

/// 简化的手动记账悬浮按钮
/// 直接跳转到记账页面，不再使用速度拨号
class ManualEntryFab extends ConsumerWidget {
  const ManualEntryFab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;

    return SizedBox(
      width: 56,
      height: 56,
      child: FButton.icon(
        onPress: () => context.push('/manual-entry?type=expense'),
        style: FButtonStyle.primary(),
        child: Icon(
          FIcons.plus,
          size: 24,
          color: colors.primaryForeground,
        ),
      ),
    );
  }
}
