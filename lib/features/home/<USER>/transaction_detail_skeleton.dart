// features/home/<USER>/transaction_detail_skeleton.dart
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:forui/forui.dart';

class TransactionDetailSkeleton extends StatelessWidget {
  const TransactionDetailSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    
    // Shimmer 效果的颜色 (参考交易卡片骨架屏设计)
    final Color shimmerBaseColor = Colors.grey[200]!;
    final Color shimmerHighlightColor = Colors.grey[50]!;
    final Color placeholderShapeColor = Colors.grey[200]!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // 页面头部骨架
            _buildHeaderSkeleton(shimmerBaseColor, shimmerHighlightColor, placeholderShapeColor),
            
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                      child: FCard(
                        child: Shimmer.fromColors(
                          baseColor: shimmerBaseColor,
                          highlightColor: shimmerHighlightColor,
                          period: const Duration(milliseconds: 1200),
                          child: _buildDetailSkeleton(placeholderShapeColor),
                        ),
                      ),
                    ),
                  ),

                  // 评论区域骨架
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                      child: Shimmer.fromColors(
                        baseColor: shimmerBaseColor,
                        highlightColor: shimmerHighlightColor,
                        period: const Duration(milliseconds: 1200),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 20,
                              width: 60,
                              color: placeholderShapeColor,
                            ),
                            const SizedBox(height: 16),
                            // 评论项骨架
                            ...List.generate(2, (index) => Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      color: placeholderShapeColor,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 14,
                                          width: 80,
                                          color: placeholderShapeColor,
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          height: 12,
                                          color: placeholderShapeColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 评论输入栏骨架
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(
                    color: placeholderShapeColor,
                    width: 1,
                  ),
                ),
              ),
              child: Shimmer.fromColors(
                baseColor: shimmerBaseColor,
                highlightColor: shimmerHighlightColor,
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: placeholderShapeColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建页面头部骨架
  Widget _buildHeaderSkeleton(Color baseColor, Color highlightColor, Color placeholderColor) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Shimmer.fromColors(
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: placeholderColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                height: 20,
                color: placeholderColor,
              ),
            ),
            const SizedBox(width: 16),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: placeholderColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建详情内容骨架
  Widget _buildDetailSkeleton(Color placeholderColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 顶部：类别图标、类别名称、时间、更多操作按钮
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: placeholderColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 18,
                    width: 120,
                    color: placeholderColor,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    height: 14,
                    width: 80,
                    color: placeholderColor,
                  ),
                ],
              ),
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: placeholderColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // 金额骨架
        Center(
          child: Container(
            height: 32,
            width: 150,
            color: placeholderColor,
          ),
        ),
        const SizedBox(height: 24),

        // 分隔线
        Container(
          height: 1,
          color: placeholderColor,
        ),
        const SizedBox(height: 12),

        // 详细信息行骨架
        ...List.generate(6, (index) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: placeholderColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                height: 14,
                width: 60,
                color: placeholderColor,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  height: 14,
                  color: placeholderColor,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
}
