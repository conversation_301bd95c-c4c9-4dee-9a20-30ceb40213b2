// features/home/<USER>/daily_expense_summary_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/material.dart';

part 'daily_expense_summary_model.freezed.dart';
part 'daily_expense_summary_model.g.dart';

enum ExpenseHeatLevel { none, low, medium, high, veryHigh }


// 辅助函数：将字符串转换为 ExpenseHeatLevel 枚举
ExpenseHeatLevel _heatLevelFromString(String? levelStr) {
  switch (levelStr?.toLowerCase()) {
    case 'low': return ExpenseHeatLevel.low;
    case 'medium': return ExpenseHeatLevel.medium;
    case 'high': return ExpenseHeatLevel.high;
    case 'veryHigh': return ExpenseHeatLevel.veryHigh; // 注意后端返回可能是 veryHigh 或 very_high
    case 'none':
    default:
      return ExpenseHeatLevel.none;
  }
}

// 辅助函数：将 ExpenseHeatLevel 枚举转换为字符串 (如果需要发送给后端)
String _heatLevelToString(ExpenseHeatLevel level) => level.toString().split('.').last;

// 日期 toJson 辅助函数
String _dateTimeToIso8601String(DateTime dt) {
  return dt.toIso8601String().substring(0,10); // YYYY-MM-DD
}

@freezed
abstract class DailyExpenseSummaryModel with _$DailyExpenseSummaryModel {
  const DailyExpenseSummaryModel._();

  @JsonSerializable(explicitToJson: true) // 确保 toJson 方法也被正确生成
  const factory DailyExpenseSummaryModel({
    @JsonKey(fromJson: DateTime.parse, toJson: _dateTimeToIso8601String) // 处理日期序列化
    required DateTime date,
    required double totalExpense,
    @JsonKey(fromJson: _heatLevelFromString, toJson: _heatLevelToString) // 处理枚举序列化
    required ExpenseHeatLevel heatLevel,
  }) = _DailyExpenseSummaryModel;

  factory DailyExpenseSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$DailyExpenseSummaryModelFromJson(json);
}

// 整个月份日历数据的模型 (对应API响应的 data 部分)
@freezed
abstract class CalendarMonthData with _$CalendarMonthData {
  const factory CalendarMonthData({
    required int year,
    required int month,
    required double totalExpenseForMonth,
    required List<DailyExpenseSummaryModel> dailySummaries,
    List<String>? trendColors, // 可选
  }) = _CalendarMonthData;

  factory CalendarMonthData.fromJson(Map<String, dynamic> json) =>
      _$CalendarMonthDataFromJson(json);
}