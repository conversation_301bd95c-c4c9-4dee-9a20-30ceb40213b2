// features/transaction_detail/providers/comment_providers.dart
import 'package:flutter_expense_tracker/core/network/network_client.dart'; // 确保路径正确
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/comment_model.dart'; // 确保路径正确
import '../services/comment_service.dart'; // 确保路径正确

// Provider for CommentService
final commentServiceProvider = Provider<CommentService>((ref) {
  // 假设 networkClientProvider 已在别处定义并可 watch
  // final networkClient = ref.watch(networkClientProvider);
  // return CommentService(networkClient);
  // 或者，如果 CommentService 已经有自己的 Provider
  return ref.watch(commentServiceSingletonProvider);
});

// 假设 comment_service.dart 或其他地方定义了这个
// final commentServiceSingletonProvider = Provider<CommentService>((ref) => CommentService(ref.watch(networkClientProvider)));

// Provider for fetching comments for a specific transaction
final commentsProvider = FutureProvider.autoDispose
    .family<List<CommentModel>, String>((ref, transactionId) async {
  final commentService = ref.watch(commentServiceProvider);
  final comments = await commentService.getComments(transactionId);

  // 1. 移除客户端构建评论树的逻辑。
  //    API 现在返回扁平列表，其中子评论通过 parentCommentId 和 repliedToUserName 关联。
  //    UI 层 (CommentSectionWidget) 将负责根据 parentCommentId 来组织显示。

  // 2. 可选：对扁平列表进行排序，例如按创建时间。
  //    这可以确保在UI层处理之前，评论有一个基础的顺序。
  comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));

  return comments; // 直接返回扁平但有序的列表
});



// 定义传递给 addCommentProvider 的参数类型 (Record 类型)
typedef AddCommentParams = ({
String transactionId,
String commentText,
String? parentCommentId, // 这个仍然需要，用于告知后端这条评论是回复谁的
});

// Provider for adding a comment
final addCommentProvider = FutureProvider.autoDispose
    .family<CommentModel, AddCommentParams>((ref, params) async {
  final commentService = ref.watch(commentServiceProvider);
  final newComment = await commentService.addComment(
    transactionId: params.transactionId,
    commentText: params.commentText,
    parentCommentId: params.parentCommentId, // 后端需要这个来建立关联
  );

  // 成功添加评论后，让相关的 commentsProvider 失效以重新获取最新评论
  ref.invalidate(commentsProvider(params.transactionId));

  return newComment;
});

// Provider for deleting a comment
final deleteCommentProvider = FutureProvider.autoDispose
    .family<void, ({String transactionId, String commentId})>(
        (ref, params) async {
      final commentService = ref.watch(commentServiceProvider);
      await commentService.deleteComment(params.commentId);

      // 成功删除评论后，让相关的 commentsProvider 失效以重新获取最新评论
      ref.invalidate(commentsProvider(params.transactionId));
    });

// StateProvider 用于管理当前正在回复的评论ID (保持不变)
final replyingToCommentIdProvider = StateProvider<String?>((ref) => null);

// StateProvider 用于管理当前正在回复的评论的用户名 (保持不变)
final replyingToUserNameProvider = StateProvider<String?>((ref) => null);

final commentServiceSingletonProvider = Provider<CommentService>((ref) {
  final networkClient = ref.watch(networkClientProvider); // 从你的网络层获取
  return CommentService(networkClient);
});