// features/home/<USER>/feed/comment_section_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';

import '../../models/comment_model.dart';
import '../../providers/comment_providers.dart';
import 'comment_item_widget.dart'; // 确保这个组件已按之前的建议重构

// Provider 保持不变
final replyingToCommentIdProvider = StateProvider<String?>((ref) => null);
final replyingToUserNameProvider = StateProvider<String?>((ref) => null);

class CommentSectionWidget extends ConsumerWidget {
  // 改为 ConsumerWidget，因为移除了内部状态
  final String transactionId;

  const CommentSectionWidget({super.key, required this.transactionId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final commentsAsyncValue = ref.watch(commentsProvider(transactionId));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 24.0, bottom: 12.0),
          child: Text('备注', style: theme.typography.xl.copyWith(fontWeight: FontWeight.w600)),
        ),
        commentsAsyncValue.when(
          data: (allComments) {
            // allComments 是扁平的评论列表
            if (allComments.isEmpty) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 30.0),
                child: Center(
                  child: Text('暂无备注', style: theme.typography.base.copyWith(color: colorScheme.mutedForeground)),
                ),
              );
            }

            // 1. 将评论分为父评论和子评论映射
            final parentComments = <CommentModel>[];
            final Map<String, List<CommentModel>> repliesMap = {};

            for (final comment in allComments) {
              if (comment.parentCommentId == null) {
                parentComments.add(comment);
              } else {
                repliesMap.putIfAbsent(comment.parentCommentId!, () => []).add(comment);
              }
            }
            // 可选：对父评论和子评论进行排序（例如按创建时间）
            parentComments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            repliesMap.forEach((key, value) {
              value.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            });

            // 2. 构建包含父评论及其子评论的 Widget 列表
            final List<Widget> commentWidgets = [];
            for (final parent in parentComments) {
              commentWidgets.add(
                Padding(
                  // 父评论的 Padding
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                  child: CommentItemWidget(
                    comment: parent,
                    transactionId: transactionId,
                    // isSubComment: false, // CommentItemWidget 内部会通过 parentCommentId 判断
                  ),
                ),
              );
              // 查找并添加该父评论下的所有子评论
              final replies = repliesMap[parent.id] ?? [];
              for (final reply in replies) {
                commentWidgets.add(
                  Padding(
                    // 子评论的 Padding (CommentItemWidget 内部已有缩进)
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0), // 子评论垂直间距可以小一些
                    child: CommentItemWidget(
                      comment: reply,
                      transactionId: transactionId,
                      // isSubComment: true,
                    ),
                  ),
                );
              }
              // 在每个父评论及其所有子评论后添加分隔线（可选）
              if (replies.isNotEmpty || parentComments.indexOf(parent) < parentComments.length - 1) {
                commentWidgets.add(
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: FDivider(),
                  ),
                );
              }
            }
            // 移除最后一个分隔符，如果它存在的话
            if (commentWidgets.isNotEmpty && commentWidgets.last is Padding && (commentWidgets.last as Padding).child is FDivider) {
              commentWidgets.removeLast();
            }

            // 使用 Column 而不是 ListView.builder，因为 CustomScrollView 的 sliver 内部不应嵌套可独立滚动的列表
            // 如果评论非常多，需要考虑性能优化，但对于单层回复，Column 通常可以接受。
            return Column(children: commentWidgets);
          },
          loading: () => const SizedBox.shrink(), // 骨架屏已经处理加载状态
          error: (err, stack) => Padding(
            padding: const EdgeInsets.all(30.0),
            child: Center(
              child: Text('加载备注失败: ${err.toString()}', style: theme.typography.base.copyWith(color: colorScheme.destructive)),
            ),
          ),
        ),
      ],
    );
  }
}
