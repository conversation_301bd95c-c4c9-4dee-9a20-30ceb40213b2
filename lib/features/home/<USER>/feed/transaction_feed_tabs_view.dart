// features/home/<USER>/feed/transaction_feed_tabs_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart'; // 引入 shadcn_ui
import '../../providers/home_providers.dart';
import 'transaction_feed_view.dart';

class TransactionFeedTabsView extends ConsumerWidget {
  const TransactionFeedTabsView({super.key});

  // 定义 Tab 的数据结构
  static const List<({TransactionFeedType type, String label})> _tabData = [
    (type: TransactionFeedType.all, label: '全部'),
    (type: TransactionFeedType.expense, label: '支出'),
    (type: TransactionFeedType.income, label: '收入'),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colors = theme.colors;
    final currentSelectedType = ref.watch(currentTransactionFeedTypeProvider);

    // 构建自定义 Tab 按钮栏
    Widget buildCustomTabBar() {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4), // Tab栏的内边距
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround, // 按钮均匀分布
          children: _tabData.map((tabInfo) {
            final isSelected = tabInfo.type == currentSelectedType;

            // 根据是否选中，选择不同的按钮样式
            if (isSelected) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0), // 按钮之间的间距
                  child: SizedBox(
                    child: FButton(
                      // 选中的按钮使用默认的 primary 样式
                      onPress: () {
                        // 如果已经是选中状态，可以不执行任何操作，或者根据需求设计
                      },
                      child: Text(
                        tabInfo.label,
                        style: theme.typography.sm.copyWith(
                          fontWeight: FontWeight.w600, // 选中时文本可以加粗
                          color: colors.primaryForeground, // 确保文本颜色与按钮背景对比清晰
                        ),
                      ),
                    ),
                  ),
                ),
              );
            } else {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0), // 按钮之间的间距
                  child: SizedBox(
                    child: FButton(
                      // 未选中的按钮使用 outline 样式
                      style: FButtonStyle.outline(),
                      onPress: () {
                        ref.read(currentTransactionFeedTypeProvider.notifier).state = tabInfo.type;
                      },
                      child: Text(
                        tabInfo.label,
                        style: theme.typography.sm.copyWith(
                          fontWeight: FontWeight.w500, // 未选中时普通字重
                          color: colors.foreground, // 使用前景色确保文字清晰可见
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }
          }).toList(),
        ),
      );
    }

    // 内容区域切换 (保持不变)
    Widget buildContentArea() {
      return AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        child: TransactionFeedView(
          key: ValueKey(currentSelectedType),
          intendedFeedType: currentSelectedType,
        ),
      );
    }

    return Column(
      children: [
        buildCustomTabBar(),
        Expanded(
          child: buildContentArea(),
        ),
      ],
    );
  }
}