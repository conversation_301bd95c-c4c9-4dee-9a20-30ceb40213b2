// features/home/<USER>/feed/transaction_feed_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_expense_tracker/features/home/<USER>/transaction_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '/features/home/<USER>/home_providers.dart';
import '/features/home/<USER>/feed/transaction_card.dart';
import 'package:shimmer/shimmer.dart';
import 'package:forui/forui.dart'; // 1. 引入 forui

class TransactionFeedView extends ConsumerStatefulWidget {
  final TransactionFeedType intendedFeedType; // <--- 新增：此视图实例期望的类型

  const TransactionFeedView({super.key, required this.intendedFeedType});

  @override
  ConsumerState<TransactionFeedView> createState() => _TransactionFeedViewState();
}

class _TransactionFeedViewState extends ConsumerState<TransactionFeedView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    // 注意：初始数据加载现在完全由 TransactionFeedNotifier 监听 currentTransactionFeedTypeProvider 触发
    // 不再需要在每个 TransactionFeedView 实例的 initState 中单独加载。
  }

  void _onScroll() {
    // 只有当当前视图显示的类型与全局选中的类型一致时，才允许加载更多
    if (widget.intendedFeedType == ref.read(currentTransactionFeedTypeProvider)) {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        final notifier = ref.read(transactionFeedNotifierProvider.notifier);
        final TransactionFeedState feedState = ref.read(transactionFeedNotifierProvider);
        if (!feedState.isLoadingMore && !feedState.hasReachedMax) {
          notifier.fetchMoreTransactions();
        }
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // 构建单个交易卡片的骨架屏
  Widget _buildTransactionCardSkeleton(BuildContext context) {
    // Shimmer 效果的颜色 (在白色背景上) 骨架图不使用主题色，采用硬编码
    // colorScheme.muted.withOpacity(0.3);
    final Color shimmerBaseColor = Colors.grey[200]!; // 骨架上Shimmer的基础色
    //  colorScheme.muted.withOpacity(0.1);
    final Color shimmerHighlightColor = Colors.grey[50]!; // 骨架上Shimmer的高亮流光色
    // 骨架元素本身的颜色 (比Shimmer基础色稍深一点，或者一个固定的浅灰色)
    // colorScheme.muted.withOpacity(0.5)
    final Color placeholderShapeColor = Colors.grey[200]!; // 这些是构成布局的形状的颜色

    // 这个外层 Container 代表骨架卡片本身，背景是白色
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
      color: Colors.white, // 卡片背景是白色
      child: Shimmer.fromColors(
        baseColor: shimmerBaseColor,
        highlightColor: shimmerHighlightColor,
        period: const Duration(milliseconds: 1200), // 动画周期
        // Shimmer 的 child 是实际的骨架布局结构
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 1. 左侧：类别图标和共享指示骨架
            SizedBox(
              width: 40, // 与真实卡片左侧宽度一致
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    // 模拟 ShadAvatar
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: placeholderShapeColor,
                      shape: BoxShape.circle, // ShadAvatar 通常是圆的
                    ),
                  ), // 主类别图标骨架
                ],
              ),
            ),
            const SizedBox(width: 12),
            // 2. 右侧主要内容区域骨架 (三段式信息流)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 段1: 类别名称, 时间戳
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(height: 15, color: placeholderShapeColor, margin: EdgeInsets.only(right: 16)), // 类别名称骨架
                      ),
                      Container(height: 12, width: 50, color: placeholderShapeColor),
                      // 时间戳骨架
                    ],
                  ),
                  const SizedBox(height: 6),
                  // 段2: 交易金额
                  Container(height: 18, width: 120, color: placeholderShapeColor, margin: EdgeInsets.symmetric(vertical: 1)),
                  const SizedBox(height: 10),
                  // 段3: 交易描述
                  Container(height: 14, color: placeholderShapeColor),
                  const SizedBox(height: 5),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme; // 获取 Forui 主题
    final colors = theme.colors;

    // 监听全局选中的 feed type
    final TransactionFeedType globalCurrentFeedType = ref.watch(currentTransactionFeedTypeProvider);
    // 监听 feed 数据和加载状态
    final TransactionFeedState feedState = ref.watch(transactionFeedNotifierProvider);
    final List<TransactionModel> transactions = feedState.transactions;

    // 关键逻辑：如果当前视图期望的类型与全局选中的类型不匹配，
    // 说明这个视图实例是 TabBarView 滑动过程中“路过”的，或者还未更新到最新数据。
    // 此时，我们应该显示加载状态（骨架屏）而不是旧数据。
    // 并且，只在类型匹配时，isLoadingMore 才有意义。
    bool shouldShowSkeletonDueToTypeMismatch = widget.intendedFeedType != globalCurrentFeedType;

    // 初始加载骨架屏逻辑 (当 transactions 为空且正在加载，且类型匹配时)
    // 或者，当类型不匹配时，也显示骨架屏，以避免闪现旧数据
    if (shouldShowSkeletonDueToTypeMismatch || (feedState.isLoadingMore && transactions.isEmpty && !feedState.hasReachedMax)) {
      // 修改：初始加载时，背景色应由父级（如ShadTabs的content区域）或ShadAppBuilder控制
      // 如果确实需要特定背景，应使用 colorScheme.background 或 card
      return ListView.separated(
        itemCount: 5, // 显示几个骨架项
        itemBuilder: (context, index) => _buildTransactionCardSkeleton(context),
        separatorBuilder: (context, index) => FDivider(axis: Axis.horizontal),
      );
    }

    // 如果有错误信息，显示错误提示
    if (feedState.errorMessage != null && widget.intendedFeedType == globalCurrentFeedType) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // 添加这行，让Column只占用需要的空间
          children: [
            Text('加载失败: ${feedState.errorMessage}', style: theme.typography.base.copyWith(color: colors.destructive)),
            const SizedBox(height: 16),
            FButton(onPress: () => ref.read(transactionFeedNotifierProvider.notifier).refreshFeed(), child: const Text('重试')),
          ],
        ),
      );
    }

    // 空状态 (transactions 为空，不在加载中，且类型匹配)
    if (transactions.isEmpty && !feedState.isLoadingMore && widget.intendedFeedType == globalCurrentFeedType) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // 添加这行，让Column只占用需要的空间
          children: [
            Text('暂无交易记录', style: theme.typography.base.copyWith(color: colors.mutedForeground)),
            const SizedBox(height: 16),
            FButton(
                mainAxisSize: MainAxisSize.min,
                onPress: () => ref.read(transactionFeedNotifierProvider.notifier).refreshFeed(),
                child: const Text('刷新试试')
            ),
          ],
        ),
      );
    }

    // 正常显示列表 (确保类型匹配)
    // 如果类型不匹配，我们已经在上面返回了骨架屏，所以这里可以认为类型是匹配的
    // 或者为了更安全，再加一次判断
    if (widget.intendedFeedType != globalCurrentFeedType) {
      //理论上不应到达这里，因为上面已经处理了类型不匹配的情况
      return const SizedBox.shrink(); // 或返回骨架屏
    }

    // 交易列表 (确保背景适配)
    return RefreshIndicator(
      color: colors.primary, // 下拉刷新指示器颜色
      backgroundColor: colors.background, // 下拉刷新背景
      onRefresh: () async {
        // 下拉刷新时，确保是针对当前 tab 的类型
        if (widget.intendedFeedType == ref.read(currentTransactionFeedTypeProvider)) {
          await ref.read(transactionFeedNotifierProvider.notifier).refreshFeed();
        }
      },
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 6),
        controller: _scrollController,
        // itemCount 逻辑调整：只有在加载更多且列表不为空，或者已达最大且列表不为空时，才为底部指示器增加1
        itemCount: transactions.length + ((feedState.isLoadingMore && transactions.isNotEmpty) || (feedState.hasReachedMax && transactions.isNotEmpty) ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < transactions.length) {
            final transaction = transactions[index];
            return TransactionCard(transaction: transaction);
          } else if (index == transactions.length && feedState.isLoadingMore && transactions.isNotEmpty) {
            // 只有在列表不为空时，才在底部显示加载更多的骨架
            // 3. 加载更多指示器
            // 可以选择显示一个卡片骨架屏，或者一个更简洁的加载指示器
            // 保留卡片骨架屏
            return _buildTransactionCardSkeleton(context);
          } else if (index == transactions.length && feedState.hasReachedMax && transactions.isNotEmpty) {
            // 只有在列表不为空时，才显示“没有更多数据”
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text('没有更多数据了', style: theme.typography.sm.copyWith(color: colors.mutedForeground)),
              ),
            );
          }
          // 如果 transactions 为空，且 isLoadingMore 为 true，这个分支由 build 方法顶部的骨架屏列表处理。
          // 此处返回 SizedBox.shrink() 以处理 itemCount > transactions.length 但不满足上述条件的情况（理论上不应发生）。
          return const SizedBox.shrink();
        },
        separatorBuilder: (context, index) => Divider(
          height: 0.5,
          thickness: 0.5,
          color: colors.border.withValues(alpha: 0.7), // 适配白色背景的分割线
        ),
      ),
    );
  }
}
