// features/home/<USER>/feed/transaction_card.dart
import 'package:flutter/material.dart';
import 'package:flutter_expense_tracker/shared/models/action_item_model.dart';
import 'package:flutter_expense_tracker/shared/widgets/dialogs/action_bottom_sheet.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import '/features/home/<USER>/transaction_model.dart';
import 'package:timeago/timeago.dart' as timeago; // 用于相对时间显示
import '/shared/config/category_config.dart';




class TransactionCard extends ConsumerWidget {
  final TransactionModel transaction;

  const TransactionCard({super.key, required this.transaction});

  /// 获取分类显示名称
  String _getCategoryDisplayName(TransactionModel transaction) {
    // 优先使用服务端本地化的分类名称
    if (transaction.categoryText != null && transaction.categoryText!.isNotEmpty) {
      return transaction.categoryText!;
    }
    // 回退到客户端分类ID映射
    if (transaction.categoryId != null) {
      return CategoryConfig.getCategoryName(transaction.categoryId);
    }
    // 最后回退到原有的category字段
    return transaction.category;
  }

  /// 获取金额显示文本
  String _getAmountDisplayText(TransactionModel transaction) {
    // 优先使用API返回的display字段
    if (transaction.display != null) {
      return transaction.display!.fullString;
    }

    // 向后兼容：使用原有的amount字段格式化
    final isExpense = transaction.type == TransactionType.expense;

    return '${isExpense ? '-' : '+'}${NumberFormat.currency(locale: 'zh_CN', symbol: '¥', decimalDigits: 2).format(transaction.amount)}';
  }

  /// 获取金额颜色
  Color _getAmountColor(TransactionModel transaction, FColors colorScheme, FThemeData theme) {
    // 统一处理：支出用红色，收入用主色调
    return transaction.type == TransactionType.expense
        ? colorScheme.destructive
        : colorScheme.primary;
  }

  /// 获取交易描述
  String _getTransactionDescription(TransactionModel transaction) {
    // 统一处理所有交易类型，直接返回描述
    return transaction.description ?? '';
  }

  // 共享者头像 (使用 FAvatar)
  Widget _buildSharerAvatar(BuildContext context, SharedUserInfo sharer) {
    final theme = context.theme;
    return Padding(
      padding: const EdgeInsets.only(top: 6.0),
      child: FAvatar(
        image: NetworkImage(sharer.avatarUrl),
        fallback: Icon(FIcons.user, size: 10, color: theme.colors.mutedForeground),
      ),
    );
  }

  void _showTransactionActions(BuildContext context, WidgetRef ref) {
    // 假设 currentUserIdProvider 存在
    // final String currentUserId = ref.watch(currentUserIdProvider);
    // final bool isOwner = transaction.userId == currentUserId; // 假设 TransactionModel 有 userId 字段代表创建者

    // 根据业务逻辑构建操作项列表
    final List<ActionItem> primaryActions = [];
    final List<ActionItem> destructiveActions = [];

    // // 编辑操作 (假设只有创建者可以编辑)
    // if (isOwner) {
    primaryActions.add(
      ActionItem(
        title: '编辑',
        icon: FIcons.file,
        onTap: () {
          print('编辑交易: ${transaction.id}');
          // TODO: 导航到编辑页面或弹出编辑对话框
          // context.goNamed('editTransaction', pathParameters: {'transactionId': transaction.id}, extra: transaction);
        },
      ),
    );
    // }

    // 共享/取消共享操作
    if (transaction.isShared) {
      primaryActions.add(
        ActionItem(
          title: '取消共享',
          icon: FIcons.eyeOff, // 或者类似取消链接的图标
          onTap: () {
            print('取消共享交易: ${transaction.id}');
            // TODO: 调用取消共享的API和Provider
            // ref.read(unshareTransactionProvider(transaction.id).future);
          },
        ),
      );
    } else {
      primaryActions.add(
        ActionItem(
          title: '共享此账单',
          icon: FIcons.share2,
          onTap: () {
            print('共享交易: ${transaction.id}');
            // TODO: 导航到共享设置页面或弹出共享对话框
          },
        ),
      );
    }

    // // 收藏/取消收藏 (示例)
    primaryActions.add(
      ActionItem(
        title: '收藏',
        icon: FIcons.bookmark,
        onTap: () {
          print('收藏交易: ${transaction.id}');
        },
      ),
    );

    // 删除操作 (假设只有创建者可以删除)
    // if (isOwner) {
    destructiveActions.add(
      ActionItem(
        title: '删除',
        icon: FIcons.trash2,
        // color: theme.colorScheme.destructive, // 颜色将由 ShadcnActionSheetContent 内部处理
        onTap: () {
          print('删除交易: ${transaction.id}');
          // TODO: 显示确认对话框，然后调用删除API和Provider
          // _showDeleteConfirmDialog(context, ref, transaction.id);
        },
      ),
    );
    // }

    // 呼出更多操作项Modal覆盖住整个底部导航栏
    showModalBottomSheet(
      context: GoRouter.of(context).routerDelegate.navigatorKey.currentContext!, // <--- 获取GoRouter的根Navigator的context
      // 或者，如果你的 MaterialApp 有一个全局的 navigatorKey:
      // context: rootNavigatorKey.currentContext!,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext sheetContext) {
        // sheetContext 现在是根Navigator下的context
        return ActionBottomSheet(actions: primaryActions, destructiveActions: destructiveActions.isNotEmpty ? destructiveActions : null);
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final isExpense = transaction.type == TransactionType.expense;

    // 初始化中文的相对时间显示
    timeago.setLocaleMessages('zh_CN', timeago.ZhCnMessages());
    final String timeAgoString = timeago.format(transaction.timestamp, locale: 'zh_CN');

    // 卡片内容，使用 Shadcn UI 组件和样式
    Widget cardContent = Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      // 背景色由列表的 ListView 或父级容器控制，这里保持透明
      // color: colorScheme.card, // 如果每个卡片需要独立背景
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. 左侧：类别图标和可能的共享指示
          SizedBox(
            width: 40,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    transaction.categoryId != null
                      ? CategoryConfig.getCategoryIcon(transaction.categoryId)
                      : CategoryConfig.getCategoryIconByName(transaction.category),
                    size: 20,
                    color: colorScheme.mutedForeground
                  ),
                ),
                if (transaction.isShared && transaction.sharedWith.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6.0),
                    child: Container(
                      height: 20.0 * transaction.sharedWith.take(1).length + (transaction.sharedWith.length > 1 ? 10 : 0), // 根据头像数量调整
                      width: 1,
                      color: colorScheme.border, // 使用主题边框色
                    ),
                  ),
                  ...transaction.sharedWith
                      .take(1) // 最多显示1个，保持简洁
                      .map((sharer) => _buildSharerAvatar(context, sharer)),
                  if (transaction.sharedWith.length > 1)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        '+${transaction.sharedWith.length - 1}',
                        style: theme.typography.sm.copyWith(color: colorScheme.mutedForeground, fontSize: 9),
                      ),
                    ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 12),

          // 2. 右侧主要内容区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 2a. 顶部行: 类别名称, 时间戳, 更多按钮
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        _getCategoryDisplayName(transaction),
                        style: theme.typography.base.copyWith(
                          fontWeight: FontWeight.w600, // 类别名称加粗
                          color: colorScheme.foreground,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(timeAgoString, style: theme.typography.sm.copyWith(color: colorScheme.mutedForeground)),
                    const SizedBox(width: 4),
                    FButton.icon(
                      style: FButtonStyle.ghost(),
                      onPress: () => _showTransactionActions(context, ref),
                      child: Icon(FIcons.ellipsis, color: colorScheme.mutedForeground, size: 18),
                    ),
                  ],
                ),
                const SizedBox(height: 2),

                // 2b. 交易金额
                Text(
                  _getAmountDisplayText(transaction),
                  style: theme.typography.lg.copyWith(
                    // 使用 lg 强调金额
                    color: _getAmountColor(transaction, colorScheme, theme),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 6),

                // 2c. 交易描述
                if (_getTransactionDescription(transaction).isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 6.0),
                    child: Text(
                      _getTransactionDescription(transaction),
                      style: theme.typography.sm.copyWith(color: colorScheme.foreground.withValues(alpha: 0.9), height: 1.3),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                // 2e. 底部操作按钮行
                Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      _TransactionActionButton(
                        icon: FIcons.messageCircle, // 替换图标
                        count: transaction.commentCount,
                        onTap: () {
                          /* TODO */
                        },
                        theme: theme,
                      ),
                      const SizedBox(width: 16),
                      _TransactionActionButton(
                        icon: FIcons.share2, // 替换图标
                        count: transaction.shareCount,
                        onTap: () {
                          /* TODO */
                        },
                        theme: theme,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );

    return Material(
      // 使用 Material 包裹 InkWell 以确保水波纹效果可见，并处理可能的背景问题
      color: Colors.transparent, // Material 的背景设为透明，让 InkWell 或其子Widget的背景色生效
      child: InkWell(
        onTap: () {
          context.pushNamed('transactionDetail', pathParameters: {'transactionId': transaction.id});
        },
        child: cardContent, // 将之前构建的卡片内容作为 InkWell 的 child
      ),
    );
  }
}

// 辅助小部件用于操作按钮 (适配 Shadcn UI)
class _TransactionActionButton extends StatelessWidget {
  final IconData icon;
  final int? count;
  final VoidCallback onTap;
  final FThemeData theme; // 传入主题

  const _TransactionActionButton({required this.icon, this.count, required this.onTap, required this.theme});

  @override
  Widget build(BuildContext context) {
    return FButton(
      style: FButtonStyle.ghost(),
      mainAxisSize: MainAxisSize.min,
      onPress: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16.0, color: theme.colors.mutedForeground), // 手动设置颜色
          if (count != null && count! > 0) ...[
            const SizedBox(width: 4.0),
            Text(
              count.toString(),
              style: theme.typography.xs.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.mutedForeground,
              )
            ),
          ],
        ],
      ),
    );
  }
}
