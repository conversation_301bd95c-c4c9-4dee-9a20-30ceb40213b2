// features/transaction_detail/widgets/comment_input_bar.dart
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart'; // 1. 引入 shadcn_ui

// 假设这些 Provider 从外部导入
import '../../providers/comment_providers.dart';

class CommentInputBar extends ConsumerStatefulWidget {
  final String transactionId;
  const CommentInputBar({super.key, required this.transactionId});

  @override
  ConsumerState<CommentInputBar> createState() => _CommentInputBarState();
}

class _CommentInputBarState extends ConsumerState<CommentInputBar> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  bool _isSubmitting = false;

  @override
  @override
  void initState() {
    super.initState();
    ref.listenManual<String?>(replyingToCommentIdProvider, (previous, next) {
      if (next != null && (previous == null || previous != next)) {
        if (!_commentFocusNode.hasFocus) {
          _commentFocusNode.requestFocus();
        }
        final currentReplyingToName = ref.read(replyingToUserNameProvider);
        // 获取被回复的评论对象，以便后续判断其 parentCommentId
        final allComments = ref.read(commentsProvider(widget.transactionId)).asData?.value ?? [];
        final repliedToComment = allComments.firstWhereOrNull((c) => c.id == next);

        if (currentReplyingToName != null && repliedToComment != null) {
          // 预填充时，可以考虑显示回复链，例如 "@张三 > @李四"
          // 但为了简单，我们仍然只显示直接回复的用户名
          final newText = "@$currentReplyingToName ";
          _commentController.value = TextEditingValue(
            text: newText,
            selection: TextSelection.collapsed(offset: newText.length),
          );
        }
      }
    });
  }

  Future<void> _submitComment() async {
    final commentText = _commentController.text.trim();
    if (commentText.isEmpty) return;

    setState(() { _isSubmitting = true; });

    final directlyRepliedToCommentId = ref.read(replyingToCommentIdProvider);
    String? effectiveParentCommentId; // 这是要传给后端的 parentCommentId

    if (directlyRepliedToCommentId != null) {
      // 需要获取所有评论数据来找到被回复的评论，以确定其 parentCommentId
      // 这假设 commentsProvider 已经加载了数据，或者在实际应用中，
      // 当点击回复时，可以将 CommentModel 对象本身存到另一个 StateProvider 中。
      final allComments = ref.read(commentsProvider(widget.transactionId)).asData?.value ?? [];
      final repliedToComment = allComments.firstWhereOrNull((c) => c.id == directlyRepliedToCommentId);

      if (repliedToComment != null) {
        if (repliedToComment.parentCommentId == null) {
          // 如果直接回复的是一个父评论，则 parentCommentId 就是这个父评论的 id
          effectiveParentCommentId = repliedToComment.id;
        } else {
          // 如果回复的是一个子评论，则 parentCommentId 应该是这个子评论的 parentCommentId (即顶层父评论的 id)
          effectiveParentCommentId = repliedToComment.parentCommentId;
        }
      } else {
        // 理论上不应该发生，因为 replyingToCommentIdProvider 应该对应一个存在的评论
        // 但作为降级处理，可以不设置 parentCommentId，使其成为一个新的父评论
        effectiveParentCommentId = null;
      }
    } else {
      // 没有回复目标，这是一条新的父评论
      effectiveParentCommentId = null;
    }

    try {
      await ref.read(addCommentProvider(
          (
          transactionId: widget.transactionId,
          commentText: commentText,
          parentCommentId: effectiveParentCommentId, // 使用计算出的 effectiveParentCommentId
          )
      ).future);

      _commentController.clear();
      ref.read(replyingToCommentIdProvider.notifier).state = null;
      ref.read(replyingToUserNameProvider.notifier).state = null;
      _commentFocusNode.unfocus();
    } catch (e) {
      if (mounted) {
        showFToast(
          context: context,
          icon: const Icon(FIcons.triangleAlert),
          title: const Text('错误'),
          description: Text('评论失败: ${e.toString()}'),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isSubmitting = false; });
      }
    }
  }

  // ... dispose 和 build 方法 ...
  @override
  void dispose() {
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;
    final replyingToName = ref.watch(replyingToUserNameProvider);
    // final directlyRepliedToCommentId = ref.watch(replyingToCommentIdProvider); // 获取被直接回复的评论ID

    // // 如果需要显示 "A > B"，则需要获取被回复评论的作者名
    // String displayReplyTarget = replyingToName ?? "";
    // if (directlyRepliedToCommentId != null) {
    //   final allComments = ref.watch(commentsProvider(widget.transactionId)).asData?.value ?? [];
    //   final directlyRepliedComment = allComments.firstWhereOrNull((c) => c.id == directlyRepliedToCommentId);
    //   if (directlyRepliedComment?.parentCommentId != null && directlyRepliedComment?.repliedToUserName != null) {
    //      // 如果是回复一个子评论，理论上 repliedToUserName 已经是父评论的作者了
    //      // 但如果我们的目标是显示 “我 > (我回复的这条子评论的作者名)”，那么这里逻辑要调整
    //      // 按照您最新的要求，子评论的回复目标是父评论，所以这里显示父评论作者名是OK的
    //      // 如果要显示 "我 > 子评论作者"，那么 replyingToUserNameProvider 就应该存子评论作者名
    //   }
    // }


    // 仅在底部增加与系统键盘等高的内边距，避免整块组件被额外上推
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: colors.background,
          border: Border(top: BorderSide(color: colors.border, width: 1)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (replyingToName != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 6.0, left: 4.0, right: 4.0),
                child: Row(
                  children: [
                    Text(
                      '回复 @$replyingToName:', // 这里显示的是被点击回复的那个评论的作者名
                      style: theme.typography.sm.copyWith(color: colors.mutedForeground),
                    ),
                    const Spacer(),
                    FButton.icon(
                      onPress: () {
                        ref.read(replyingToCommentIdProvider.notifier).state = null;
                        ref.read(replyingToUserNameProvider.notifier).state = null;
                        _commentFocusNode.unfocus();
                        _commentController.clear();
                      },
                      child: Icon(FIcons.x, color: colors.mutedForeground, size: 16),
                    )
                  ],
                ),
              ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: FTextField(
                    controller: _commentController,
                    focusNode: _commentFocusNode,
                    hint: '添加备注...',
                    onTap: () {
                      // 这里的逻辑是为了确保用户直接点击输入框时，清除可能存在的旧的回复状态
                      // 如果是通过点击某个评论的“回复”按钮而聚焦的，replyingToCommentIdProvider 此时已经有值
                      if (ref.read(replyingToCommentIdProvider) == null && _commentFocusNode.hasFocus) {
                        // 如果没有回复对象且焦点在输入框，用户可能想开始新评论，无需操作
                      } else if (ref.read(replyingToCommentIdProvider) != null && !_commentFocusNode.hasFocus) {
                        // 如果有回复对象但焦点不在输入框，点击时获取焦点
                        // （通常 ShadInput 内部会处理焦点获取）
                      } else if (ref.read(replyingToCommentIdProvider) == null && !_commentFocusNode.hasFocus) {
                        // 用户直接点击空输入框，清除可能残留的回复状态
                        ref.read(replyingToCommentIdProvider.notifier).state = null;
                        ref.read(replyingToUserNameProvider.notifier).state = null;
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                _isSubmitting
                    ? SizedBox(
                  width: 36,
                  height: 36,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: colors.primary,
                    ),
                  ),
                )
                    : FButton.icon(
                  onPress: _submitComment,
                  child: Icon(FIcons.send),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}