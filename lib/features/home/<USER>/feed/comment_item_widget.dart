// features/transaction_detail/widgets/comment_item_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../models/comment_model.dart';
import '../../providers/comment_providers.dart';

final currentUserIdProvider = Provider<String>((ref) => '1'); // 假设的当前用户ID

class CommentItemWidget extends ConsumerWidget {
  final CommentModel comment;
  final String transactionId;

  const CommentItemWidget({
    super.key,
    required this.comment,
    required this.transactionId,
  });

  Future<void> _showDeleteConfirmDialog(
      BuildContext context, WidgetRef ref) async {
    return showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: const Text('你确定要删除这条评论吗？此操作无法撤销。'),
          actions: <Widget>[
            FButton(
              style: FButtonStyle.outline(),
              onPress: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text('取消'),
            ),
            FButton(
              style: FButtonStyle.destructive(),
              onPress: () async {
                Navigator.of(dialogContext).pop();
                try {
                  await ref.read(deleteCommentProvider(
                      (transactionId: transactionId, commentId: comment.id))
                      .future);
                  if (!context.mounted) return;
                  showFToast(
                    context: context,
                    title: const Text('成功'),
                    description: const Text('评论已删除'),
                  );
                } catch (e) {
                  if (!context.mounted) return;
                  showFToast(
                    context: context,
                    icon: const Icon(FIcons.triangleAlert),
                    title: const Text('错误'),
                    description: Text('删除失败: ${e.toString()}'),
                  );
                }
              },
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  void _showCommentActions(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    final String currentLoggedInUserId = ref.watch(currentUserIdProvider);
    final bool canDelete = comment.userId == currentLoggedInUserId;

    List<Widget> actions = [];

    if (canDelete) {
      actions.add(
        FButton(
          style: FButtonStyle.ghost(),
          onPress: () {
            Navigator.of(context).pop();
            _showDeleteConfirmDialog(context, ref);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(FIcons.trash2, size: 16, color: colorScheme.destructive),
              const SizedBox(width: 8),
              Text('删除评论', style: TextStyle(color: colorScheme.destructive)),
            ],
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      showFToast(
        context: context,
        title: const Text('提示'),
        description: const Text('没有可用的操作'),
      );
      return;
    }

    showModalBottomSheet(
      context: context, // 使用 CommentItemWidget 的 context
      builder: (sheetContext) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: actions,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final colorScheme = theme.colors;
    timeago.setLocaleMessages('zh_CN', timeago.ZhCnMessages());

    final bool isSubComment = comment.parentCommentId != null;
    final double avatarRadius = isSubComment ? 12 : 16;
    final double avatarIconSize = isSubComment ? 10 : 14;
    final String currentLoggedInUserId = ref.watch(currentUserIdProvider);
    final bool canDelete = comment.userId == currentLoggedInUserId;

    // 构建用户名显示，包含回复目标
    Widget buildUserNameDisplay() {
      final List<Widget> nameParts = [
        Text(
          comment.userName, // 当前评论者
          style: theme.typography.sm.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.foreground,
          ),
        ),
      ];

      // 检查是否是子评论，并且有被回复者信息
      if (isSubComment && comment.repliedToUserName != null && comment.repliedToUserName!.isNotEmpty) {
        nameParts.addAll([
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Icon(FIcons.chevronRight, size: 12, color: colorScheme.mutedForeground),
          ),
          Flexible( // 使用 Flexible 避免超长用户名溢出
            child: Text(
              comment.repliedToUserName!, // 被回复者
              style: theme.typography.sm.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.primary, // 被回复者用主题色突出
              ),
              overflow: TextOverflow.ellipsis, // 超长时省略
            ),
          ),
        ]);
      }
      return Row(mainAxisSize: MainAxisSize.min, children: nameParts);
    }

    return Padding(
      padding: EdgeInsets.only(
        left: isSubComment ? 32.0 : 0, // 子评论的缩进
        top: isSubComment ? 6.0 : 10.0, // 子评论的顶部间距可以小一些
        bottom: 6.0, // 统一底部间距
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FAvatar(
                image: NetworkImage(comment.userAvatarUrl),
                fallback: Icon(FIcons.circleUserRound,
                    size: avatarIconSize,
                    color: colorScheme.mutedForeground),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(child: buildUserNameDisplay()),
                        const SizedBox(width: 6),
                        Text(
                          timeago.format(comment.createdAt, locale: 'zh_CN'),
                          style: theme.typography.sm.copyWith(
                              fontSize: 11,
                              color: colorScheme.mutedForeground),
                        ),
                        if (canDelete)
                          FButton.icon(
                            onPress: () => _showCommentActions(context, ref),
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Icon(FIcons.ellipsis,
                                  color: colorScheme.mutedForeground, size: 16),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      comment.commentText,
                      style: theme.typography.sm.copyWith(
                          color: colorScheme.foreground,
                          height: 1.4),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0), // 减小回复按钮的顶部间距
                      child: FButton(
                        style: FButtonStyle.ghost(),
                        onPress: () {
                          final currentReplyingTo = ref.read(replyingToCommentIdProvider);
                          if (currentReplyingTo == comment.id) {
                            ref.read(replyingToCommentIdProvider.notifier).state = null;
                            ref.read(replyingToUserNameProvider.notifier).state = null;
                          } else {
                            ref.read(replyingToCommentIdProvider.notifier).state = comment.id;
                            // 当回复父评论时，被回复者就是父评论的作者
                            // 当回复子评论时，被回复者应该是该子评论的作者
                            ref.read(replyingToUserNameProvider.notifier).state = comment.userName;
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0), // 调整padding
                          child: SizedBox(
                            height: 24, // 给链接按钮一个明确的高度
                            child: Text(
                              '回复',
                              style: theme.typography.sm.copyWith(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: colorScheme.mutedForeground,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // 子评论不再由此 Widget 递归渲染
        ],
      ),
    );
  }
}