import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_expense_tracker/core/network/exceptions/app_exception.dart';
import 'package:flutter_expense_tracker/core/storage/secure_storage_service.dart';
import 'package:flutter_expense_tracker/features/auth/models/user.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

// 认证状态
enum AuthStatus { initial, authenticated, unauthenticated, loading }

class AuthState {
  final AuthStatus status;
  final UserModel? user;
  final String? token;

  AuthState({this.status = AuthStatus.initial, this.user, this.token});

  AuthState copyWith({AuthStatus? status, UserModel? user, String? token}) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      token: token ?? this.token,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  final SecureStorageService _storageService;

  AuthNotifier(this._authService, this._storageService) : super(AuthState()) {
    // 构造函数中初始化状态恢复
    // 注意：这里不等待异步完成，让初始化在后台进行
    _initializeAuthState();
  }

  /// 应用启动时恢复认证状态
  Future<void> _initializeAuthState() async {
    try {
      log('AuthNotifier: 开始初始化认证状态...');
      // 设置初始化状态
      state = state.copyWith(status: AuthStatus.loading);

      // 从本地存储读取token
      final token = await _storageService.getToken();
      log(
        'AuthNotifier: 从本地存储读取Token: ${token != null ? '${token.substring(0, 10)}...' : 'null'}',
      );

      if (token != null && token.isNotEmpty) {
        // 直接从本地存储恢复登录状态
        log('AuthNotifier: 找到本地Token，直接恢复登录状态');

        // 尝试从本地存储获取用户信息
        final userData = await _authService.getStoredAuthData();
        if (userData != null) {
          state = state.copyWith(
            status: AuthStatus.authenticated,
            user: userData['user'] as UserModel,
            token: token,
          );
          log(
            'AuthNotifier: 成功恢复登录状态 - 用户: ${(userData['user'] as UserModel).email}',
          );
        } else {
          // 如果没有用户信息，只恢复Token
          state = state.copyWith(
            status: AuthStatus.authenticated,
            token: token,
          );
          log('AuthNotifier: 恢复登录状态（仅Token，无用户信息）');
        }
      } else {
        // 没有token，设置为未登录状态
        state = state.copyWith(status: AuthStatus.unauthenticated);
        log('AuthNotifier: 未找到本地Token，设置为未登录状态');
      }
    } catch (e) {
      // 初始化失败，设置为未登录状态
      state = state.copyWith(status: AuthStatus.unauthenticated);
      log('AuthNotifier: 状态初始化失败: $e');
    }
  }

  // 内部辅助方法，用于保存认证数据
  Future<void> _handleAuthenticationSuccess({
    required UserModel user,
    required String token,
  }) async {
    await _storageService.saveToken(token); // <--- 保存 Token 到本地存储
    log('AuthNotifier: Token已保存到本地存储: ${token.substring(0, 10)}...');
    // await _storageService.saveUser(user); // 可选：保存用户信息
    state = state.copyWith(
      status: AuthStatus.authenticated,
      user: user,
      token: token,
    );
    log('AuthNotifier: 登录成功，状态已更新 - 用户: ${user.email}');
  }

  Future<void> login(String email, String password) async {
    try {
      // 不设置loading状态，避免触发SplashScreen显示
      final result = await _authService.login(email, password);
      await _handleAuthenticationSuccess(
        user: result.user,
        token: result.token,
      );
    } catch (e) {
      // 失败时，确保状态为 unauthenticated（通常已经是这个状态）
      if (state.status != AuthStatus.unauthenticated) {
        state = state.copyWith(status: AuthStatus.unauthenticated);
      }
      // 检查DioException并解包出我们自定义的AppException
      if (e is DioException && e.error is AppException) {
        throw e.error as AppException;
      }
      // 对于其他类型的异常，直接重新抛出
      rethrow;
    }
  }

  Future<void> register({
    required String account,
    required String password,
    required String verificationCode,
  }) async {
    try {
      // 不再修改这里的全局状态，让UI层自己管理加载状态
      final result = await _authService.register(
        account: account,
        password: password,
        verificationCode: verificationCode,
      );
      await _handleAuthenticationSuccess(
        user: result.user,
        token: result.token,
      );
    } catch (e) {
      // 检查DioException并解包出我们自定义的AppException
      if (e is DioException && e.error is AppException) {
        throw e.error as AppException;
      }
      // 对于其他类型的异常，直接重新抛出
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _authService.logout(); // 后端登出
      await _storageService.deleteToken();
      // await _storageService.deleteUser(); // 可选
      state = AuthState(status: AuthStatus.unauthenticated);
      log('AuthNotifier: 用户已成功登出');
    } catch (e) {
      // 即使后端登出失败，也要清除本地状态
      await _storageService.deleteToken();
      state = AuthState(status: AuthStatus.unauthenticated);
      log('AuthNotifier: 登出时发生错误，但已清除本地状态: $e');
    }
  }

  /// 刷新用户信息（从本地存储重新读取）
  Future<void> refreshUser() async {
    if (state.status != AuthStatus.authenticated) return;

    try {
      final userData = await _authService.getStoredAuthData();
      if (userData != null) {
        state = state.copyWith(user: userData['user'] as UserModel);
        log('AuthNotifier: 用户信息已从本地存储刷新');
      } else {
        log('AuthNotifier: 本地存储中未找到用户信息');
      }
    } catch (e) {
      log('AuthNotifier: 刷新用户信息失败: $e');
    }
  }

  /// 手动检查并刷新认证状态
  Future<void> checkAuthStatus() async {
    await _initializeAuthState();
  }

  /// 调试方法：检查本地存储的Token
  Future<void> debugCheckToken() async {
    try {
      final token = await _storageService.getToken();
      log(
        'DEBUG: 当前本地存储的Token: ${token != null ? '${token.substring(0, 10)}...' : 'null'}',
      );
      log('DEBUG: 当前状态: ${state.status}');
      log('DEBUG: 当前用户: ${state.user?.email ?? 'null'}');
    } catch (e) {
      log('DEBUG: 检查Token时发生错误: $e');
    }
  }
}

// 修改 authProvider 的定义以注入 SecureStorageService
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  final storageService = ref.watch(secureStorageServiceProvider);
  return AuthNotifier(authService, storageService);
});

// 当 token 从 null 变为有值时，依赖它的 Provider 会自动重新计算。
final authTokenProvider = Provider<String?>((ref) {
  // watch 整个 authProvider
  final authState = ref.watch(authProvider);
  // 只 select token 字段。当 token 变化时，这个 Provider 才会通知其监听者。
  return authState.token;
});

// 用户信息 Provider
final userProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

// 认证状态 Provider
final authStatusProvider = Provider<AuthStatus>((ref) {
  final authState = ref.watch(authProvider);
  return authState.status;
});
