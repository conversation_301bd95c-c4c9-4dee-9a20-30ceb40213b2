import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

enum VerificationStatus {
  initial,
  sendingCode,
  codeSent,
  error,
}

class VerificationState {
  final VerificationStatus status;
  final String? errorMessage;

  VerificationState({
    this.status = VerificationStatus.initial,
    this.errorMessage,
  });

  VerificationState copyWith({
    VerificationStatus? status,
    String? errorMessage,
    bool clearError = false,
  }) {
    return VerificationState(
      status: status ?? this.status,
      errorMessage: clearError ? null : errorMessage ?? this.errorMessage,
    );
  }
}

class VerificationNotifier extends StateNotifier<VerificationState> {
  final AuthService _authService;

  VerificationNotifier(this._authService) : super(VerificationState());

  // 只保留发送验证码的方法
  Future<void> sendVerificationCode(String contact) async {
    try {
      state = state.copyWith(status: VerificationStatus.sendingCode, clearError: true);
      await _authService.sendVerificationCode(contact);
      state = state.copyWith(status: VerificationStatus.codeSent);
    } catch (e) {
      state = state.copyWith(status: VerificationStatus.error, errorMessage: e.toString());
    }
  }

  // verifyCode 方法被移除，因为验证是在最终注册时由后端完成

  void reset() {
    state = VerificationState();
  }
}

final verificationProvider = StateNotifierProvider.autoDispose<VerificationNotifier, VerificationState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return VerificationNotifier(authService);
});