import 'dart:developer';
import 'package:flutter_expense_tracker/core/constants/api_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/network/network_client.dart';
import 'package:flutter_expense_tracker/features/auth/models/user.dart';
import '/core/utils/map_require.dart';
import '../../../core/services/timezone_service.dart';

class AuthService {
  final NetworkClient _networkClient;
  final TimezoneService _timezoneService;
  final FlutterSecureStorage _storage;
  late final SharedPreferences _prefs;

  // Keys for secure storage
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id'; // Store user ID securely

  // Keys for shared preferences
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _userPhoneKey = 'user_phone';

  AuthService(this._networkClient, this._timezoneService) : _storage = const FlutterSecureStorage() {
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Helper to save authentication data
  Future<void> _saveAuthData(String token, UserModel user) async {
    // Save sensitive data to secure storage
    await _storage.write(key: _tokenKey, value: token);
    await _storage.write(key: _userIdKey, value: user.id);

    // Save non-sensitive data to shared preferences
    await _prefs.setString(_userNameKey, user.username?.toString() ?? '');
    if (user.email != null) {
      await _prefs.setString(_userEmailKey, user.email!);
    } else {
      await _prefs.remove(_userEmailKey);
    }
    if (user.phone != null) {
      await _prefs.setString(_userPhoneKey, user.phone!);
    } else {
      await _prefs.remove(_userPhoneKey);
    }
    log(
      'AuthService: Auth data saved to secure storage and shared preferences.',
    );
  }

  // Helper to delete authentication data
  Future<void> _deleteAuthData() async {
    // Clear sensitive data from secure storage
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _userIdKey);

    // Clear non-sensitive data from shared preferences
    await _prefs.remove(_userNameKey);
    await _prefs.remove(_userEmailKey);
    await _prefs.remove(_userPhoneKey);
    log(
      'AuthService: Auth data deleted from secure storage and shared preferences.',
    );
  }

  // Method to retrieve stored authentication data
  Future<Map<String, dynamic>?> getStoredAuthData() async {
    final token = await _storage.read(key: _tokenKey);
    final userId = await _storage.read(key: _userIdKey);

    if (token != null && userId != null) {
      // Retrieve non-sensitive data from shared preferences
      final username = _prefs.getString(_userNameKey);
      final email = _prefs.getString(_userEmailKey);
      final phone = _prefs.getString(_userPhoneKey);

      if (username != null) {
        // Ensure at least username is present for a valid user model
        final user = UserModel(
          id: userId,
          username: username,
          email: email,
          phone: phone,
        );
        log('AuthService: Stored auth data retrieved.');
        return {'user': user, 'token': token};
      }
    }
    return null;
  }

  Future<({UserModel user, String token})> login(
    String account,
    String password,
  ) async {
    // 获取用户时区
    final timezone = await _timezoneService.getCurrentTimezone();

    final response = await _networkClient.request<Map<String, dynamic>>(
      '/auth/login',
      method: HttpMethod.post,
      data: {
        'account': account,
        'password': password,
        'type': 'email',
        'timezone': timezone, // 添加时区信息
      },
      fromJsonT: (json) => json as Map<String, dynamic>,
    );
    final userJson = response.require<Map<String, dynamic>>('user');
    final token = response.require<String>('token');
    final UserModel user = UserModel.fromJson(userJson);
    await _saveAuthData(token, user); // Save data after successful registration
    // 返回一个包含强类型对象的元组 (Record)
    return (user: user, token: token);
  }

  // --- 对接发送验证码API ---
  Future<void> sendVerificationCode(String account) async {
    await _networkClient.request<void>(
      '${ApiConstants.baseUrl}/auth/send-code',
      method: HttpMethod.post,
      data: {'account': account, 'type': 'email'},
    );
    log(
      "AuthService: Verification code sent to $account (API call successful)",
    );
  }

  // 用户注册
  Future<({UserModel user, String token})> register({
    required String account,
    required String password,
    required String verificationCode,
  }) async {
    // 获取用户时区
    final timezone = await _timezoneService.getCurrentTimezone();

    final response = await _networkClient.request<Map<String, dynamic>>(
      '/auth/register',
      method: HttpMethod.post,
      data: {
        'type': 'email',
        'account': account,
        'password': password,
        'code': verificationCode,
        'timezone': timezone, // 添加时区信息
      },
      fromJsonT: (json) => json as Map<String, dynamic>,
    );
    final userJson = response['user'] as Map<String, dynamic>?;
    final UserModel user = UserModel.fromJson(userJson!);
    final String? token = response['token'] as String?;
    await _saveAuthData(
      token!,
      user,
    ); // Save data after successful registration
    // 返回一个包含强类型对象的元组 (Record)
    return (user: user, token: token);
  }

  // 移除了不必要的 validateToken 和 getCurrentUser 方法
  // 因为登录时已经获取了完整的用户信息和Token，无需额外的API调用

  Future<void> logout() async {
    // Simulate logout delay
    await Future.delayed(const Duration(seconds: 1));
    await _deleteAuthData(); // Clear token and user data from secure storage and shared preferences
    log('AuthService: User logged out and data cleared.');
  }
}

// Provider for AuthService (确保 NetworkClientProvider 已定义)
final authServiceProvider = Provider<AuthService>((ref) {
  final networkClient = ref.watch(networkClientProvider); // 从你的网络层获取
  final timezoneService = ref.watch(timezoneServiceProvider); // 获取时区服务
  return AuthService(networkClient, timezoneService);
});
