import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import '../providers/verification_provider.dart'; // 导入新的验证码Provider
import 'register_step_2_page.dart';
import '/shared/l10n/app_strings.dart';

class RegisterStep1Page extends ConsumerStatefulWidget {
  const RegisterStep1Page({super.key});

  @override
  ConsumerState<RegisterStep1Page> createState() => _RegisterStep1PageState();
}

class _RegisterStep1PageState extends ConsumerState<RegisterStep1Page> {
  final _formKey = GlobalKey<FormState>();
  final _contactController = TextEditingController();
  final _codeController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _contactController.dispose();
    _codeController.dispose();
    // 验证码Provider现在是autoDispose的，当不再被监听时会自动销毁并重置状态
    super.dispose();
  }

  Future<void> _onSendCodePressed() async {
    final contact = _contactController.text;
    if (contact.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.get('pleaseEnterEmail')),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    await ref.read(verificationProvider.notifier).sendVerificationCode(contact);
  }

  Future<void> _onNextPressed() async {
    if (_formKey.currentState!.validate()) {
      // 不再调用 verifyCode 接口，直接导航到 RegisterStep2Page
      // 验证码的正确性由后端在第二步最终提交时验证
      // 直接导航到第二步，并传递联系方式和用户输入的验证码
      context.pushNamed(
        'registerStep2',
        extra: {
          'contact': _contactController.text,
          'verificationCode': _codeController.text,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    // 监听 verificationProvider 来显示“发送中”或“发送失败”
    final verificationState = ref.watch(verificationProvider); // 监听验证码状态
    final bool isSendingCode = verificationState.status == VerificationStatus.sendingCode;


    ref.listen<VerificationState>(verificationProvider, (previous, next) {
      if (next.status == VerificationStatus.codeSent) {
        showFToast(context: context, title: Text(AppStrings.get('codeSent')));
      } else if (next.status == VerificationStatus.error && next.errorMessage != null) {
        showFToast(
          context: context,
          title: Text(AppStrings.get('sendFailed')),
          description: Text(next.errorMessage!),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(title: const Text('创建账户')),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    AppStrings.get('createAccount'),
                    style: theme.typography.xl3,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  FTextFormField.email(
                    controller: _contactController,
                    label: Text(AppStrings.get('email')),
                    hint: AppStrings.get('pleaseEnterEmailPlaceholder'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (value) {
                      final trimmedValue = value?.trim() ?? '';
                      if (trimmedValue.isEmpty) {
                        return AppStrings.get('emailCannotBeEmpty');
                      }
                      final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
                      final emailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
                      if (!phoneRegex.hasMatch(trimmedValue) && !emailRegex.hasMatch(trimmedValue)) {
                        return AppStrings.get('pleaseEnterValidEmail');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),
                  FTextFormField(
                    controller: _codeController,
                    label: Text(AppStrings.get('verificationCode')),
                    hint: AppStrings.get('pleaseEnterVerificationCode'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    suffixBuilder: (context, style, child) => GestureDetector(
                      onTap: isSendingCode ? null : _onSendCodePressed,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          isSendingCode
                              ? AppStrings.get('sending')
                              : AppStrings.get('getVerificationCode'),
                          style: TextStyle(
                            color: isSendingCode
                                ? theme.colors.mutedForeground
                                : theme.colors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.get('verificationCodeCannotBeEmpty');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                  FButton(
                    onPress: isSendingCode ? null : _onNextPressed,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (isSendingCode) ...[
                          SizedBox.square(
                            dimension: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: theme.colors.primaryForeground,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        const Text('下一步'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}