import 'package:flutter_expense_tracker/core/network/exceptions/app_exception.dart';
import 'package:flutter_expense_tracker/features/auth/pages/register_step_1_page.dart';
import 'package:flutter_expense_tracker/features/auth/providers/auth_provider.dart';
import 'package:flutter_expense_tracker/features/auth/providers/verification_provider.dart'; // 导入新的验证码Provider
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import '/shared/l10n/app_strings.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false; // 本地加载状态

  // final bool _isLoading = false; // 这个变量不再需要，因为状态由 AuthState 管理

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _onLoginPressed() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    setState(() {
      _isLoading = true;
    });
    try {
      // 调用 Notifier 的 login 方法，它在失败时会 rethrow 异常
      await ref.read(authProvider.notifier).login(_emailController.text, _passwordController.text);
      // 如果 await 顺利通过，说明登录成功
      if (!mounted) return;

      // 显示成功提示
      showFToast(context: context, title: Text(AppStrings.get('loginSuccess')));

      // 延迟一下再跳转，确保状态更新完成
      await Future.delayed(const Duration(milliseconds: 100));
      if (!mounted) return;

      // 使用 pushReplacement 而不是 go，避免返回到登录页
      context.pushReplacement('/home');
    } on AppException catch (e) {
      if (!mounted) return;
      showFToast(
        context: context,
        title: Text(AppStrings.get('loginFailed')),
        description: Text(e.message), // 使用从异常中获取的清晰错误信息
      );
    } catch (e) {
      if (!mounted) return;
      showFToast(context: context, title: Text(AppStrings.get('unknownError')), description: Text(AppStrings.get('pleaseTryAgain')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: Form(
            key: _formKey,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
              Text(AppStrings.get('welcomeBack'), style: theme.typography.xl2, textAlign: TextAlign.center),
              const SizedBox(height: 8),
              Text(
                AppStrings.get('loginSubtitle'),
                style: theme.typography.sm.copyWith(color: theme.colors.mutedForeground),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
                  FTextFormField.email(
                    controller: _emailController,
                    label: Text(AppStrings.get('email')),
                    hint: AppStrings.get('emailPlaceholder'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.get('emailRequired');
                      }
                      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                        return AppStrings.get('emailInvalid');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),
                  FTextFormField.password(
                    controller: _passwordController,
                    label: Text(AppStrings.get('password')),
                    hint: AppStrings.get('passwordPlaceholder'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.get('passwordRequired');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                  FButton(
                    onPress: _isLoading ? null : _onLoginPressed,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isLoading) ...[
                          SizedBox.square(
                            dimension: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: theme.colors.primaryForeground,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Text(_isLoading ? AppStrings.get('loggingIn') : AppStrings.get('login')),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  FButton(
                    style: FButtonStyle.ghost(),
                    onPress: () {
                      // 重置验证码状态，避免下次进入注册流程时状态残留
                      ref.read(verificationProvider.notifier).reset();
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const RegisterStep1Page()),
                      );
                    },
                    child: Text(AppStrings.get('noAccount')),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
