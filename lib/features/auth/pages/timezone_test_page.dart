import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import '../../../core/services/timezone_service.dart';

/// 测试页面，用于验证时区功能
class TimezoneTestPage extends ConsumerStatefulWidget {
  const TimezoneTestPage({super.key});

  @override
  ConsumerState<TimezoneTestPage> createState() => _TimezoneTestPageState();
}

class _TimezoneTestPageState extends ConsumerState<TimezoneTestPage> {
  String? _currentTimezone;
  List<String> _availableTimezones = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTimezoneInfo();
  }

  Future<void> _loadTimezoneInfo() async {
    setState(() => _isLoading = true);
    
    try {
      final timezoneService = ref.read(timezoneServiceProvider);
      
      // 获取当前时区
      final currentTimezone = await timezoneService.getCurrentTimezone();
      
      // 获取可用时区列表（前20个用于展示）
      final allTimezones = await timezoneService.getAvailableTimezones();
      final limitedTimezones = allTimezones.take(20).toList();
      
      setState(() {
        _currentTimezone = currentTimezone;
        _availableTimezones = limitedTimezones;
      });
    } catch (e) {
      if (mounted) {
        showFToast(
          context: context,
          title: const Text('获取时区信息失败'),
          description: Text(e.toString()),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('时区测试'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前时区信息
            FCard(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前时区',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('正在获取时区信息...'),
                        ],
                      )
                    else if (_currentTimezone != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _currentTimezone!,
                            style: const TextStyle(
                              fontSize: 16,
                              fontFamily: 'monospace',
                              color: Colors.blue,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '当前时间: ${DateTime.now().toString()}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      )
                    else
                      const Text(
                        '无法获取时区信息',
                        style: TextStyle(color: Colors.red),
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 刷新按钮
            FButton(
              onPress: _isLoading ? null : _loadTimezoneInfo,
              child: const Text('刷新时区信息'),
            ),
            
            const SizedBox(height: 24),
            
            // 可用时区列表
            const Text(
              '部分可用时区 (前20个)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Expanded(
              child: _availableTimezones.isEmpty
                  ? const Center(
                      child: Text('暂无可用时区数据'),
                    )
                  : ListView.builder(
                      itemCount: _availableTimezones.length,
                      itemBuilder: (context, index) {
                        final timezone = _availableTimezones[index];
                        final isCurrent = timezone == _currentTimezone;
                        
                        return ListTile(
                          title: Text(
                            timezone,
                            style: TextStyle(
                              fontFamily: 'monospace',
                              color: isCurrent ? Colors.blue : null,
                              fontWeight: isCurrent ? FontWeight.bold : null,
                            ),
                          ),
                          trailing: isCurrent
                              ? const Icon(
                                  Icons.check_circle,
                                  color: Colors.blue,
                                )
                              : null,
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
