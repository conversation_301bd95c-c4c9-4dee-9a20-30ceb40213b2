import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import '../../../core/network/exceptions/app_exception.dart';
import '../providers/auth_provider.dart';
import '../providers/verification_provider.dart'; // 导入新的验证码Provider
import '/shared/l10n/app_strings.dart';

class RegisterStep2Page extends ConsumerStatefulWidget {
  final String contact;
  final String verificationCode;

  const RegisterStep2Page({
    super.key,
    required this.contact,
    required this.verificationCode,
  });

  @override
  ConsumerState<RegisterStep2Page> createState() => _RegisterStep2PageState();
}

class _RegisterStep2PageState extends ConsumerState<RegisterStep2Page> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  // 这些变量不再需要，因为 FTextFormField.password 内置了密码显示/隐藏功能
  bool _isLoading = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _onRegisterPressed() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    try {
      await ref
          .read(authProvider.notifier)
          .register(
            account: widget.contact,
            password: _passwordController.text,
            verificationCode: widget.verificationCode,
          );
      if (!mounted) return;

      // 显示成功提示
      showFToast(
        context: context,
        title: Text(AppStrings.get('registrationSuccess')),
      );

      // 清理验证码状态
      ref.read(verificationProvider.notifier).reset();

      // 延迟一下再跳转，确保状态更新完成
      await Future.delayed(const Duration(milliseconds: 100));
      if (!mounted) return;

      // 使用 pushReplacement 而不是 go，避免返回到注册页
      context.pushReplacement('/home');
    } on AppException catch (e) {
      if (!mounted) return;
      showFToast(
        context: context,
        title: Text(AppStrings.get('registrationFailed')),
        description: Text(e.message), // 现在可以正确显示后端消息
      );
    } catch (e) {
      if (!mounted) return;
      showFToast(
        context: context,
        title: const Text('发生未知错误'),
        description: Text(e.toString()),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Scaffold(
      appBar: AppBar(title: Text(AppStrings.get('setPassword'))),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    AppStrings.get('setAccountPassword'),
                    style: theme.typography.xl3,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  FTextFormField.password(
                    controller: _passwordController,
                    label: Text(AppStrings.get('password')),
                    hint: AppStrings.get('passwordPlaceholder'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.get('passwordCannotBeEmpty');
                      }
                      if (value.length < 6) {
                        return AppStrings.get('passwordTooShort');
                      }
                      // 密码强度校验：至少包含数字和字母
                      if (!value.contains(RegExp(r'[0-9]')) ||
                          !value.contains(RegExp(r'[a-zA-Z]'))) {
                        return AppStrings.get('passwordMustContainNumbersAndLetters');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 20),
                  FTextFormField.password(
                    controller: _confirmPasswordController,
                    label: Text(AppStrings.get('confirmPassword')),
                    hint: AppStrings.get('pleaseEnterPasswordAgain'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.get('pleaseEnterPasswordAgainShort');
                      }
                      if (value != _passwordController.text) {
                        return AppStrings.get('passwordsDoNotMatch');
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                  FButton(
                    onPress: _isLoading ? null : _onRegisterPressed,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isLoading) ...[
                          SizedBox.square(
                            dimension: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: theme.colors.primaryForeground,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Text(_isLoading ? AppStrings.get('registering') : AppStrings.get('completeRegistration')),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
