// features/auth/models/user.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
abstract class UserModel with _$UserModel {
  const factory UserModel({
    required String id, // 通常是UUID或数字字符串
    String? username,
    String? email,
    String? phone,
    String? avatarUrl,
    String? timezone, // 用户时区，例如: "Asia/Shanghai"
    // ... 其他你需要的用户字段
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}