import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/auth/providers/auth_provider.dart';

class AuthDebugPage extends ConsumerWidget {
  const AuthDebugPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('认证状态调试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前认证状态',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('状态: ${authState.status}'),
                    Text('用户: ${authState.user?.email ?? '无'}'),
                    Text('Token: ${authState.token != null ? '${authState.token!.substring(0, 10)}...' : '无'}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 调试按钮
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    await ref.read(authProvider.notifier).debugCheckToken();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('检查完成，请查看控制台日志')),
                    );
                  },
                  child: const Text('检查本地Token'),
                ),
                
                ElevatedButton(
                  onPressed: () async {
                    await ref.read(authProvider.notifier).checkAuthStatus();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('状态刷新完成')),
                    );
                  },
                  child: const Text('刷新认证状态'),
                ),
                
                ElevatedButton(
                  onPressed: () async {
                    await ref.read(authProvider.notifier).logout();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('已登出')),
                    );
                  },
                  child: const Text('强制登出'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 说明文字
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '调试说明：',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. 登录后，检查本地Token是否保存'),
                    Text('2. 关闭应用重新打开'),
                    Text('3. 查看控制台日志，确认初始化过程'),
                    Text('4. 如果状态没有恢复，点击"刷新认证状态"'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
