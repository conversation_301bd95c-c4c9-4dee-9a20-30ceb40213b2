# 手动记账功能 PRD

## 1. 概述

### 1.1 背景
当前应用主要通过AI聊天界面进行记账，但部分用户可能更习惯传统的手动记账方式。为满足这部分用户需求，我们计划在首页添加一个悬浮按钮，提供直观的手动记账入口。

### 1.2 目标
- 为不喜欢AI交互的用户提供传统记账方式
- 保持应用整体设计风格的一致性
- 提供简洁高效的记账流程
- 与现有功能无缝集成

### 1.3 用户价值
- 降低新用户使用门槛
- 提供多样化的记账方式选择
- 提高记账效率和用户满意度

## 2. 功能设计

### 2.1 悬浮按钮设计

#### 2.1.1 视觉设计
- **位置**: 首页右下角，考虑安全区域，避免与系统手势冲突
- **尺寸**: 56 x 56 dp，与应用现有FAB保持一致
- **形状**: 圆形，符合Material Design规范
- **颜色**: 使用应用主色调 #4F46E5 (深蓝色)
- **图标**: 使用 LucideIcons.plus 图标，大小为24dp
- **阴影**: 轻微的阴影效果，提升层次感
- **状态**: 支持按下状态视觉反馈

#### 2.1.2 交互设计
- **点击行为**: 展开速度拨号菜单(Speed Dial)，显示记账类型选项
- **展开动画**: 按钮旋转45度变为"X"，同时弹出选项菜单
- **触感反馈**: 点击时提供轻微的触感反馈
- **可访问性**: 支持TalkBack/VoiceOver朗读

#### 2.1.3 速度拨号菜单
- **选项列表**:
  1. 支出记录 (箭头向上图标)
  2. 收入记录 (箭头向下图标)
  3. 转账记录 (双向箭头图标)
- **视觉样式**:
  - 每个选项为小型圆形按钮(40dp)
  - 带有文字标签
  - 垂直排列，向上展开
  - 选项间距16dp
- **动画效果**:
  - 顺序弹出动画，每个选项延迟50ms
  - 使用animate_do库实现FadeInUp效果

### 2.2 记账表单设计

#### 2.2.1 通用表单结构
- **展示方式**: 底部弹出抽屉(BottomSheet)
- **高度**: 屏幕高度的75%，可拖动调整
- **标题栏**: 顶部显示操作类型("添加支出"/"添加收入"/"添加转账")
- **关闭按钮**: 右上角X按钮，点击关闭表单
- **提交按钮**: 底部全宽按钮，文字"保存"
- **键盘适配**: 表单滚动以适应键盘弹出

#### 2.2.2 支出/收入表单字段
- **金额输入**:
  - 大号字体显示
  - 数字键盘
  - 货币符号前缀
  - 自动格式化
- **日期选择**:
  - 默认为当前日期
  - 点击打开日期选择器
  - 显示格式: YYYY年MM月DD日
- **分类选择**:
  - 横向滚动的图标网格
  - 每个分类显示图标和名称
  - 支持自定义分类
- **商家名称**:
  - 单行文本输入
  - 可选填
  - 带有自动完成功能(基于历史输入)
- **备注**:
  - 多行文本输入
  - 可选填
  - 支持表情符号输入
- **参与人员**:
  - 可选填
  - 支持添加多个人员
  - 显示为标签样式
- **位置信息**:
  - 可选填
  - 支持地图选点或当前位置
- **照片附件**:
  - 可选填
  - 支持拍照或从相册选择
  - 显示缩略图预览

#### 2.2.3 转账表单字段
- **金额输入**: 同上
- **日期选择**: 同上
- **转出账户**:
  - 下拉选择
  - 显示账户名称和图标
- **转入账户**:
  - 下拉选择
  - 显示账户名称和图标
- **转账说明**:
  - 单行文本输入
  - 可选填

### 2.3 与现有功能集成

#### 2.3.1 数据同步
- 手动记账的数据与AI记账数据共享同一数据模型
- 记录会同时显示在交易流和足迹模块中
- 支持后续编辑和删除操作

#### 2.3.2 AI增强
- 提供选项让AI为手动记账生成叙事标题
- 自动推荐适合的心情表情
- 智能分类建议

## 3. 用户流程

### 3.1 基本流程
1. 用户点击首页右下角的"+"悬浮按钮
2. 速度拨号菜单展开，显示三个选项
3. 用户选择记账类型(支出/收入/转账)
4. 底部弹出相应的记账表单
5. 用户填写必要信息(金额、分类等)
6. 用户点击"保存"按钮
7. 显示成功提示，表单关闭
8. 新记录出现在交易流顶部

### 3.2 错误处理流程
1. 用户未填写必填字段时，禁用"保存"按钮
2. 输入无效数据时，显示相应错误提示
3. 保存失败时，显示错误信息并保留已填写内容

## 4. 技术实现

### 4.1 组件结构（已重构）
```
lib/features/home/
├── pages/
│   └── manual_entry_page.dart            # 统一的记账页面（支出/收入）
├── widgets/manual_entry/
│   ├── manual_entry_fab.dart             # 悬浮按钮
│   ├── custom_number_keyboard.dart       # 自定义数字键盘
│   └── tag_input_field.dart              # 标签输入组件
└── providers/
    └── manual_entry_provider.dart        # 记账状态管理
```

**注意**: 原有的sheet模式组件已被删除，现在使用统一的页面模式。

### 4.2 状态管理
- 使用Riverpod管理表单状态
- 创建专用的Provider管理记账表单状态
- 实现表单验证逻辑

### 4.3 数据模型
- 复用现有的TransactionModel
- 添加isManualEntry标志字段区分来源

### 4.4 动画实现
- 使用Flutter的AnimationController实现按钮旋转
- 使用animate_do库实现菜单项弹出动画
- 使用BottomSheet的内置动画效果

## 5. UI设计规范

### 5.1 颜色规范
- **主按钮**: #4F46E5 (深蓝色)
- **支出按钮**: #EF4444 (红色)
- **收入按钮**: #10B981 (绿色)
- **转账按钮**: #F59E0B (橙色)
- **背景色**: 使用应用现有背景色
- **文字颜色**: 使用应用现有文字颜色

### 5.2 字体规范
- **标题**: 18sp, 粗体
- **表单标签**: 14sp, 常规
- **金额输入**: 24sp, 粗体
- **按钮文字**: 16sp, 中等
- **辅助文字**: 12sp, 常规

### 5.3 间距规范
- **内边距**: 16dp
- **字段间距**: 20dp
- **分组间距**: 32dp
- **按钮高度**: 48dp
- **输入框高度**: 56dp

## 6. 测试计划

### 6.1 功能测试
- 验证悬浮按钮显示和点击响应
- 验证速度拨号菜单展开和选项点击
- 验证各表单字段的输入和验证
- 验证表单提交和数据保存
- 验证新记录在交易流中的显示

### 6.2 兼容性测试
- 不同屏幕尺寸适配测试
- 横竖屏切换测试
- 深色/浅色模式测试
- 不同系统版本测试

### 6.3 用户测试
- 邀请5-10名用户进行实际操作
- 收集用户反馈和使用体验
- 评估完成记账任务的时间和步骤数

## 7. 实施计划

### 7.1 开发阶段
1. **设计阶段** (1周)
   - UI设计稿定稿
   - 交互原型制作
   - 技术方案评审

2. **开发阶段** (2周)
   - 悬浮按钮和速度拨号菜单实现
   - 三种记账表单实现
   - 数据处理和状态管理实现

3. **测试阶段** (1周)
   - 功能测试
   - 兼容性测试
   - 用户测试

4. **发布阶段** (1周)
   - 修复测试中发现的问题
   - 文档完善
   - 版本发布

### 7.2 优先级
- **P0**: 悬浮按钮和基本支出记账功能
- **P1**: 收入和转账记账功能
- **P2**: AI增强功能
- **P3**: 高级字段(位置、照片等)

## 8. 成功指标

### 8.1 用户指标
- 手动记账功能使用率 > 30%
- 功能满意度评分 > 4.5/5
- 记账完成时间 < 30秒

### 8.2 技术指标
- 表单加载时间 < 300ms
- 表单提交响应时间 < 500ms
- 崩溃率 < 0.1%

## 9. 未来扩展

### 9.1 潜在功能扩展
- 语音输入记账
- 扫描发票自动填充
- 记账模板保存和复用
- 批量记账功能
- 定时提醒记账

### 9.2 长期规划
- 与预算功能集成
- 提供更多自定义选项
- 支持更复杂的分摊计算
- 导出记账数据功能

## 10. 附录

### 10.1 UI原型参考
![手动记账悬浮按钮](https://example.com/fab_mockup.png)
![支出记账表单](https://example.com/expense_form_mockup.png)

### 10.2 参考资料
- Material Design FAB指南
- Flutter BottomSheet文档
- 现有应用UI规范文档