import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// 字体配置类，用于管理应用的字体设置
/// 支持本地字体和Google Fonts，优先使用本地字体以避免网络问题
class FontConfig {
  // 本地字体族名称
  static const String _localNotoSansSC = 'NotoSansSC';
  static const String _localSourceHanSansSC = 'SourceHanSansSC';

  /// 获取适合中文显示的字体族名称
  static String getChineseFontFamily() {
    // 优先使用本地字体，避免网络下载问题
    return _localNotoSansSC;
  }

  /// 获取中文字体的回退列表
  static List<String> getChineseFontFallbacks() {
    return [
      _localNotoSansSC,        // 本地Noto Sans SC
      _localSourceHanSansSC,   // 本地思源黑体
      'PingFang SC',           // iOS系统字体
      'Microsoft YaHei',       // Windows系统字体
      'sans-serif',            // 系统默认
    ];
  }

  /// 创建中文友好的TextStyle
  static TextStyle createChineseTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: getChineseFontFamily(),
      fontFamilyFallback: getChineseFontFallbacks(),
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }



  /// 创建适合中文的MaterialApp TextTheme（使用本地字体）
  static TextTheme createChineseFriendlyMaterialTextTheme({
    required Brightness brightness,
  }) {
    final baseTheme = brightness == Brightness.light
        ? ThemeData.light().textTheme
        : ThemeData.dark().textTheme;

    // 为每个文本样式应用中文字体
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontFamily: getChineseFontFamily(),
        fontFamilyFallback: getChineseFontFallbacks(),
      ),
    );
  }

}
