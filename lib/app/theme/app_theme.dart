// 自定义 ThemeData（白/黑主题）

import 'package:flutter/material.dart';

final lightButtonStyle = getCustomButtonStyle(
  Colors.black,
  Colors.white,
  Colors.black,
);
final darkButtonStyle = getCustomButtonStyle(
  Colors.white,
  Colors.black,
  Colors.white,
);

final ThemeData darkTheme = ThemeData(
  splashFactory: NoSplash.splashFactory,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  brightness: Brightness.dark,
  scaffoldBackgroundColor: const Color(0xFF121212),
  appBarTheme: const AppBarTheme(
    elevation: 0,
    shadowColor: Colors.transparent,
    // backgroundColor: Color(0xFF1E1E1E),
    backgroundColor: Colors.black,
    foregroundColor: Colors.black,
  ),

  elevatedButtonTheme: ElevatedButtonThemeData(style: darkButtonStyle),
  outlinedButtonTheme: OutlinedButtonThemeData(style: darkButtonStyle),
  textButtonTheme: TextButtonThemeData(style: darkButtonStyle),

  cardTheme: const CardThemeData(elevation: 0, shadowColor: Colors.transparent),
  colorScheme: const ColorScheme.dark(
    primary: Colors.deepPurpleAccent,
    secondary: Colors.tealAccent,
  ),
);

final ThemeData lightTheme = ThemeData(
  splashFactory: NoSplash.splashFactory,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  brightness: Brightness.light,
  scaffoldBackgroundColor: Colors.white,
  appBarTheme: const AppBarTheme(
    elevation: 0,
    shadowColor: Colors.transparent,
    backgroundColor: Colors.white,
    foregroundColor: Colors.white,
  ),
  cardTheme: const CardThemeData(elevation: 0, shadowColor: Colors.transparent),
  elevatedButtonTheme: ElevatedButtonThemeData(style: lightButtonStyle),
  outlinedButtonTheme: OutlinedButtonThemeData(style: lightButtonStyle),
  textButtonTheme: TextButtonThemeData(style: lightButtonStyle),
  colorScheme: const ColorScheme.light(
    primary: Colors.black,
    secondary: Colors.grey,
  ),
  inputDecorationTheme: InputDecorationTheme(
    border: InputBorder.none,
    // 无边框
    enabledBorder: InputBorder.none,
    focusedBorder: InputBorder.none,
    errorBorder: InputBorder.none,
    disabledBorder: InputBorder.none,
    fillColor: Colors.transparent,
    // 背景透明
    filled: false,
    isDense: true,
    contentPadding: EdgeInsets.zero, // 去除内边距
  ),
  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // 让组件更紧凑（可选）
);

ButtonStyle getCustomButtonStyle(
  Color bgColor,
  Color fgColor,
  Color borderColor,
) {
  return ButtonStyle(
    backgroundColor: WidgetStateProperty.all(bgColor),
    foregroundColor: WidgetStateProperty.all(fgColor),
    side: WidgetStateProperty.all(BorderSide(color: borderColor)),
    elevation: WidgetStateProperty.all(0), // 去除阴影凹陷
    shape: WidgetStateProperty.all(
      RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // 调整这里的数值改变圆角大小
      ),
    ),
    // 取消按下时的阴影变化，保持扁平
    overlayColor: WidgetStateProperty.all(Colors.transparent),
  );
}
