import 'package:flutter/material.dart';

/// 针对中国大陆用户的字体配置
/// 提供多种字体获取方式，确保在网络受限环境下也能正常工作
class ChinaFontConfig {
  // 本地字体族名称
  static const String _localNotoSansSC = 'NotoSansSC';
  static const String _localSourceHanSansSC = 'SourceHanSansSC';
  
  /// 检查是否可以访问Google Fonts
  static Future<bool> _canAccessGoogleFonts() async {
    try {
      // 简单的网络检查，实际项目中可以更复杂
      return false; // 在中国大陆默认返回false
    } catch (e) {
      return false;
    }
  }

  /// 获取适合中文显示的字体族名称
  static String? getChineseFontFamily() {
    // 字体文件已下载，使用本地字体
    return _localNotoSansSC;
  }

  /// 获取字体回退列表
  static List<String> getFontFallbacks() {
    return [
      _localNotoSansSC,        // 本地Noto Sans SC
      _localSourceHanSansSC,   // 本地思源黑体
      'PingFang SC',           // iOS/macOS系统字体
      'Microsoft YaHei',       // Windows系统字体
      'Hiragino Sans GB',      // macOS中文字体
      'WenQuanYi Micro Hei',   // Linux中文字体
      'sans-serif',            // 系统默认
    ];
  }

  /// 创建中文友好的TextStyle
  static TextStyle createChineseTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return TextStyle(
      fontFamily: getChineseFontFamily(),
      fontFamilyFallback: getFontFallbacks(),
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      // 针对中文优化的字体渲染设置
      letterSpacing: 0.0,
    );
  }

  /// 获取适合 Forui 的中文字体族名称
  static String get foruiFontFamily => getChineseFontFamily() ?? 'NotoSansSC';

  /// 创建适合中文的MaterialApp TextTheme
  static TextTheme createChineseFriendlyMaterialTextTheme({
    required Brightness brightness,
  }) {
    final baseTheme = brightness == Brightness.light 
        ? ThemeData.light().textTheme 
        : ThemeData.dark().textTheme;

    final fontFamily = getChineseFontFamily();
    final fallbacks = getFontFallbacks();

    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontFamily: fontFamily,
        fontFamilyFallback: fallbacks,
      ),
    );
  }

  /// 获取字体信息（用于调试和显示）
  static Map<String, dynamic> getFontInfo() {
    return {
      'primaryFont': getChineseFontFamily(),
      'fallbackFonts': getFontFallbacks(),
      'isLocalFont': true,
      'description': '使用本地字体文件，避免网络依赖',
    };
  }
}
