// lib/app/theme/forui_theme_config.dart
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'china_font_config.dart';

/// Forui主题配置类
/// 提供亮色和暗色主题，并集成中国字体优化
class ForuiThemeConfig {
  /// 获取亮色主题
  static FThemeData get lightTheme {
    // 基于Forui的zinc light主题
    final baseTheme = FThemes.zinc.light;

    // 创建中国字体优化的Typography
    final typography = _createChineseFriendlyTypography(
      baseTypography: baseTheme.typography,
      brightness: Brightness.light,
    );

    return FThemeData(
      colors: baseTheme.colors,
      typography: typography,
      style: baseTheme.style,
    );
  }
  
  /// 获取暗色主题
  static FThemeData get darkTheme {
    // 基于Forui的zinc dark主题
    final baseTheme = FThemes.zinc.dark;

    // 创建中国字体优化的Typography
    final typography = _createChineseFriendlyTypography(
      baseTypography: baseTheme.typography,
      brightness: Brightness.dark,
    );

    return FThemeData(
      colors: baseTheme.colors,
      typography: typography,
      style: baseTheme.style,
    );
  }
  
  /// 创建中国字体友好的Typography
  static FTypography _createChineseFriendlyTypography({
    required FTypography baseTypography,
    required Brightness brightness,
  }) {
    // 获取中国字体配置
    final chineseFontFamily = ChinaFontConfig.foruiFontFamily;
    
    // 基于亮暗模式获取前景色
    final foregroundColor = brightness == Brightness.light 
        ? const Color(0xFF09090B)  // 亮色模式前景色
        : const Color(0xFFFAFAFA); // 暗色模式前景色
    
    return baseTypography.copyWith(
      xs: baseTypography.xs.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      sm: baseTypography.sm.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      base: baseTypography.base.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      lg: baseTypography.lg.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl: baseTypography.xl.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl2: baseTypography.xl2.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl3: baseTypography.xl3.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl4: baseTypography.xl4.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl5: baseTypography.xl5.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl6: baseTypography.xl6.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl7: baseTypography.xl7.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
      xl8: baseTypography.xl8.copyWith(
        fontFamily: chineseFontFamily,
        color: foregroundColor,
      ),
    );
  }
}

/// BuildContext扩展，方便访问Forui主题
extension ForuiThemeExtension on BuildContext {
  /// 获取当前Forui主题数据
  FThemeData get foruiTheme => FTheme.of(this);
  
  /// 获取当前Forui颜色方案
  FColors get foruiColors => FTheme.of(this).colors;
  
  /// 获取当前Forui字体样式
  FTypography get foruiTypography => FTheme.of(this).typography;
  
  /// 获取当前Forui样式配置
  FStyle get foruiStyle => FTheme.of(this).style;
}
