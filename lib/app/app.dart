// app.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';


// 假设这些是您项目中现有的文件路径
import '../app/router/app_router.dart';
import '../app/theme/theme_provider.dart'; // 包含您的 themeProvider (Riverpod)
import '../app/theme/china_font_config.dart'; // 中国字体配置
import '../app/theme/forui_theme_config.dart'; // Forui 主题配置
import '../features/auth/providers/auth_provider.dart';

// 2. 定义 Forui 的亮色和暗色主题 (可以根据您的设计进行定制)
// 您可以将这些主题数据移到您的主题管理文件中。
final foruiLightTheme = ForuiThemeConfig.lightTheme;

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final foruiDarkTheme = ForuiThemeConfig.darkTheme;

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 从 Riverpod 获取应用当前的主题模式
    final appThemeMode = ref.watch(themeProvider);

    // 根据主题模式选择对应的 Forui 主题
    final currentForuiTheme = appThemeMode == ThemeMode.dark
        ? foruiDarkTheme
        : foruiLightTheme;

    // 3. 使用正确的 Forui + Material 集成方式
    return MaterialApp.router(
      title: 'ai expense tracker',

      // 4. 使用 Forui 主题转换为 Material 主题，并添加中国字体支持
      theme: currentForuiTheme.toApproximateMaterialTheme().copyWith(
        textTheme: ChinaFontConfig.createChineseFriendlyMaterialTextTheme(
          brightness: Brightness.light,
        ),
      ),
      darkTheme: currentForuiTheme.toApproximateMaterialTheme().copyWith(
        textTheme: ChinaFontConfig.createChineseFriendlyMaterialTextTheme(
          brightness: Brightness.dark,
        ),
      ),
      themeMode: appThemeMode,
      localizationsDelegates: FLocalizations.localizationsDelegates,
      supportedLocales: FLocalizations.supportedLocales,
      debugShowCheckedModeBanner: false,
      routerConfig: ref.watch(appRouterProvider),




      // 5. 在 builder 中包装 FTheme
      builder: (materialContext, navigator) {
        return FTheme(
          data: currentForuiTheme,
          child: _buildAppContent(ref, navigator!),
        );
      },
    );
  }

  /// 构建应用内容，处理认证状态的初始化显示
  Widget _buildAppContent(WidgetRef ref, Widget navigator) {
    final authState = ref.watch(authProvider);

    // 如果认证状态正在初始化，显示启动页面
    if (authState.status == AuthStatus.loading || authState.status == AuthStatus.initial) {
      return const _SplashScreen();
    }

    // 认证状态已确定，显示正常的导航内容
    return navigator;
  }
}

/// 启动页面组件
class _SplashScreen extends StatelessWidget {
  const _SplashScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用Logo或图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(16)),
              child: const Icon(Icons.account_balance_wallet, size: 40, color: Colors.white),
            ),
            const SizedBox(height: 24),

            // 应用名称
            Text('AI费用追踪', style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),

            // 副标题
            Text('智能管理您的每一笔支出', style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).textTheme.bodySmall?.color)),
            const SizedBox(height: 32),

            // 加载指示器
            const SizedBox(width: 32, height: 32, child: CircularProgressIndicator(strokeWidth: 3)),
            const SizedBox(height: 16),

            // 加载文本
            Text('正在初始化...', style: Theme.of(context).textTheme.bodySmall),
          ],
        ),
      ),
    );
  }
}
