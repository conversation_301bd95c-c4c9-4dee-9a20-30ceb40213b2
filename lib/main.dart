// main.dart
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_expense_tracker/app/app.dart';
import 'package:flutter_expense_tracker/features/auth/providers/auth_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'shared/services/locale_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 初始化语言服务
    await LocaleService.initialize();
    log('Main: 语言服务初始化完成');

    // 初始化中文日期格式数据
    await initializeDateFormatting('zh_CN', null);
    log('Main: 日期格式初始化完成');

    // 这里可以添加其他初始化逻辑
    // 例如：Firebase初始化、崩溃报告等
    // await Firebase.initializeApp();
    // await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

    log('Main: 应用初始化完成，启动应用');
  } catch (e, stackTrace) {
    log('Main: 初始化过程中发生错误: $e', stackTrace: stackTrace);
    // 即使初始化失败，也要启动应用
  }

  // 创建ProviderContainer来预热认证状态
  final container = ProviderContainer();

  try {
    // 预热authProvider，触发状态初始化
    log('Main: 开始预热认证状态...');
    container.read(authProvider);
    log('Main: 认证状态预热完成');
  } catch (e) {
    log('Main: 认证状态预热失败: $e');
  }

  runApp(UncontrolledProviderScope(container: container, child: const MyApp()));
}
