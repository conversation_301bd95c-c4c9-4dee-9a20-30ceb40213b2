// lib/core/network/network_status_provider.dart
import 'dart:async';
import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'network_diagnostics.dart';

/// 网络状态提供者
/// 用于监控和管理应用的网络连接状态
class NetworkStatusNotifier extends StateNotifier<NetworkStatus> {
  NetworkStatusNotifier() : super(NetworkStatus.unknown) {
    _startPeriodicCheck();
  }

  Timer? _periodicTimer;
  static const Duration _checkInterval = Duration(minutes: 1);

  /// 开始定期检查网络状态
  void _startPeriodicCheck() {
    _periodicTimer = Timer.periodic(_checkInterval, (_) {
      checkNetworkStatus();
    });
    
    // 立即执行一次检查
    checkNetworkStatus();
  }

  /// 检查网络状态
  Future<void> checkNetworkStatus() async {
    try {
      state = NetworkStatus.checking;
      
      final diagnostic = await NetworkDiagnostics.runFullDiagnostic();
      
      if (diagnostic.isNetworkOk) {
        state = NetworkStatus.connected;
      } else if (diagnostic.internetAvailable) {
        state = NetworkStatus.serverUnavailable;
      } else {
        state = NetworkStatus.disconnected;
      }
      
      log('NetworkStatusProvider: Status updated to ${state}');
    } catch (e) {
      log('NetworkStatusProvider: Error checking network status: $e');
      state = NetworkStatus.error;
    }
  }

  /// 手动刷新网络状态
  Future<void> refresh() async {
    await checkNetworkStatus();
  }

  @override
  void dispose() {
    _periodicTimer?.cancel();
    super.dispose();
  }
}

/// 网络状态枚举
enum NetworkStatus {
  unknown,           // 未知状态
  checking,          // 正在检查
  connected,         // 已连接且服务器可用
  disconnected,      // 网络断开
  serverUnavailable, // 网络连接正常但服务器不可用
  error,             // 检查过程中出错
}

/// 网络状态扩展方法
extension NetworkStatusExtension on NetworkStatus {
  /// 是否可以进行网络请求
  bool get canMakeRequests => this == NetworkStatus.connected;
  
  /// 获取状态描述
  String get description {
    switch (this) {
      case NetworkStatus.unknown:
        return '网络状态未知';
      case NetworkStatus.checking:
        return '正在检查网络状态...';
      case NetworkStatus.connected:
        return '网络连接正常';
      case NetworkStatus.disconnected:
        return '网络连接断开';
      case NetworkStatus.serverUnavailable:
        return '服务器暂时不可用';
      case NetworkStatus.error:
        return '网络状态检查失败';
    }
  }
  
  /// 获取用户友好的建议
  String get userAdvice {
    switch (this) {
      case NetworkStatus.unknown:
      case NetworkStatus.checking:
        return '请稍候...';
      case NetworkStatus.connected:
        return '一切正常';
      case NetworkStatus.disconnected:
        return '请检查您的网络连接';
      case NetworkStatus.serverUnavailable:
        return '服务器维护中，请稍后重试';
      case NetworkStatus.error:
        return '请检查网络设置或重启应用';
    }
  }
  
  /// 是否显示为错误状态
  bool get isError => this == NetworkStatus.disconnected || 
                     this == NetworkStatus.serverUnavailable || 
                     this == NetworkStatus.error;
}

/// 网络状态提供者
final networkStatusProvider = StateNotifierProvider<NetworkStatusNotifier, NetworkStatus>((ref) {
  return NetworkStatusNotifier();
});

/// 网络连接状态提供者（简化版本）
final isNetworkConnectedProvider = Provider<bool>((ref) {
  final status = ref.watch(networkStatusProvider);
  return status.canMakeRequests;
});

/// 网络状态描述提供者
final networkStatusDescriptionProvider = Provider<String>((ref) {
  final status = ref.watch(networkStatusProvider);
  return status.description;
});

/// 网络建议提供者
final networkAdviceProvider = Provider<String>((ref) {
  final status = ref.watch(networkStatusProvider);
  return status.userAdvice;
});
