// core/network/network_client.dart (新文件或修改现有DioClient)
import 'dart:developer';
import 'package:dio/dio.dart';
import 'dio_provider.dart';
import 'exceptions/app_exception.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'interceptors/business_interceptor.dart';
import 'network_diagnostics.dart';

// 定义 HTTP 方法枚举，方便类型安全
enum HttpMethod { get, post, put, delete, patch }

class NetworkClient {
  final Dio _dio;

  NetworkClient(this._dio);

  /// 通用的网络请求方法
  /// - [path]: API的相对路径
  /// - [method]: HTTP方法 (GET, POST, etc.)
  /// - [queryParameters]: URL查询参数
  /// - [data]: 请求体 (用于POST, PUT, PATCH)
  /// - [fromJsonT]: 用于将响应的data部分转换为期望的泛型类型T的回调函数
  /// - [options]: 可选的Dio Options，用于覆盖默认设置或传递额外信息（如拦截器需要的extra）
  /// - [enableRetry]: 是否启用重试机制，默认为true
  /// - [maxRetries]: 最大重试次数，默认为3
  Future<T> request<T>(
    String path, {
    required HttpMethod method,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? data,
    FromJsonT<T>? fromJsonT, // T Function(Object? json)
    Options? options,
    CancelToken? cancelToken,
    bool enableRetry = true,
    int maxRetries = 3,
  }) async {
    return await _executeWithRetry<T>(
      () async {
        // 准备 Options
        final requestOptions = (options ?? Options()).copyWith(
          method: method.toString().split('.').last, // 将枚举转为 "GET", "POST" 等字符串
        );

        // 执行请求
        final Response response = await _dio.request(path, data: data, queryParameters: queryParameters, cancelToken: cancelToken, options: requestOptions);

        return response;
      },
      fromJsonT: fromJsonT,
      enableRetry: enableRetry,
      maxRetries: maxRetries,
      path: path,
    );
  }

  /// 带重试机制的请求执行器
  Future<T> _executeWithRetry<T>(
    Future<Response> Function() requestFunction, {
    FromJsonT<T>? fromJsonT,
    required bool enableRetry,
    required int maxRetries,
    required String path,
  }) async {
    int attempts = 0;
    DioException? lastException;

    while (attempts <= maxRetries) {
      try {
        final response = await requestFunction();

        // 在这里执行最终的类型转换
        // BusinessInterceptor 已经确保 response.data 是成功响应中的 'data' 字段
        if (fromJsonT != null) {
          try {
            return fromJsonT(response.data);
          } catch (e) {
            log("NetworkClient: fromJsonT 解析失败: $e");
            // 抛出特定的数据解析异常
            throw DataParsingException("客户端数据解析失败: ${e.toString()}");
          }
        } else {
          // 如果没有提供解析器，我们假设调用者期望原始数据
          // 并进行一次安全的类型检查
          if (response.data is T) {
            return response.data;
          } else {
            // 如果类型不匹配，这也是一种解析错误
            throw DataParsingException("响应数据类型与期望类型不匹配。收到: ${response.data.runtimeType}, 期望: $T");
          }
        }
      } on DioException catch (e) {
        lastException = e;
        attempts++;

        // 检查是否应该重试
        if (!enableRetry || attempts > maxRetries || !_shouldRetry(e)) {
          break;
        }

        log("NetworkClient: 请求失败，准备重试 (${attempts}/${maxRetries}): ${e.message}");

        // 在重试前等待一段时间（指数退避）
        await Future.delayed(Duration(milliseconds: 500 * attempts));
        continue;
      } catch (e) {
        if (e is AppException) {
          rethrow; // 如果已经是 AppException，直接重新抛出
        }
        log("NetworkClient: 捕获到未知错误: $e");
        throw Exception("客户端发生意外错误: ${e.toString()}"); // 包装为通用 Exception
      }
    }

    // 如果所有重试都失败了，处理最后的异常
    if (lastException != null) {
      // 在最终失败前，运行网络诊断
      await _handleFinalFailure(lastException, path);

      // ErrorInterceptor 应该已经将 e.error 字段填充为 AppException 的子类
      if (lastException.error is AppException) {
        throw lastException.error as AppException; // 直接抛出拦截器转换后的 AppException
      }
      log("NetworkClient: 所有重试失败，最终错误: ${lastException.type}, ${lastException.message}");
      throw NetworkException("网络请求失败，已重试${maxRetries}次: ${lastException.message ?? '未知网络错误'}");
    }

    throw NetworkException("请求执行异常，未知错误");
  }

  /// 判断是否应该重试请求
  bool _shouldRetry(DioException e) {
    // 只对特定类型的错误进行重试
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.connectionError:
        return true;
      case DioExceptionType.badResponse:
        // 对于5xx服务器错误，可以重试
        final statusCode = e.response?.statusCode;
        return statusCode != null && statusCode >= 500;
      default:
        return false;
    }
  }

  /// 处理最终失败，运行网络诊断
  Future<void> _handleFinalFailure(DioException e, String path) async {
    try {
      log("NetworkClient: 运行网络诊断，路径: $path");
      final diagnostic = await NetworkDiagnostics.runFullDiagnostic();
      final advice = NetworkDiagnostics.getNetworkAdvice(diagnostic);
      log("NetworkClient: 网络诊断结果: $diagnostic");
      log("NetworkClient: 网络建议: $advice");
    } catch (diagnosticError) {
      log("NetworkClient: 网络诊断失败: $diagnosticError");
    }
  }

  Future<Map<String, dynamic>> requestMap(
    String path, {
    required HttpMethod method,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    Options? options,
    bool enableRetry = true,
    int maxRetries = 3,
  }) {
    return request<Map<String, dynamic>>(
      path,
      method: method,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      options: options,
      enableRetry: enableRetry,
      maxRetries: maxRetries,
      fromJsonT: (json) => json as Map<String, dynamic>,
    );
  }
}

// Riverpod Provider for NetworkClient
final networkClientProvider = Provider<NetworkClient>((ref) {
  final dio = ref.watch(dioProvider);
  return NetworkClient(dio);
});
