// lib/core/network/network_diagnostics.dart
import 'dart:io';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../constants/api_constants.dart';

/// 网络诊断工具类
/// 用于检测网络连接状态和服务器可达性
class NetworkDiagnostics {
  static const String _tag = 'NetworkDiagnostics';

  /// 检查网络连接状态
  static Future<bool> checkInternetConnection() async {
    try {
      // 尝试连接到可靠的外部服务器
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        log('$_tag: Internet connection available');
        return true;
      }
    } catch (e) {
      log('$_tag: No internet connection: $e');
    }
    return false;
  }

  /// 检查服务器可达性
  static Future<ServerStatus> checkServerStatus() async {
    try {
      // 提取服务器地址和端口
      final uri = Uri.parse(ApiConstants.baseUrl);
      final host = uri.host;
      final port = uri.port;

      log('$_tag: Checking server connectivity to $host:$port');

      // 尝试建立TCP连接
      final socket = await Socket.connect(
        host,
        port,
        timeout: const Duration(seconds: 5),
      );
      
      await socket.close();
      log('$_tag: Server is reachable');
      
      // 进一步检查HTTP响应
      return await _checkHttpHealth();
    } catch (e) {
      log('$_tag: Server unreachable: $e');
      return ServerStatus.unreachable;
    }
  }

  /// 检查HTTP健康状态
  static Future<ServerStatus> _checkHttpHealth() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 5);
      dio.options.receiveTimeout = const Duration(seconds: 10);
      
      // 尝试访问健康检查端点或基础端点
      final response = await dio.get('${ApiConstants.baseUrl}/health');
      
      if (response.statusCode == 200) {
        log('$_tag: Server HTTP health check passed');
        return ServerStatus.healthy;
      } else {
        log('$_tag: Server responded with status: ${response.statusCode}');
        return ServerStatus.unhealthy;
      }
    } catch (e) {
      log('$_tag: HTTP health check failed: $e');
      // 如果没有健康检查端点，尝试任意端点
      return await _checkAnyEndpoint();
    }
  }

  /// 检查任意端点响应
  static Future<ServerStatus> _checkAnyEndpoint() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 5);
      dio.options.receiveTimeout = const Duration(seconds: 10);
      
      // 尝试访问一个简单的端点
      await dio.get(ApiConstants.baseUrl);
      
      log('$_tag: Server base URL accessible');
      return ServerStatus.reachable;
    } catch (e) {
      if (e is DioException) {
        // 如果能收到HTTP错误响应，说明服务器是可达的
        if (e.response != null) {
          log('$_tag: Server reachable but returned error: ${e.response?.statusCode}');
          return ServerStatus.reachable;
        }
      }
      log('$_tag: Server completely unreachable: $e');
      return ServerStatus.unreachable;
    }
  }

  /// 执行完整的网络诊断
  static Future<NetworkDiagnosticResult> runFullDiagnostic() async {
    log('$_tag: Starting full network diagnostic...');
    
    final internetAvailable = await checkInternetConnection();
    final serverStatus = await checkServerStatus();
    
    final result = NetworkDiagnosticResult(
      internetAvailable: internetAvailable,
      serverStatus: serverStatus,
      timestamp: DateTime.now(),
    );
    
    log('$_tag: Diagnostic complete - Internet: $internetAvailable, Server: $serverStatus');
    return result;
  }

  /// 获取网络建议
  static String getNetworkAdvice(NetworkDiagnosticResult result) {
    if (!result.internetAvailable) {
      return '请检查您的网络连接，确保设备已连接到互联网';
    }
    
    switch (result.serverStatus) {
      case ServerStatus.healthy:
        return '网络连接正常';
      case ServerStatus.reachable:
        return '服务器可达，但可能存在服务问题';
      case ServerStatus.unhealthy:
        return '服务器响应异常，请稍后重试';
      case ServerStatus.unreachable:
        return '无法连接到服务器，请检查服务器状态或稍后重试';
    }
  }
}

/// 服务器状态枚举
enum ServerStatus {
  healthy,    // 服务器健康
  reachable,  // 服务器可达但状态未知
  unhealthy,  // 服务器可达但不健康
  unreachable // 服务器不可达
}

/// 网络诊断结果
class NetworkDiagnosticResult {
  final bool internetAvailable;
  final ServerStatus serverStatus;
  final DateTime timestamp;

  NetworkDiagnosticResult({
    required this.internetAvailable,
    required this.serverStatus,
    required this.timestamp,
  });

  bool get isNetworkOk => internetAvailable && 
    (serverStatus == ServerStatus.healthy || serverStatus == ServerStatus.reachable);

  @override
  String toString() {
    return 'NetworkDiagnosticResult(internet: $internetAvailable, server: $serverStatus, time: $timestamp)';
  }
}
